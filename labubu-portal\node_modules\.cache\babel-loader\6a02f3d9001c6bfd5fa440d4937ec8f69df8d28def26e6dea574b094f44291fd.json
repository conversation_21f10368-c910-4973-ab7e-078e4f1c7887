{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createGraphFromNodeEdge from '../helper/createGraphFromNodeEdge.js';\nimport Model from '../../model/Model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar SankeySeriesModel = /** @class */function (_super) {\n  __extends(SankeySeriesModel, _super);\n  function SankeySeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SankeySeriesModel.type;\n    return _this;\n  }\n  /**\r\n   * Init a graph data structure from data in option series\r\n   */\n  SankeySeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var links = option.edges || option.links || [];\n    var nodes = option.data || option.nodes || [];\n    var levels = option.levels || [];\n    this.levelModels = [];\n    var levelModels = this.levelModels;\n    for (var i = 0; i < levels.length; i++) {\n      if (levels[i].depth != null && levels[i].depth >= 0) {\n        levelModels[levels[i].depth] = new Model(levels[i], this, ecModel);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          throw new Error('levels[i].depth is mandatory and should be natural number');\n        }\n      }\n    }\n    var graph = createGraphFromNodeEdge(nodes, links, this, true, beforeLink);\n    return graph.data;\n    function beforeLink(nodeData, edgeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var seriesModel = model.parentModel;\n        var layout = seriesModel.getData().getItemLayout(idx);\n        if (layout) {\n          var nodeDepth = layout.depth;\n          var levelModel = seriesModel.levelModels[nodeDepth];\n          if (levelModel) {\n            model.parentModel = levelModel;\n          }\n        }\n        return model;\n      });\n      edgeData.wrapMethod('getItemModel', function (model, idx) {\n        var seriesModel = model.parentModel;\n        var edge = seriesModel.getGraph().getEdgeByIndex(idx);\n        var layout = edge.node1.getLayout();\n        if (layout) {\n          var depth = layout.depth;\n          var levelModel = seriesModel.levelModels[depth];\n          if (levelModel) {\n            model.parentModel = levelModel;\n          }\n        }\n        return model;\n      });\n    }\n  };\n  SankeySeriesModel.prototype.setNodePosition = function (dataIndex, localPosition) {\n    var nodes = this.option.data || this.option.nodes;\n    var dataItem = nodes[dataIndex];\n    dataItem.localX = localPosition[0];\n    dataItem.localY = localPosition[1];\n  };\n  /**\r\n   * Return the graphic data structure\r\n   *\r\n   * @return graphic data structure\r\n   */\n  SankeySeriesModel.prototype.getGraph = function () {\n    return this.getData().graph;\n  };\n  /**\r\n   * Get edge data of graphic data structure\r\n   *\r\n   * @return data structure of list\r\n   */\n  SankeySeriesModel.prototype.getEdgeData = function () {\n    return this.getGraph().edgeData;\n  };\n  SankeySeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    function noValue(val) {\n      return isNaN(val) || val == null;\n    }\n    // dataType === 'node' or empty do not show tooltip by default\n    if (dataType === 'edge') {\n      var params = this.getDataParams(dataIndex, dataType);\n      var rawDataOpt = params.data;\n      var edgeValue = params.value;\n      var edgeName = rawDataOpt.source + ' -- ' + rawDataOpt.target;\n      return createTooltipMarkup('nameValue', {\n        name: edgeName,\n        value: edgeValue,\n        noValue: noValue(edgeValue)\n      });\n    }\n    // dataType === 'node'\n    else {\n      var node = this.getGraph().getNodeByIndex(dataIndex);\n      var value = node.getLayout().value;\n      var name_1 = this.getDataParams(dataIndex, dataType).data.name;\n      return createTooltipMarkup('nameValue', {\n        name: name_1 != null ? name_1 + '' : null,\n        value: value,\n        noValue: noValue(value)\n      });\n    }\n  };\n  SankeySeriesModel.prototype.optionUpdated = function () {};\n  // Override Series.getDataParams()\n  SankeySeriesModel.prototype.getDataParams = function (dataIndex, dataType) {\n    var params = _super.prototype.getDataParams.call(this, dataIndex, dataType);\n    if (params.value == null && dataType === 'node') {\n      var node = this.getGraph().getNodeByIndex(dataIndex);\n      var nodeValue = node.getLayout().value;\n      params.value = nodeValue;\n    }\n    return params;\n  };\n  SankeySeriesModel.type = 'series.sankey';\n  SankeySeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'view',\n    left: '5%',\n    top: '5%',\n    right: '20%',\n    bottom: '5%',\n    orient: 'horizontal',\n    nodeWidth: 20,\n    nodeGap: 8,\n    draggable: true,\n    layoutIterations: 32,\n    label: {\n      show: true,\n      position: 'right',\n      fontSize: 12\n    },\n    edgeLabel: {\n      show: false,\n      fontSize: 12\n    },\n    levels: [],\n    nodeAlign: 'justify',\n    lineStyle: {\n      color: '#314656',\n      opacity: 0.2,\n      curveness: 0.5\n    },\n    emphasis: {\n      label: {\n        show: true\n      },\n      lineStyle: {\n        opacity: 0.5\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    animationEasing: 'linear',\n    animationDuration: 1000\n  };\n  return SankeySeriesModel;\n}(SeriesModel);\nexport default SankeySeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "createGraphFromNodeEdge", "Model", "createTooltipMarkup", "SankeySeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "getInitialData", "option", "ecModel", "links", "edges", "nodes", "data", "levels", "levelModels", "i", "length", "depth", "process", "env", "NODE_ENV", "Error", "graph", "beforeLink", "nodeData", "edgeData", "wrapMethod", "model", "idx", "seriesModel", "parentModel", "layout", "getData", "getItemLayout", "nodeDepth", "levelModel", "edge", "getGraph", "getEdgeByIndex", "node1", "getLayout", "setNodePosition", "dataIndex", "localPosition", "dataItem", "localX", "localY", "getEdgeData", "formatTooltip", "multipleSeries", "dataType", "noValue", "val", "isNaN", "params", "getDataParams", "rawDataOpt", "edgeValue", "value", "edgeName", "source", "target", "name", "node", "getNodeByIndex", "name_1", "optionUpdated", "call", "nodeValue", "defaultOption", "z", "coordinateSystem", "left", "top", "right", "bottom", "orient", "nodeWidth", "nodeGap", "draggable", "layoutIterations", "label", "show", "position", "fontSize", "edgeLabel", "nodeAlign", "lineStyle", "color", "opacity", "curveness", "emphasis", "select", "itemStyle", "borderColor", "animationEasing", "animationDuration"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/sankey/SankeySeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createGraphFromNodeEdge from '../helper/createGraphFromNodeEdge.js';\nimport Model from '../../model/Model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nvar SankeySeriesModel = /** @class */function (_super) {\n  __extends(SankeySeriesModel, _super);\n  function SankeySeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SankeySeriesModel.type;\n    return _this;\n  }\n  /**\r\n   * Init a graph data structure from data in option series\r\n   */\n  SankeySeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var links = option.edges || option.links || [];\n    var nodes = option.data || option.nodes || [];\n    var levels = option.levels || [];\n    this.levelModels = [];\n    var levelModels = this.levelModels;\n    for (var i = 0; i < levels.length; i++) {\n      if (levels[i].depth != null && levels[i].depth >= 0) {\n        levelModels[levels[i].depth] = new Model(levels[i], this, ecModel);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          throw new Error('levels[i].depth is mandatory and should be natural number');\n        }\n      }\n    }\n    var graph = createGraphFromNodeEdge(nodes, links, this, true, beforeLink);\n    return graph.data;\n    function beforeLink(nodeData, edgeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var seriesModel = model.parentModel;\n        var layout = seriesModel.getData().getItemLayout(idx);\n        if (layout) {\n          var nodeDepth = layout.depth;\n          var levelModel = seriesModel.levelModels[nodeDepth];\n          if (levelModel) {\n            model.parentModel = levelModel;\n          }\n        }\n        return model;\n      });\n      edgeData.wrapMethod('getItemModel', function (model, idx) {\n        var seriesModel = model.parentModel;\n        var edge = seriesModel.getGraph().getEdgeByIndex(idx);\n        var layout = edge.node1.getLayout();\n        if (layout) {\n          var depth = layout.depth;\n          var levelModel = seriesModel.levelModels[depth];\n          if (levelModel) {\n            model.parentModel = levelModel;\n          }\n        }\n        return model;\n      });\n    }\n  };\n  SankeySeriesModel.prototype.setNodePosition = function (dataIndex, localPosition) {\n    var nodes = this.option.data || this.option.nodes;\n    var dataItem = nodes[dataIndex];\n    dataItem.localX = localPosition[0];\n    dataItem.localY = localPosition[1];\n  };\n  /**\r\n   * Return the graphic data structure\r\n   *\r\n   * @return graphic data structure\r\n   */\n  SankeySeriesModel.prototype.getGraph = function () {\n    return this.getData().graph;\n  };\n  /**\r\n   * Get edge data of graphic data structure\r\n   *\r\n   * @return data structure of list\r\n   */\n  SankeySeriesModel.prototype.getEdgeData = function () {\n    return this.getGraph().edgeData;\n  };\n  SankeySeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    function noValue(val) {\n      return isNaN(val) || val == null;\n    }\n    // dataType === 'node' or empty do not show tooltip by default\n    if (dataType === 'edge') {\n      var params = this.getDataParams(dataIndex, dataType);\n      var rawDataOpt = params.data;\n      var edgeValue = params.value;\n      var edgeName = rawDataOpt.source + ' -- ' + rawDataOpt.target;\n      return createTooltipMarkup('nameValue', {\n        name: edgeName,\n        value: edgeValue,\n        noValue: noValue(edgeValue)\n      });\n    }\n    // dataType === 'node'\n    else {\n      var node = this.getGraph().getNodeByIndex(dataIndex);\n      var value = node.getLayout().value;\n      var name_1 = this.getDataParams(dataIndex, dataType).data.name;\n      return createTooltipMarkup('nameValue', {\n        name: name_1 != null ? name_1 + '' : null,\n        value: value,\n        noValue: noValue(value)\n      });\n    }\n  };\n  SankeySeriesModel.prototype.optionUpdated = function () {};\n  // Override Series.getDataParams()\n  SankeySeriesModel.prototype.getDataParams = function (dataIndex, dataType) {\n    var params = _super.prototype.getDataParams.call(this, dataIndex, dataType);\n    if (params.value == null && dataType === 'node') {\n      var node = this.getGraph().getNodeByIndex(dataIndex);\n      var nodeValue = node.getLayout().value;\n      params.value = nodeValue;\n    }\n    return params;\n  };\n  SankeySeriesModel.type = 'series.sankey';\n  SankeySeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'view',\n    left: '5%',\n    top: '5%',\n    right: '20%',\n    bottom: '5%',\n    orient: 'horizontal',\n    nodeWidth: 20,\n    nodeGap: 8,\n    draggable: true,\n    layoutIterations: 32,\n    label: {\n      show: true,\n      position: 'right',\n      fontSize: 12\n    },\n    edgeLabel: {\n      show: false,\n      fontSize: 12\n    },\n    levels: [],\n    nodeAlign: 'justify',\n    lineStyle: {\n      color: '#314656',\n      opacity: 0.2,\n      curveness: 0.5\n    },\n    emphasis: {\n      label: {\n        show: true\n      },\n      lineStyle: {\n        opacity: 0.5\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    animationEasing: 'linear',\n    animationDuration: 1000\n  };\n  return SankeySeriesModel;\n}(SeriesModel);\nexport default SankeySeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,IAAIC,iBAAiB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACrDN,SAAS,CAACK,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,iBAAiB,CAACK,IAAI;IACnC,OAAOH,KAAK;EACd;EACA;AACF;AACA;EACEF,iBAAiB,CAACM,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACtE,IAAIC,KAAK,GAAGF,MAAM,CAACG,KAAK,IAAIH,MAAM,CAACE,KAAK,IAAI,EAAE;IAC9C,IAAIE,KAAK,GAAGJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACI,KAAK,IAAI,EAAE;IAC7C,IAAIE,MAAM,GAAGN,MAAM,CAACM,MAAM,IAAI,EAAE;IAChC,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAIA,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIF,MAAM,CAACE,CAAC,CAAC,CAACE,KAAK,IAAI,IAAI,IAAIJ,MAAM,CAACE,CAAC,CAAC,CAACE,KAAK,IAAI,CAAC,EAAE;QACnDH,WAAW,CAACD,MAAM,CAACE,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,IAAIpB,KAAK,CAACgB,MAAM,CAACE,CAAC,CAAC,EAAE,IAAI,EAAEP,OAAO,CAAC;MACpE,CAAC,MAAM;QACL,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,MAAM,IAAIC,KAAK,CAAC,2DAA2D,CAAC;QAC9E;MACF;IACF;IACA,IAAIC,KAAK,GAAG1B,uBAAuB,CAACe,KAAK,EAAEF,KAAK,EAAE,IAAI,EAAE,IAAI,EAAEc,UAAU,CAAC;IACzE,OAAOD,KAAK,CAACV,IAAI;IACjB,SAASW,UAAUA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;MACtCD,QAAQ,CAACE,UAAU,CAAC,cAAc,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;QACxD,IAAIC,WAAW,GAAGF,KAAK,CAACG,WAAW;QACnC,IAAIC,MAAM,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC,CAACC,aAAa,CAACL,GAAG,CAAC;QACrD,IAAIG,MAAM,EAAE;UACV,IAAIG,SAAS,GAAGH,MAAM,CAACd,KAAK;UAC5B,IAAIkB,UAAU,GAAGN,WAAW,CAACf,WAAW,CAACoB,SAAS,CAAC;UACnD,IAAIC,UAAU,EAAE;YACdR,KAAK,CAACG,WAAW,GAAGK,UAAU;UAChC;QACF;QACA,OAAOR,KAAK;MACd,CAAC,CAAC;MACFF,QAAQ,CAACC,UAAU,CAAC,cAAc,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;QACxD,IAAIC,WAAW,GAAGF,KAAK,CAACG,WAAW;QACnC,IAAIM,IAAI,GAAGP,WAAW,CAACQ,QAAQ,CAAC,CAAC,CAACC,cAAc,CAACV,GAAG,CAAC;QACrD,IAAIG,MAAM,GAAGK,IAAI,CAACG,KAAK,CAACC,SAAS,CAAC,CAAC;QACnC,IAAIT,MAAM,EAAE;UACV,IAAId,KAAK,GAAGc,MAAM,CAACd,KAAK;UACxB,IAAIkB,UAAU,GAAGN,WAAW,CAACf,WAAW,CAACG,KAAK,CAAC;UAC/C,IAAIkB,UAAU,EAAE;YACdR,KAAK,CAACG,WAAW,GAAGK,UAAU;UAChC;QACF;QACA,OAAOR,KAAK;MACd,CAAC,CAAC;IACJ;EACF,CAAC;EACD5B,iBAAiB,CAACM,SAAS,CAACoC,eAAe,GAAG,UAAUC,SAAS,EAAEC,aAAa,EAAE;IAChF,IAAIhC,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAI,IAAI,CAACL,MAAM,CAACI,KAAK;IACjD,IAAIiC,QAAQ,GAAGjC,KAAK,CAAC+B,SAAS,CAAC;IAC/BE,QAAQ,CAACC,MAAM,GAAGF,aAAa,CAAC,CAAC,CAAC;IAClCC,QAAQ,CAACE,MAAM,GAAGH,aAAa,CAAC,CAAC,CAAC;EACpC,CAAC;EACD;AACF;AACA;AACA;AACA;EACE5C,iBAAiB,CAACM,SAAS,CAACgC,QAAQ,GAAG,YAAY;IACjD,OAAO,IAAI,CAACL,OAAO,CAAC,CAAC,CAACV,KAAK;EAC7B,CAAC;EACD;AACF;AACA;AACA;AACA;EACEvB,iBAAiB,CAACM,SAAS,CAAC0C,WAAW,GAAG,YAAY;IACpD,OAAO,IAAI,CAACV,QAAQ,CAAC,CAAC,CAACZ,QAAQ;EACjC,CAAC;EACD1B,iBAAiB,CAACM,SAAS,CAAC2C,aAAa,GAAG,UAAUN,SAAS,EAAEO,cAAc,EAAEC,QAAQ,EAAE;IACzF,SAASC,OAAOA,CAACC,GAAG,EAAE;MACpB,OAAOC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,IAAI,IAAI;IAClC;IACA;IACA,IAAIF,QAAQ,KAAK,MAAM,EAAE;MACvB,IAAII,MAAM,GAAG,IAAI,CAACC,aAAa,CAACb,SAAS,EAAEQ,QAAQ,CAAC;MACpD,IAAIM,UAAU,GAAGF,MAAM,CAAC1C,IAAI;MAC5B,IAAI6C,SAAS,GAAGH,MAAM,CAACI,KAAK;MAC5B,IAAIC,QAAQ,GAAGH,UAAU,CAACI,MAAM,GAAG,MAAM,GAAGJ,UAAU,CAACK,MAAM;MAC7D,OAAO/D,mBAAmB,CAAC,WAAW,EAAE;QACtCgE,IAAI,EAAEH,QAAQ;QACdD,KAAK,EAAED,SAAS;QAChBN,OAAO,EAAEA,OAAO,CAACM,SAAS;MAC5B,CAAC,CAAC;IACJ;IACA;IAAA,KACK;MACH,IAAIM,IAAI,GAAG,IAAI,CAAC1B,QAAQ,CAAC,CAAC,CAAC2B,cAAc,CAACtB,SAAS,CAAC;MACpD,IAAIgB,KAAK,GAAGK,IAAI,CAACvB,SAAS,CAAC,CAAC,CAACkB,KAAK;MAClC,IAAIO,MAAM,GAAG,IAAI,CAACV,aAAa,CAACb,SAAS,EAAEQ,QAAQ,CAAC,CAACtC,IAAI,CAACkD,IAAI;MAC9D,OAAOhE,mBAAmB,CAAC,WAAW,EAAE;QACtCgE,IAAI,EAAEG,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAG,EAAE,GAAG,IAAI;QACzCP,KAAK,EAAEA,KAAK;QACZP,OAAO,EAAEA,OAAO,CAACO,KAAK;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EACD3D,iBAAiB,CAACM,SAAS,CAAC6D,aAAa,GAAG,YAAY,CAAC,CAAC;EAC1D;EACAnE,iBAAiB,CAACM,SAAS,CAACkD,aAAa,GAAG,UAAUb,SAAS,EAAEQ,QAAQ,EAAE;IACzE,IAAII,MAAM,GAAGtD,MAAM,CAACK,SAAS,CAACkD,aAAa,CAACY,IAAI,CAAC,IAAI,EAAEzB,SAAS,EAAEQ,QAAQ,CAAC;IAC3E,IAAII,MAAM,CAACI,KAAK,IAAI,IAAI,IAAIR,QAAQ,KAAK,MAAM,EAAE;MAC/C,IAAIa,IAAI,GAAG,IAAI,CAAC1B,QAAQ,CAAC,CAAC,CAAC2B,cAAc,CAACtB,SAAS,CAAC;MACpD,IAAI0B,SAAS,GAAGL,IAAI,CAACvB,SAAS,CAAC,CAAC,CAACkB,KAAK;MACtCJ,MAAM,CAACI,KAAK,GAAGU,SAAS;IAC1B;IACA,OAAOd,MAAM;EACf,CAAC;EACDvD,iBAAiB,CAACK,IAAI,GAAG,eAAe;EACxCL,iBAAiB,CAACsE,aAAa,GAAG;IAChC;IACAC,CAAC,EAAE,CAAC;IACJC,gBAAgB,EAAE,MAAM;IACxBC,IAAI,EAAE,IAAI;IACVC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,YAAY;IACpBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,EAAE;IACpBC,KAAK,EAAE;MACLC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,KAAK;MACXE,QAAQ,EAAE;IACZ,CAAC;IACDvE,MAAM,EAAE,EAAE;IACVyE,SAAS,EAAE,SAAS;IACpBC,SAAS,EAAE;MACTC,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,GAAG;MACZC,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE;MACRV,KAAK,EAAE;QACLC,IAAI,EAAE;MACR,CAAC;MACDK,SAAS,EAAE;QACTE,OAAO,EAAE;MACX;IACF,CAAC;IACDG,MAAM,EAAE;MACNC,SAAS,EAAE;QACTC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,eAAe,EAAE,QAAQ;IACzBC,iBAAiB,EAAE;EACrB,CAAC;EACD,OAAOjG,iBAAiB;AAC1B,CAAC,CAACJ,WAAW,CAAC;AACd,eAAeI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}