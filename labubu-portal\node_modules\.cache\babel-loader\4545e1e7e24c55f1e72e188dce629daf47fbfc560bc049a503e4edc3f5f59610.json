{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport SankeyView from './SankeyView.js';\nimport SankeySeriesModel from './SankeySeries.js';\nimport sankeyLayout from './sankeyLayout.js';\nimport sankeyVisual from './sankeyVisual.js';\nexport function install(registers) {\n  registers.registerChartView(SankeyView);\n  registers.registerSeriesModel(SankeySeriesModel);\n  registers.registerLayout(sankeyLayout);\n  registers.registerVisual(sankeyVisual);\n  registers.registerAction({\n    type: 'dragNode',\n    event: 'dragnode',\n    // here can only use 'update' now, other value is not support in echarts.\n    update: 'update'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'sankey',\n      query: payload\n    }, function (seriesModel) {\n      seriesModel.setNodePosition(payload.dataIndex, [payload.localX, payload.localY]);\n    });\n  });\n}", "map": {"version": 3, "names": ["SankeyView", "SankeySeriesModel", "sankeyLayout", "sankeyVisual", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "registerVisual", "registerAction", "type", "event", "update", "payload", "ecModel", "eachComponent", "mainType", "subType", "query", "seriesModel", "setNodePosition", "dataIndex", "localX", "localY"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/sankey/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport SankeyView from './SankeyView.js';\nimport SankeySeriesModel from './SankeySeries.js';\nimport sankeyLayout from './sankeyLayout.js';\nimport sankeyVisual from './sankeyVisual.js';\nexport function install(registers) {\n  registers.registerChartView(SankeyView);\n  registers.registerSeriesModel(SankeySeriesModel);\n  registers.registerLayout(sankeyLayout);\n  registers.registerVisual(sankeyVisual);\n  registers.registerAction({\n    type: 'dragNode',\n    event: 'dragnode',\n    // here can only use 'update' now, other value is not support in echarts.\n    update: 'update'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'sankey',\n      query: payload\n    }, function (seriesModel) {\n      seriesModel.setNodePosition(payload.dataIndex, [payload.localX, payload.localY]);\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,OAAOC,iBAAiB,MAAM,mBAAmB;AACjD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACN,UAAU,CAAC;EACvCK,SAAS,CAACE,mBAAmB,CAACN,iBAAiB,CAAC;EAChDI,SAAS,CAACG,cAAc,CAACN,YAAY,CAAC;EACtCG,SAAS,CAACI,cAAc,CAACN,YAAY,CAAC;EACtCE,SAAS,CAACK,cAAc,CAAC;IACvBC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,UAAU;IACjB;IACAC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC7BA,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAEL;IACT,CAAC,EAAE,UAAUM,WAAW,EAAE;MACxBA,WAAW,CAACC,eAAe,CAACP,OAAO,CAACQ,SAAS,EAAE,CAACR,OAAO,CAACS,MAAM,EAAET,OAAO,CAACU,MAAM,CAAC,CAAC;IAClF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}