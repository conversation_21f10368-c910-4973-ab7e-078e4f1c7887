language: "node_js"
sudo: false

node_js:
  - "0.8"
  - "0.10"
  - "0.12"
  - "1"
  - "2"

notifications:
  email:
    on_success: never
    on_failure: never

before_script:
  - npm install standard
  - standard

script:
  - npm install istanbul-harmony
  - node --harmony node_modules/.bin/istanbul cover test.js

after_success:
  - npm install coveralls
  - cat ./coverage/lcov.info | coveralls

matrix:
  allow_failures:
    - node_js: "0.8"
    - node_js: "0.10"