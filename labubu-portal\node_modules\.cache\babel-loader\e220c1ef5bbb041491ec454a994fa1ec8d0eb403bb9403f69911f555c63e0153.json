{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport { getLayoutRect, box as layoutBox, positionElement } from '../../util/layout.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as graphic from '../../util/graphic.js';\n/**\r\n * Layout list like component.\r\n * It will box layout each items in group of component and then position the whole group in the viewport\r\n * @param {module:zrender/group/Group} group\r\n * @param {module:echarts/model/Component} componentModel\r\n * @param {module:echarts/ExtensionAPI}\r\n */\nexport function layout(group, componentModel, api) {\n  var boxLayoutParams = componentModel.getBoxLayoutParams();\n  var padding = componentModel.get('padding');\n  var viewportSize = {\n    width: api.getWidth(),\n    height: api.getHeight()\n  };\n  var rect = getLayoutRect(boxLayoutParams, viewportSize, padding);\n  layoutBox(componentModel.get('orient'), group, componentModel.get('itemGap'), rect.width, rect.height);\n  positionElement(group, boxLayoutParams, viewportSize, padding);\n}\nexport function makeBackground(rect, componentModel) {\n  var padding = formatUtil.normalizeCssArray(componentModel.get('padding'));\n  var style = componentModel.getItemStyle(['color', 'opacity']);\n  style.fill = componentModel.get('backgroundColor');\n  rect = new graphic.Rect({\n    shape: {\n      x: rect.x - padding[3],\n      y: rect.y - padding[0],\n      width: rect.width + padding[1] + padding[3],\n      height: rect.height + padding[0] + padding[2],\n      r: componentModel.get('borderRadius')\n    },\n    style: style,\n    silent: true,\n    z2: -1\n  });\n  // FIXME\n  // `subPixelOptimizeRect` may bring some gap between edge of viewpart\n  // and background rect when setting like `left: 0`, `top: 0`.\n  // graphic.subPixelOptimizeRect(rect);\n  return rect;\n}", "map": {"version": 3, "names": ["getLayoutRect", "box", "layoutBox", "positionElement", "formatUtil", "graphic", "layout", "group", "componentModel", "api", "boxLayoutParams", "getBoxLayoutParams", "padding", "get", "viewportSize", "width", "getWidth", "height", "getHeight", "rect", "makeBackground", "normalizeCssArray", "style", "getItemStyle", "fill", "Rect", "shape", "x", "y", "r", "silent", "z2"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/helper/listComponent.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport { getLayoutRect, box as layoutBox, positionElement } from '../../util/layout.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as graphic from '../../util/graphic.js';\n/**\r\n * Layout list like component.\r\n * It will box layout each items in group of component and then position the whole group in the viewport\r\n * @param {module:zrender/group/Group} group\r\n * @param {module:echarts/model/Component} componentModel\r\n * @param {module:echarts/ExtensionAPI}\r\n */\nexport function layout(group, componentModel, api) {\n  var boxLayoutParams = componentModel.getBoxLayoutParams();\n  var padding = componentModel.get('padding');\n  var viewportSize = {\n    width: api.getWidth(),\n    height: api.getHeight()\n  };\n  var rect = getLayoutRect(boxLayoutParams, viewportSize, padding);\n  layoutBox(componentModel.get('orient'), group, componentModel.get('itemGap'), rect.width, rect.height);\n  positionElement(group, boxLayoutParams, viewportSize, padding);\n}\nexport function makeBackground(rect, componentModel) {\n  var padding = formatUtil.normalizeCssArray(componentModel.get('padding'));\n  var style = componentModel.getItemStyle(['color', 'opacity']);\n  style.fill = componentModel.get('backgroundColor');\n  rect = new graphic.Rect({\n    shape: {\n      x: rect.x - padding[3],\n      y: rect.y - padding[0],\n      width: rect.width + padding[1] + padding[3],\n      height: rect.height + padding[0] + padding[2],\n      r: componentModel.get('borderRadius')\n    },\n    style: style,\n    silent: true,\n    z2: -1\n  });\n  // FIXME\n  // `subPixelOptimizeRect` may bring some gap between edge of viewpart\n  // and background rect when setting like `left: 0`, `top: 0`.\n  // graphic.subPixelOptimizeRect(rect);\n  return rect;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,GAAG,IAAIC,SAAS,EAAEC,eAAe,QAAQ,sBAAsB;AACvF,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAEC,cAAc,EAAEC,GAAG,EAAE;EACjD,IAAIC,eAAe,GAAGF,cAAc,CAACG,kBAAkB,CAAC,CAAC;EACzD,IAAIC,OAAO,GAAGJ,cAAc,CAACK,GAAG,CAAC,SAAS,CAAC;EAC3C,IAAIC,YAAY,GAAG;IACjBC,KAAK,EAAEN,GAAG,CAACO,QAAQ,CAAC,CAAC;IACrBC,MAAM,EAAER,GAAG,CAACS,SAAS,CAAC;EACxB,CAAC;EACD,IAAIC,IAAI,GAAGnB,aAAa,CAACU,eAAe,EAAEI,YAAY,EAAEF,OAAO,CAAC;EAChEV,SAAS,CAACM,cAAc,CAACK,GAAG,CAAC,QAAQ,CAAC,EAAEN,KAAK,EAAEC,cAAc,CAACK,GAAG,CAAC,SAAS,CAAC,EAAEM,IAAI,CAACJ,KAAK,EAAEI,IAAI,CAACF,MAAM,CAAC;EACtGd,eAAe,CAACI,KAAK,EAAEG,eAAe,EAAEI,YAAY,EAAEF,OAAO,CAAC;AAChE;AACA,OAAO,SAASQ,cAAcA,CAACD,IAAI,EAAEX,cAAc,EAAE;EACnD,IAAII,OAAO,GAAGR,UAAU,CAACiB,iBAAiB,CAACb,cAAc,CAACK,GAAG,CAAC,SAAS,CAAC,CAAC;EACzE,IAAIS,KAAK,GAAGd,cAAc,CAACe,YAAY,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;EAC7DD,KAAK,CAACE,IAAI,GAAGhB,cAAc,CAACK,GAAG,CAAC,iBAAiB,CAAC;EAClDM,IAAI,GAAG,IAAId,OAAO,CAACoB,IAAI,CAAC;IACtBC,KAAK,EAAE;MACLC,CAAC,EAAER,IAAI,CAACQ,CAAC,GAAGf,OAAO,CAAC,CAAC,CAAC;MACtBgB,CAAC,EAAET,IAAI,CAACS,CAAC,GAAGhB,OAAO,CAAC,CAAC,CAAC;MACtBG,KAAK,EAAEI,IAAI,CAACJ,KAAK,GAAGH,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAC3CK,MAAM,EAAEE,IAAI,CAACF,MAAM,GAAGL,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;MAC7CiB,CAAC,EAAErB,cAAc,CAACK,GAAG,CAAC,cAAc;IACtC,CAAC;IACDS,KAAK,EAAEA,KAAK;IACZQ,MAAM,EAAE,IAAI;IACZC,EAAE,EAAE,CAAC;EACP,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA,OAAOZ,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}