{"name": "tailwindcss", "version": "2.2.19", "description": "A utility-first CSS framework for rapidly building custom user interfaces.", "license": "MIT", "main": "lib/index.js", "style": "dist/tailwind.css", "repository": "https://github.com/tailwindlabs/tailwindcss.git", "bugs": "https://github.com/tailwindlabs/tailwindcss/issues", "homepage": "https://tailwindcss.com", "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "scripts": {"prebabelify": "npm run generate:plugin-list && rimraf lib", "babelify": "babel src --out-dir lib --copy-files", "postbabelify": "ncc build lib/cli-peer-dependencies.js -o peers", "rebuild-fixtures": "npm run babelify && babel-node scripts/rebuildFixtures.js", "prepublishOnly": "npm install --force && npm run babelify && babel-node scripts/build.js && node scripts/build-plugins.js", "style": "eslint .", "test": "cross-env TAILWIND_MODE=build jest", "test:integrations": "npm run test --prefix ./integrations", "install:integrations": "node scripts/install-integrations.js", "posttest": "npm run style", "compat": "node scripts/compat.js --prepare", "compat:restore": "node scripts/compat.js --restore", "generate:plugin-list": "babel-node scripts/create-plugin-list.js"}, "files": ["dist/*.css", "cli/*", "lib/*", "peers/*", "scripts/*.js", "stubs/*.stub.js", "nesting/*", "*.css", "*.js"], "devDependencies": {"@babel/cli": "^7.14.8", "@babel/core": "^7.15.0", "@babel/node": "^7.12.13", "@babel/preset-env": "^7.15.0", "@tailwindcss/aspect-ratio": "^0.2.1", "@vercel/ncc": "^0.30.0", "autoprefixer": "^10.3.3", "babel-jest": "^27.0.6", "clean-css": "5.1.4", "cross-env": "^7.0.3", "cssnano": "^5.0.8", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.1", "jest": "^27.0.6", "jest-diff": "^27.0.2", "postcss": "^8.3.6", "postcss-cli": "^8.3.1", "prettier": "^2.3.2", "rimraf": "^3.0.0"}, "peerDependencies": {"autoprefixer": "^10.0.2", "postcss": "^8.0.9"}, "dependencies": {"arg": "^5.0.1", "bytes": "^3.0.0", "chalk": "^4.1.2", "chokidar": "^3.5.2", "color": "^4.0.1", "cosmiconfig": "^7.0.1", "detective": "^5.2.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.2.7", "fs-extra": "^10.0.0", "glob-parent": "^6.0.1", "html-tags": "^3.1.0", "is-color-stop": "^1.1.0", "is-glob": "^4.0.1", "lodash": "^4.17.21", "lodash.topath": "^4.5.2", "modern-normalize": "^1.1.0", "node-emoji": "^1.11.0", "normalize-path": "^3.0.0", "object-hash": "^2.2.0", "postcss-js": "^3.0.3", "postcss-load-config": "^3.1.0", "postcss-nested": "5.0.6", "postcss-selector-parser": "^6.0.6", "postcss-value-parser": "^4.1.0", "pretty-hrtime": "^1.0.3", "purgecss": "^4.0.3", "quick-lru": "^5.1.1", "reduce-css-calc": "^2.1.8", "resolve": "^1.20.0", "tmp": "^0.2.1"}, "browserslist": ["> 1%", "not edge <= 18", "not ie 11", "not op_mini all"], "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "12.13.0"}}]]}, "jest": {"testTimeout": 30000, "setupFilesAfterEnv": ["<rootDir>/jest/customMatchers.js"], "testPathIgnorePatterns": ["/node_modules/", "/integrations/"]}, "engines": {"node": ">=12.13.0"}}