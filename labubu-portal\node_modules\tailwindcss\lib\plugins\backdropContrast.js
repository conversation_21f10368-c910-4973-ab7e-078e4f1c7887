"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _default;

function _default() {
  return function ({
    config,
    matchUtilities,
    theme,
    variants
  }) {
    matchUtilities({
      'backdrop-contrast': value => {
        return {
          '--tw-backdrop-contrast': `contrast(${value})`,
          ...(config('mode') === 'jit' ? {
            '@defaults backdrop-filter': {},
            'backdrop-filter': 'var(--tw-backdrop-filter)'
          } : {})
        };
      }
    }, {
      values: theme('backdropContrast'),
      variants: variants('backdropContrast'),
      type: 'any'
    });
  };
}