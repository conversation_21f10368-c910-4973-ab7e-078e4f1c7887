{"name": "postcss-js", "version": "3.0.3", "description": "PostCSS for React Inline Styles, Radium, Free Style and other CSS-in-JS", "keywords": ["postcss", "postcss-runner", "js", "inline", "react", "css", "c<PERSON><PERSON>s"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "postcss/postcss-js", "engines": {"node": ">=10.0"}, "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./": "./"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"camelcase-css": "^2.0.1", "postcss": "^8.1.6"}}