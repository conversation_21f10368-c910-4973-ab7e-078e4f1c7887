{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { isString, extend, map, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { formatTplSimple } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport ComponentView from '../../view/Component.js';\nimport { getLocaleModel } from '../../core/locale.js';\nvar CalendarView = /** @class */function (_super) {\n  __extends(CalendarView, _super);\n  function CalendarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CalendarView.type;\n    return _this;\n  }\n  CalendarView.prototype.render = function (calendarModel, ecModel, api) {\n    var group = this.group;\n    group.removeAll();\n    var coordSys = calendarModel.coordinateSystem;\n    // range info\n    var rangeData = coordSys.getRangeInfo();\n    var orient = coordSys.getOrient();\n    // locale\n    var localeModel = ecModel.getLocaleModel();\n    this._renderDayRect(calendarModel, rangeData, group);\n    // _renderLines must be called prior to following function\n    this._renderLines(calendarModel, rangeData, orient, group);\n    this._renderYearText(calendarModel, rangeData, orient, group);\n    this._renderMonthText(calendarModel, localeModel, orient, group);\n    this._renderWeekText(calendarModel, localeModel, rangeData, orient, group);\n  };\n  // render day rect\n  CalendarView.prototype._renderDayRect = function (calendarModel, rangeData, group) {\n    var coordSys = calendarModel.coordinateSystem;\n    var itemRectStyleModel = calendarModel.getModel('itemStyle').getItemStyle();\n    var sw = coordSys.getCellWidth();\n    var sh = coordSys.getCellHeight();\n    for (var i = rangeData.start.time; i <= rangeData.end.time; i = coordSys.getNextNDay(i, 1).time) {\n      var point = coordSys.dataToRect([i], false).tl;\n      // every rect\n      var rect = new graphic.Rect({\n        shape: {\n          x: point[0],\n          y: point[1],\n          width: sw,\n          height: sh\n        },\n        cursor: 'default',\n        style: itemRectStyleModel\n      });\n      group.add(rect);\n    }\n  };\n  // render separate line\n  CalendarView.prototype._renderLines = function (calendarModel, rangeData, orient, group) {\n    var self = this;\n    var coordSys = calendarModel.coordinateSystem;\n    var lineStyleModel = calendarModel.getModel(['splitLine', 'lineStyle']).getLineStyle();\n    var show = calendarModel.get(['splitLine', 'show']);\n    var lineWidth = lineStyleModel.lineWidth;\n    this._tlpoints = [];\n    this._blpoints = [];\n    this._firstDayOfMonth = [];\n    this._firstDayPoints = [];\n    var firstDay = rangeData.start;\n    for (var i = 0; firstDay.time <= rangeData.end.time; i++) {\n      addPoints(firstDay.formatedDate);\n      if (i === 0) {\n        firstDay = coordSys.getDateInfo(rangeData.start.y + '-' + rangeData.start.m);\n      }\n      var date = firstDay.date;\n      date.setMonth(date.getMonth() + 1);\n      firstDay = coordSys.getDateInfo(date);\n    }\n    addPoints(coordSys.getNextNDay(rangeData.end.time, 1).formatedDate);\n    function addPoints(date) {\n      self._firstDayOfMonth.push(coordSys.getDateInfo(date));\n      self._firstDayPoints.push(coordSys.dataToRect([date], false).tl);\n      var points = self._getLinePointsOfOneWeek(calendarModel, date, orient);\n      self._tlpoints.push(points[0]);\n      self._blpoints.push(points[points.length - 1]);\n      show && self._drawSplitline(points, lineStyleModel, group);\n    }\n    // render top/left line\n    show && this._drawSplitline(self._getEdgesPoints(self._tlpoints, lineWidth, orient), lineStyleModel, group);\n    // render bottom/right line\n    show && this._drawSplitline(self._getEdgesPoints(self._blpoints, lineWidth, orient), lineStyleModel, group);\n  };\n  // get points at both ends\n  CalendarView.prototype._getEdgesPoints = function (points, lineWidth, orient) {\n    var rs = [points[0].slice(), points[points.length - 1].slice()];\n    var idx = orient === 'horizontal' ? 0 : 1;\n    // both ends of the line are extend half lineWidth\n    rs[0][idx] = rs[0][idx] - lineWidth / 2;\n    rs[1][idx] = rs[1][idx] + lineWidth / 2;\n    return rs;\n  };\n  // render split line\n  CalendarView.prototype._drawSplitline = function (points, lineStyle, group) {\n    var poyline = new graphic.Polyline({\n      z2: 20,\n      shape: {\n        points: points\n      },\n      style: lineStyle\n    });\n    group.add(poyline);\n  };\n  // render month line of one week points\n  CalendarView.prototype._getLinePointsOfOneWeek = function (calendarModel, date, orient) {\n    var coordSys = calendarModel.coordinateSystem;\n    var parsedDate = coordSys.getDateInfo(date);\n    var points = [];\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(parsedDate.time, i);\n      var point = coordSys.dataToRect([tmpD.time], false);\n      points[2 * tmpD.day] = point.tl;\n      points[2 * tmpD.day + 1] = point[orient === 'horizontal' ? 'bl' : 'tr'];\n    }\n    return points;\n  };\n  CalendarView.prototype._formatterLabel = function (formatter, params) {\n    if (isString(formatter) && formatter) {\n      return formatTplSimple(formatter, params);\n    }\n    if (isFunction(formatter)) {\n      return formatter(params);\n    }\n    return params.nameMap;\n  };\n  CalendarView.prototype._yearTextPositionControl = function (textEl, point, orient, position, margin) {\n    var x = point[0];\n    var y = point[1];\n    var aligns = ['center', 'bottom'];\n    if (position === 'bottom') {\n      y += margin;\n      aligns = ['center', 'top'];\n    } else if (position === 'left') {\n      x -= margin;\n    } else if (position === 'right') {\n      x += margin;\n      aligns = ['center', 'top'];\n    } else {\n      // top\n      y -= margin;\n    }\n    var rotate = 0;\n    if (position === 'left' || position === 'right') {\n      rotate = Math.PI / 2;\n    }\n    return {\n      rotation: rotate,\n      x: x,\n      y: y,\n      style: {\n        align: aligns[0],\n        verticalAlign: aligns[1]\n      }\n    };\n  };\n  // render year\n  CalendarView.prototype._renderYearText = function (calendarModel, rangeData, orient, group) {\n    var yearLabel = calendarModel.getModel('yearLabel');\n    if (!yearLabel.get('show')) {\n      return;\n    }\n    var margin = yearLabel.get('margin');\n    var pos = yearLabel.get('position');\n    if (!pos) {\n      pos = orient !== 'horizontal' ? 'top' : 'left';\n    }\n    var points = [this._tlpoints[this._tlpoints.length - 1], this._blpoints[0]];\n    var xc = (points[0][0] + points[1][0]) / 2;\n    var yc = (points[0][1] + points[1][1]) / 2;\n    var idx = orient === 'horizontal' ? 0 : 1;\n    var posPoints = {\n      top: [xc, points[idx][1]],\n      bottom: [xc, points[1 - idx][1]],\n      left: [points[1 - idx][0], yc],\n      right: [points[idx][0], yc]\n    };\n    var name = rangeData.start.y;\n    if (+rangeData.end.y > +rangeData.start.y) {\n      name = name + '-' + rangeData.end.y;\n    }\n    var formatter = yearLabel.get('formatter');\n    var params = {\n      start: rangeData.start.y,\n      end: rangeData.end.y,\n      nameMap: name\n    };\n    var content = this._formatterLabel(formatter, params);\n    var yearText = new graphic.Text({\n      z2: 30,\n      style: createTextStyle(yearLabel, {\n        text: content\n      }),\n      silent: yearLabel.get('silent')\n    });\n    yearText.attr(this._yearTextPositionControl(yearText, posPoints[pos], orient, pos, margin));\n    group.add(yearText);\n  };\n  CalendarView.prototype._monthTextPositionControl = function (point, isCenter, orient, position, margin) {\n    var align = 'left';\n    var vAlign = 'top';\n    var x = point[0];\n    var y = point[1];\n    if (orient === 'horizontal') {\n      y = y + margin;\n      if (isCenter) {\n        align = 'center';\n      }\n      if (position === 'start') {\n        vAlign = 'bottom';\n      }\n    } else {\n      x = x + margin;\n      if (isCenter) {\n        vAlign = 'middle';\n      }\n      if (position === 'start') {\n        align = 'right';\n      }\n    }\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  };\n  // render month and year text\n  CalendarView.prototype._renderMonthText = function (calendarModel, localeModel, orient, group) {\n    var monthLabel = calendarModel.getModel('monthLabel');\n    if (!monthLabel.get('show')) {\n      return;\n    }\n    var nameMap = monthLabel.get('nameMap');\n    var margin = monthLabel.get('margin');\n    var pos = monthLabel.get('position');\n    var align = monthLabel.get('align');\n    var termPoints = [this._tlpoints, this._blpoints];\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      }\n      // PENDING\n      // for ZH locale, original form is `一月` but current form is `1月`\n      nameMap = localeModel.get(['time', 'monthAbbr']) || [];\n    }\n    var idx = pos === 'start' ? 0 : 1;\n    var axis = orient === 'horizontal' ? 0 : 1;\n    margin = pos === 'start' ? -margin : margin;\n    var isCenter = align === 'center';\n    var labelSilent = monthLabel.get('silent');\n    for (var i = 0; i < termPoints[idx].length - 1; i++) {\n      var tmp = termPoints[idx][i].slice();\n      var firstDay = this._firstDayOfMonth[i];\n      if (isCenter) {\n        var firstDayPoints = this._firstDayPoints[i];\n        tmp[axis] = (firstDayPoints[axis] + termPoints[0][i + 1][axis]) / 2;\n      }\n      var formatter = monthLabel.get('formatter');\n      var name_1 = nameMap[+firstDay.m - 1];\n      var params = {\n        yyyy: firstDay.y,\n        yy: (firstDay.y + '').slice(2),\n        MM: firstDay.m,\n        M: +firstDay.m,\n        nameMap: name_1\n      };\n      var content = this._formatterLabel(formatter, params);\n      var monthText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(monthLabel, {\n          text: content\n        }), this._monthTextPositionControl(tmp, isCenter, orient, pos, margin)),\n        silent: labelSilent\n      });\n      group.add(monthText);\n    }\n  };\n  CalendarView.prototype._weekTextPositionControl = function (point, orient, position, margin, cellSize) {\n    var align = 'center';\n    var vAlign = 'middle';\n    var x = point[0];\n    var y = point[1];\n    var isStart = position === 'start';\n    if (orient === 'horizontal') {\n      x = x + margin + (isStart ? 1 : -1) * cellSize[0] / 2;\n      align = isStart ? 'right' : 'left';\n    } else {\n      y = y + margin + (isStart ? 1 : -1) * cellSize[1] / 2;\n      vAlign = isStart ? 'bottom' : 'top';\n    }\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  };\n  // render weeks\n  CalendarView.prototype._renderWeekText = function (calendarModel, localeModel, rangeData, orient, group) {\n    var dayLabel = calendarModel.getModel('dayLabel');\n    if (!dayLabel.get('show')) {\n      return;\n    }\n    var coordSys = calendarModel.coordinateSystem;\n    var pos = dayLabel.get('position');\n    var nameMap = dayLabel.get('nameMap');\n    var margin = dayLabel.get('margin');\n    var firstDayOfWeek = coordSys.getFirstDayOfWeek();\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      }\n      // Use the first letter of `dayOfWeekAbbr` if `dayOfWeekShort` doesn't exist in the locale file\n      var dayOfWeekShort = localeModel.get(['time', 'dayOfWeekShort']);\n      nameMap = dayOfWeekShort || map(localeModel.get(['time', 'dayOfWeekAbbr']), function (val) {\n        return val[0];\n      });\n    }\n    var start = coordSys.getNextNDay(rangeData.end.time, 7 - rangeData.lweek).time;\n    var cellSize = [coordSys.getCellWidth(), coordSys.getCellHeight()];\n    margin = parsePercent(margin, Math.min(cellSize[1], cellSize[0]));\n    if (pos === 'start') {\n      start = coordSys.getNextNDay(rangeData.start.time, -(7 + rangeData.fweek)).time;\n      margin = -margin;\n    }\n    var labelSilent = dayLabel.get('silent');\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(start, i);\n      var point = coordSys.dataToRect([tmpD.time], false).center;\n      var day = i;\n      day = Math.abs((i + firstDayOfWeek) % 7);\n      var weekText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(dayLabel, {\n          text: nameMap[day]\n        }), this._weekTextPositionControl(point, orient, pos, margin, cellSize)),\n        silent: labelSilent\n      });\n      group.add(weekText);\n    }\n  };\n  CalendarView.type = 'calendar';\n  return CalendarView;\n}(ComponentView);\nexport default CalendarView;", "map": {"version": 3, "names": ["__extends", "isString", "extend", "map", "isFunction", "graphic", "createTextStyle", "formatTplSimple", "parsePercent", "ComponentView", "getLocaleModel", "CalendarView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "calendarModel", "ecModel", "api", "group", "removeAll", "coordSys", "coordinateSystem", "rangeData", "getRangeInfo", "orient", "getOrient", "localeModel", "_renderDayRect", "_renderLines", "_renderYearText", "_renderMonthText", "_renderWeekText", "itemRectStyleModel", "getModel", "getItemStyle", "sw", "get<PERSON>ell<PERSON><PERSON>th", "sh", "getCellHeight", "i", "start", "time", "end", "getNextNDay", "point", "dataToRect", "tl", "rect", "Rect", "shape", "x", "y", "width", "height", "cursor", "style", "add", "self", "lineStyleModel", "getLineStyle", "show", "get", "lineWidth", "_tlpoints", "_blpoints", "_firstDayOfMonth", "_firstDayPoints", "firstDay", "addPoints", "formatedDate", "getDateInfo", "m", "date", "setMonth", "getMonth", "push", "points", "_getLinePointsOfOneWeek", "length", "_drawSplitline", "_getEdgesPoints", "rs", "slice", "idx", "lineStyle", "poyline", "Polyline", "z2", "parsedDate", "tmpD", "day", "_formatterLabel", "formatter", "params", "nameMap", "_yearTextPositionControl", "textEl", "position", "margin", "aligns", "rotate", "Math", "PI", "rotation", "align", "verticalAlign", "<PERSON><PERSON><PERSON><PERSON>", "pos", "xc", "yc", "posPoints", "top", "bottom", "left", "right", "name", "content", "yearText", "Text", "text", "silent", "attr", "_monthTextPositionControl", "isCenter", "vAlign", "<PERSON><PERSON><PERSON><PERSON>", "termPoints", "axis", "labelSilent", "tmp", "firstDayPoints", "name_1", "yyyy", "yy", "MM", "M", "monthText", "_weekTextPositionControl", "cellSize", "isStart", "<PERSON><PERSON><PERSON><PERSON>", "firstDayOfWeek", "getFirstDayOfWeek", "dayOfWeekShort", "val", "lweek", "min", "fweek", "center", "abs", "weekText"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/calendar/CalendarView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { isString, extend, map, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { formatTplSimple } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport ComponentView from '../../view/Component.js';\nimport { getLocaleModel } from '../../core/locale.js';\nvar CalendarView = /** @class */function (_super) {\n  __extends(CalendarView, _super);\n  function CalendarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CalendarView.type;\n    return _this;\n  }\n  CalendarView.prototype.render = function (calendarModel, ecModel, api) {\n    var group = this.group;\n    group.removeAll();\n    var coordSys = calendarModel.coordinateSystem;\n    // range info\n    var rangeData = coordSys.getRangeInfo();\n    var orient = coordSys.getOrient();\n    // locale\n    var localeModel = ecModel.getLocaleModel();\n    this._renderDayRect(calendarModel, rangeData, group);\n    // _renderLines must be called prior to following function\n    this._renderLines(calendarModel, rangeData, orient, group);\n    this._renderYearText(calendarModel, rangeData, orient, group);\n    this._renderMonthText(calendarModel, localeModel, orient, group);\n    this._renderWeekText(calendarModel, localeModel, rangeData, orient, group);\n  };\n  // render day rect\n  CalendarView.prototype._renderDayRect = function (calendarModel, rangeData, group) {\n    var coordSys = calendarModel.coordinateSystem;\n    var itemRectStyleModel = calendarModel.getModel('itemStyle').getItemStyle();\n    var sw = coordSys.getCellWidth();\n    var sh = coordSys.getCellHeight();\n    for (var i = rangeData.start.time; i <= rangeData.end.time; i = coordSys.getNextNDay(i, 1).time) {\n      var point = coordSys.dataToRect([i], false).tl;\n      // every rect\n      var rect = new graphic.Rect({\n        shape: {\n          x: point[0],\n          y: point[1],\n          width: sw,\n          height: sh\n        },\n        cursor: 'default',\n        style: itemRectStyleModel\n      });\n      group.add(rect);\n    }\n  };\n  // render separate line\n  CalendarView.prototype._renderLines = function (calendarModel, rangeData, orient, group) {\n    var self = this;\n    var coordSys = calendarModel.coordinateSystem;\n    var lineStyleModel = calendarModel.getModel(['splitLine', 'lineStyle']).getLineStyle();\n    var show = calendarModel.get(['splitLine', 'show']);\n    var lineWidth = lineStyleModel.lineWidth;\n    this._tlpoints = [];\n    this._blpoints = [];\n    this._firstDayOfMonth = [];\n    this._firstDayPoints = [];\n    var firstDay = rangeData.start;\n    for (var i = 0; firstDay.time <= rangeData.end.time; i++) {\n      addPoints(firstDay.formatedDate);\n      if (i === 0) {\n        firstDay = coordSys.getDateInfo(rangeData.start.y + '-' + rangeData.start.m);\n      }\n      var date = firstDay.date;\n      date.setMonth(date.getMonth() + 1);\n      firstDay = coordSys.getDateInfo(date);\n    }\n    addPoints(coordSys.getNextNDay(rangeData.end.time, 1).formatedDate);\n    function addPoints(date) {\n      self._firstDayOfMonth.push(coordSys.getDateInfo(date));\n      self._firstDayPoints.push(coordSys.dataToRect([date], false).tl);\n      var points = self._getLinePointsOfOneWeek(calendarModel, date, orient);\n      self._tlpoints.push(points[0]);\n      self._blpoints.push(points[points.length - 1]);\n      show && self._drawSplitline(points, lineStyleModel, group);\n    }\n    // render top/left line\n    show && this._drawSplitline(self._getEdgesPoints(self._tlpoints, lineWidth, orient), lineStyleModel, group);\n    // render bottom/right line\n    show && this._drawSplitline(self._getEdgesPoints(self._blpoints, lineWidth, orient), lineStyleModel, group);\n  };\n  // get points at both ends\n  CalendarView.prototype._getEdgesPoints = function (points, lineWidth, orient) {\n    var rs = [points[0].slice(), points[points.length - 1].slice()];\n    var idx = orient === 'horizontal' ? 0 : 1;\n    // both ends of the line are extend half lineWidth\n    rs[0][idx] = rs[0][idx] - lineWidth / 2;\n    rs[1][idx] = rs[1][idx] + lineWidth / 2;\n    return rs;\n  };\n  // render split line\n  CalendarView.prototype._drawSplitline = function (points, lineStyle, group) {\n    var poyline = new graphic.Polyline({\n      z2: 20,\n      shape: {\n        points: points\n      },\n      style: lineStyle\n    });\n    group.add(poyline);\n  };\n  // render month line of one week points\n  CalendarView.prototype._getLinePointsOfOneWeek = function (calendarModel, date, orient) {\n    var coordSys = calendarModel.coordinateSystem;\n    var parsedDate = coordSys.getDateInfo(date);\n    var points = [];\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(parsedDate.time, i);\n      var point = coordSys.dataToRect([tmpD.time], false);\n      points[2 * tmpD.day] = point.tl;\n      points[2 * tmpD.day + 1] = point[orient === 'horizontal' ? 'bl' : 'tr'];\n    }\n    return points;\n  };\n  CalendarView.prototype._formatterLabel = function (formatter, params) {\n    if (isString(formatter) && formatter) {\n      return formatTplSimple(formatter, params);\n    }\n    if (isFunction(formatter)) {\n      return formatter(params);\n    }\n    return params.nameMap;\n  };\n  CalendarView.prototype._yearTextPositionControl = function (textEl, point, orient, position, margin) {\n    var x = point[0];\n    var y = point[1];\n    var aligns = ['center', 'bottom'];\n    if (position === 'bottom') {\n      y += margin;\n      aligns = ['center', 'top'];\n    } else if (position === 'left') {\n      x -= margin;\n    } else if (position === 'right') {\n      x += margin;\n      aligns = ['center', 'top'];\n    } else {\n      // top\n      y -= margin;\n    }\n    var rotate = 0;\n    if (position === 'left' || position === 'right') {\n      rotate = Math.PI / 2;\n    }\n    return {\n      rotation: rotate,\n      x: x,\n      y: y,\n      style: {\n        align: aligns[0],\n        verticalAlign: aligns[1]\n      }\n    };\n  };\n  // render year\n  CalendarView.prototype._renderYearText = function (calendarModel, rangeData, orient, group) {\n    var yearLabel = calendarModel.getModel('yearLabel');\n    if (!yearLabel.get('show')) {\n      return;\n    }\n    var margin = yearLabel.get('margin');\n    var pos = yearLabel.get('position');\n    if (!pos) {\n      pos = orient !== 'horizontal' ? 'top' : 'left';\n    }\n    var points = [this._tlpoints[this._tlpoints.length - 1], this._blpoints[0]];\n    var xc = (points[0][0] + points[1][0]) / 2;\n    var yc = (points[0][1] + points[1][1]) / 2;\n    var idx = orient === 'horizontal' ? 0 : 1;\n    var posPoints = {\n      top: [xc, points[idx][1]],\n      bottom: [xc, points[1 - idx][1]],\n      left: [points[1 - idx][0], yc],\n      right: [points[idx][0], yc]\n    };\n    var name = rangeData.start.y;\n    if (+rangeData.end.y > +rangeData.start.y) {\n      name = name + '-' + rangeData.end.y;\n    }\n    var formatter = yearLabel.get('formatter');\n    var params = {\n      start: rangeData.start.y,\n      end: rangeData.end.y,\n      nameMap: name\n    };\n    var content = this._formatterLabel(formatter, params);\n    var yearText = new graphic.Text({\n      z2: 30,\n      style: createTextStyle(yearLabel, {\n        text: content\n      }),\n      silent: yearLabel.get('silent')\n    });\n    yearText.attr(this._yearTextPositionControl(yearText, posPoints[pos], orient, pos, margin));\n    group.add(yearText);\n  };\n  CalendarView.prototype._monthTextPositionControl = function (point, isCenter, orient, position, margin) {\n    var align = 'left';\n    var vAlign = 'top';\n    var x = point[0];\n    var y = point[1];\n    if (orient === 'horizontal') {\n      y = y + margin;\n      if (isCenter) {\n        align = 'center';\n      }\n      if (position === 'start') {\n        vAlign = 'bottom';\n      }\n    } else {\n      x = x + margin;\n      if (isCenter) {\n        vAlign = 'middle';\n      }\n      if (position === 'start') {\n        align = 'right';\n      }\n    }\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  };\n  // render month and year text\n  CalendarView.prototype._renderMonthText = function (calendarModel, localeModel, orient, group) {\n    var monthLabel = calendarModel.getModel('monthLabel');\n    if (!monthLabel.get('show')) {\n      return;\n    }\n    var nameMap = monthLabel.get('nameMap');\n    var margin = monthLabel.get('margin');\n    var pos = monthLabel.get('position');\n    var align = monthLabel.get('align');\n    var termPoints = [this._tlpoints, this._blpoints];\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      }\n      // PENDING\n      // for ZH locale, original form is `一月` but current form is `1月`\n      nameMap = localeModel.get(['time', 'monthAbbr']) || [];\n    }\n    var idx = pos === 'start' ? 0 : 1;\n    var axis = orient === 'horizontal' ? 0 : 1;\n    margin = pos === 'start' ? -margin : margin;\n    var isCenter = align === 'center';\n    var labelSilent = monthLabel.get('silent');\n    for (var i = 0; i < termPoints[idx].length - 1; i++) {\n      var tmp = termPoints[idx][i].slice();\n      var firstDay = this._firstDayOfMonth[i];\n      if (isCenter) {\n        var firstDayPoints = this._firstDayPoints[i];\n        tmp[axis] = (firstDayPoints[axis] + termPoints[0][i + 1][axis]) / 2;\n      }\n      var formatter = monthLabel.get('formatter');\n      var name_1 = nameMap[+firstDay.m - 1];\n      var params = {\n        yyyy: firstDay.y,\n        yy: (firstDay.y + '').slice(2),\n        MM: firstDay.m,\n        M: +firstDay.m,\n        nameMap: name_1\n      };\n      var content = this._formatterLabel(formatter, params);\n      var monthText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(monthLabel, {\n          text: content\n        }), this._monthTextPositionControl(tmp, isCenter, orient, pos, margin)),\n        silent: labelSilent\n      });\n      group.add(monthText);\n    }\n  };\n  CalendarView.prototype._weekTextPositionControl = function (point, orient, position, margin, cellSize) {\n    var align = 'center';\n    var vAlign = 'middle';\n    var x = point[0];\n    var y = point[1];\n    var isStart = position === 'start';\n    if (orient === 'horizontal') {\n      x = x + margin + (isStart ? 1 : -1) * cellSize[0] / 2;\n      align = isStart ? 'right' : 'left';\n    } else {\n      y = y + margin + (isStart ? 1 : -1) * cellSize[1] / 2;\n      vAlign = isStart ? 'bottom' : 'top';\n    }\n    return {\n      x: x,\n      y: y,\n      align: align,\n      verticalAlign: vAlign\n    };\n  };\n  // render weeks\n  CalendarView.prototype._renderWeekText = function (calendarModel, localeModel, rangeData, orient, group) {\n    var dayLabel = calendarModel.getModel('dayLabel');\n    if (!dayLabel.get('show')) {\n      return;\n    }\n    var coordSys = calendarModel.coordinateSystem;\n    var pos = dayLabel.get('position');\n    var nameMap = dayLabel.get('nameMap');\n    var margin = dayLabel.get('margin');\n    var firstDayOfWeek = coordSys.getFirstDayOfWeek();\n    if (!nameMap || isString(nameMap)) {\n      if (nameMap) {\n        // case-sensitive\n        localeModel = getLocaleModel(nameMap) || localeModel;\n      }\n      // Use the first letter of `dayOfWeekAbbr` if `dayOfWeekShort` doesn't exist in the locale file\n      var dayOfWeekShort = localeModel.get(['time', 'dayOfWeekShort']);\n      nameMap = dayOfWeekShort || map(localeModel.get(['time', 'dayOfWeekAbbr']), function (val) {\n        return val[0];\n      });\n    }\n    var start = coordSys.getNextNDay(rangeData.end.time, 7 - rangeData.lweek).time;\n    var cellSize = [coordSys.getCellWidth(), coordSys.getCellHeight()];\n    margin = parsePercent(margin, Math.min(cellSize[1], cellSize[0]));\n    if (pos === 'start') {\n      start = coordSys.getNextNDay(rangeData.start.time, -(7 + rangeData.fweek)).time;\n      margin = -margin;\n    }\n    var labelSilent = dayLabel.get('silent');\n    for (var i = 0; i < 7; i++) {\n      var tmpD = coordSys.getNextNDay(start, i);\n      var point = coordSys.dataToRect([tmpD.time], false).center;\n      var day = i;\n      day = Math.abs((i + firstDayOfWeek) % 7);\n      var weekText = new graphic.Text({\n        z2: 30,\n        style: extend(createTextStyle(dayLabel, {\n          text: nameMap[day]\n        }), this._weekTextPositionControl(point, orient, pos, margin, cellSize)),\n        silent: labelSilent\n      });\n      group.add(weekText);\n    }\n  };\n  CalendarView.type = 'calendar';\n  return CalendarView;\n}(ComponentView);\nexport default CalendarView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAEC,UAAU,QAAQ,0BAA0B;AAC5E,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,eAAe,QAAQ,sBAAsB;AACtD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDZ,SAAS,CAACW,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EACAF,YAAY,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACrE,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,CAACC,SAAS,CAAC,CAAC;IACjB,IAAIC,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C;IACA,IAAIC,SAAS,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;IACvC,IAAIC,MAAM,GAAGJ,QAAQ,CAACK,SAAS,CAAC,CAAC;IACjC;IACA,IAAIC,WAAW,GAAGV,OAAO,CAACV,cAAc,CAAC,CAAC;IAC1C,IAAI,CAACqB,cAAc,CAACZ,aAAa,EAAEO,SAAS,EAAEJ,KAAK,CAAC;IACpD;IACA,IAAI,CAACU,YAAY,CAACb,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,CAAC;IAC1D,IAAI,CAACW,eAAe,CAACd,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,CAAC;IAC7D,IAAI,CAACY,gBAAgB,CAACf,aAAa,EAAEW,WAAW,EAAEF,MAAM,EAAEN,KAAK,CAAC;IAChE,IAAI,CAACa,eAAe,CAAChB,aAAa,EAAEW,WAAW,EAAEJ,SAAS,EAAEE,MAAM,EAAEN,KAAK,CAAC;EAC5E,CAAC;EACD;EACAX,YAAY,CAACM,SAAS,CAACc,cAAc,GAAG,UAAUZ,aAAa,EAAEO,SAAS,EAAEJ,KAAK,EAAE;IACjF,IAAIE,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAIW,kBAAkB,GAAGjB,aAAa,CAACkB,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;IAC3E,IAAIC,EAAE,GAAGf,QAAQ,CAACgB,YAAY,CAAC,CAAC;IAChC,IAAIC,EAAE,GAAGjB,QAAQ,CAACkB,aAAa,CAAC,CAAC;IACjC,KAAK,IAAIC,CAAC,GAAGjB,SAAS,CAACkB,KAAK,CAACC,IAAI,EAAEF,CAAC,IAAIjB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAEF,CAAC,GAAGnB,QAAQ,CAACuB,WAAW,CAACJ,CAAC,EAAE,CAAC,CAAC,CAACE,IAAI,EAAE;MAC/F,IAAIG,KAAK,GAAGxB,QAAQ,CAACyB,UAAU,CAAC,CAACN,CAAC,CAAC,EAAE,KAAK,CAAC,CAACO,EAAE;MAC9C;MACA,IAAIC,IAAI,GAAG,IAAI9C,OAAO,CAAC+C,IAAI,CAAC;QAC1BC,KAAK,EAAE;UACLC,CAAC,EAAEN,KAAK,CAAC,CAAC,CAAC;UACXO,CAAC,EAAEP,KAAK,CAAC,CAAC,CAAC;UACXQ,KAAK,EAAEjB,EAAE;UACTkB,MAAM,EAAEhB;QACV,CAAC;QACDiB,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAEvB;MACT,CAAC,CAAC;MACFd,KAAK,CAACsC,GAAG,CAACT,IAAI,CAAC;IACjB;EACF,CAAC;EACD;EACAxC,YAAY,CAACM,SAAS,CAACe,YAAY,GAAG,UAAUb,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,EAAE;IACvF,IAAIuC,IAAI,GAAG,IAAI;IACf,IAAIrC,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAIqC,cAAc,GAAG3C,aAAa,CAACkB,QAAQ,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC0B,YAAY,CAAC,CAAC;IACtF,IAAIC,IAAI,GAAG7C,aAAa,CAAC8C,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IACnD,IAAIC,SAAS,GAAGJ,cAAc,CAACI,SAAS;IACxC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAIC,QAAQ,GAAG7C,SAAS,CAACkB,KAAK;IAC9B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAE4B,QAAQ,CAAC1B,IAAI,IAAInB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAEF,CAAC,EAAE,EAAE;MACxD6B,SAAS,CAACD,QAAQ,CAACE,YAAY,CAAC;MAChC,IAAI9B,CAAC,KAAK,CAAC,EAAE;QACX4B,QAAQ,GAAG/C,QAAQ,CAACkD,WAAW,CAAChD,SAAS,CAACkB,KAAK,CAACW,CAAC,GAAG,GAAG,GAAG7B,SAAS,CAACkB,KAAK,CAAC+B,CAAC,CAAC;MAC9E;MACA,IAAIC,IAAI,GAAGL,QAAQ,CAACK,IAAI;MACxBA,IAAI,CAACC,QAAQ,CAACD,IAAI,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAClCP,QAAQ,GAAG/C,QAAQ,CAACkD,WAAW,CAACE,IAAI,CAAC;IACvC;IACAJ,SAAS,CAAChD,QAAQ,CAACuB,WAAW,CAACrB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC4B,YAAY,CAAC;IACnE,SAASD,SAASA,CAACI,IAAI,EAAE;MACvBf,IAAI,CAACQ,gBAAgB,CAACU,IAAI,CAACvD,QAAQ,CAACkD,WAAW,CAACE,IAAI,CAAC,CAAC;MACtDf,IAAI,CAACS,eAAe,CAACS,IAAI,CAACvD,QAAQ,CAACyB,UAAU,CAAC,CAAC2B,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC1B,EAAE,CAAC;MAChE,IAAI8B,MAAM,GAAGnB,IAAI,CAACoB,uBAAuB,CAAC9D,aAAa,EAAEyD,IAAI,EAAEhD,MAAM,CAAC;MACtEiC,IAAI,CAACM,SAAS,CAACY,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9BnB,IAAI,CAACO,SAAS,CAACW,IAAI,CAACC,MAAM,CAACA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC;MAC9ClB,IAAI,IAAIH,IAAI,CAACsB,cAAc,CAACH,MAAM,EAAElB,cAAc,EAAExC,KAAK,CAAC;IAC5D;IACA;IACA0C,IAAI,IAAI,IAAI,CAACmB,cAAc,CAACtB,IAAI,CAACuB,eAAe,CAACvB,IAAI,CAACM,SAAS,EAAED,SAAS,EAAEtC,MAAM,CAAC,EAAEkC,cAAc,EAAExC,KAAK,CAAC;IAC3G;IACA0C,IAAI,IAAI,IAAI,CAACmB,cAAc,CAACtB,IAAI,CAACuB,eAAe,CAACvB,IAAI,CAACO,SAAS,EAAEF,SAAS,EAAEtC,MAAM,CAAC,EAAEkC,cAAc,EAAExC,KAAK,CAAC;EAC7G,CAAC;EACD;EACAX,YAAY,CAACM,SAAS,CAACmE,eAAe,GAAG,UAAUJ,MAAM,EAAEd,SAAS,EAAEtC,MAAM,EAAE;IAC5E,IAAIyD,EAAE,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAEN,MAAM,CAACA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;IAC/D,IAAIC,GAAG,GAAG3D,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IACzC;IACAyD,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGrB,SAAS,GAAG,CAAC;IACvCmB,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,CAACE,GAAG,CAAC,GAAGrB,SAAS,GAAG,CAAC;IACvC,OAAOmB,EAAE;EACX,CAAC;EACD;EACA1E,YAAY,CAACM,SAAS,CAACkE,cAAc,GAAG,UAAUH,MAAM,EAAEQ,SAAS,EAAElE,KAAK,EAAE;IAC1E,IAAImE,OAAO,GAAG,IAAIpF,OAAO,CAACqF,QAAQ,CAAC;MACjCC,EAAE,EAAE,EAAE;MACNtC,KAAK,EAAE;QACL2B,MAAM,EAAEA;MACV,CAAC;MACDrB,KAAK,EAAE6B;IACT,CAAC,CAAC;IACFlE,KAAK,CAACsC,GAAG,CAAC6B,OAAO,CAAC;EACpB,CAAC;EACD;EACA9E,YAAY,CAACM,SAAS,CAACgE,uBAAuB,GAAG,UAAU9D,aAAa,EAAEyD,IAAI,EAAEhD,MAAM,EAAE;IACtF,IAAIJ,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAImE,UAAU,GAAGpE,QAAQ,CAACkD,WAAW,CAACE,IAAI,CAAC;IAC3C,IAAII,MAAM,GAAG,EAAE;IACf,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIkD,IAAI,GAAGrE,QAAQ,CAACuB,WAAW,CAAC6C,UAAU,CAAC/C,IAAI,EAAEF,CAAC,CAAC;MACnD,IAAIK,KAAK,GAAGxB,QAAQ,CAACyB,UAAU,CAAC,CAAC4C,IAAI,CAAChD,IAAI,CAAC,EAAE,KAAK,CAAC;MACnDmC,MAAM,CAAC,CAAC,GAAGa,IAAI,CAACC,GAAG,CAAC,GAAG9C,KAAK,CAACE,EAAE;MAC/B8B,MAAM,CAAC,CAAC,GAAGa,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,GAAG9C,KAAK,CAACpB,MAAM,KAAK,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC;IACzE;IACA,OAAOoD,MAAM;EACf,CAAC;EACDrE,YAAY,CAACM,SAAS,CAAC8E,eAAe,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;IACpE,IAAIhG,QAAQ,CAAC+F,SAAS,CAAC,IAAIA,SAAS,EAAE;MACpC,OAAOzF,eAAe,CAACyF,SAAS,EAAEC,MAAM,CAAC;IAC3C;IACA,IAAI7F,UAAU,CAAC4F,SAAS,CAAC,EAAE;MACzB,OAAOA,SAAS,CAACC,MAAM,CAAC;IAC1B;IACA,OAAOA,MAAM,CAACC,OAAO;EACvB,CAAC;EACDvF,YAAY,CAACM,SAAS,CAACkF,wBAAwB,GAAG,UAAUC,MAAM,EAAEpD,KAAK,EAAEpB,MAAM,EAAEyE,QAAQ,EAAEC,MAAM,EAAE;IACnG,IAAIhD,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIO,CAAC,GAAGP,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIuD,MAAM,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACjC,IAAIF,QAAQ,KAAK,QAAQ,EAAE;MACzB9C,CAAC,IAAI+C,MAAM;MACXC,MAAM,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAIF,QAAQ,KAAK,MAAM,EAAE;MAC9B/C,CAAC,IAAIgD,MAAM;IACb,CAAC,MAAM,IAAID,QAAQ,KAAK,OAAO,EAAE;MAC/B/C,CAAC,IAAIgD,MAAM;MACXC,MAAM,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC5B,CAAC,MAAM;MACL;MACAhD,CAAC,IAAI+C,MAAM;IACb;IACA,IAAIE,MAAM,GAAG,CAAC;IACd,IAAIH,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,EAAE;MAC/CG,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IACtB;IACA,OAAO;MACLC,QAAQ,EAAEH,MAAM;MAChBlD,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJI,KAAK,EAAE;QACLiD,KAAK,EAAEL,MAAM,CAAC,CAAC,CAAC;QAChBM,aAAa,EAAEN,MAAM,CAAC,CAAC;MACzB;IACF,CAAC;EACH,CAAC;EACD;EACA5F,YAAY,CAACM,SAAS,CAACgB,eAAe,GAAG,UAAUd,aAAa,EAAEO,SAAS,EAAEE,MAAM,EAAEN,KAAK,EAAE;IAC1F,IAAIwF,SAAS,GAAG3F,aAAa,CAACkB,QAAQ,CAAC,WAAW,CAAC;IACnD,IAAI,CAACyE,SAAS,CAAC7C,GAAG,CAAC,MAAM,CAAC,EAAE;MAC1B;IACF;IACA,IAAIqC,MAAM,GAAGQ,SAAS,CAAC7C,GAAG,CAAC,QAAQ,CAAC;IACpC,IAAI8C,GAAG,GAAGD,SAAS,CAAC7C,GAAG,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC8C,GAAG,EAAE;MACRA,GAAG,GAAGnF,MAAM,KAAK,YAAY,GAAG,KAAK,GAAG,MAAM;IAChD;IACA,IAAIoD,MAAM,GAAG,CAAC,IAAI,CAACb,SAAS,CAAC,IAAI,CAACA,SAAS,CAACe,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAACd,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3E,IAAI4C,EAAE,GAAG,CAAChC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1C,IAAIiC,EAAE,GAAG,CAACjC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1C,IAAIO,GAAG,GAAG3D,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IACzC,IAAIsF,SAAS,GAAG;MACdC,GAAG,EAAE,CAACH,EAAE,EAAEhC,MAAM,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB6B,MAAM,EAAE,CAACJ,EAAE,EAAEhC,MAAM,CAAC,CAAC,GAAGO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC8B,IAAI,EAAE,CAACrC,MAAM,CAAC,CAAC,GAAGO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE0B,EAAE,CAAC;MAC9BK,KAAK,EAAE,CAACtC,MAAM,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE0B,EAAE;IAC5B,CAAC;IACD,IAAIM,IAAI,GAAG7F,SAAS,CAACkB,KAAK,CAACW,CAAC;IAC5B,IAAI,CAAC7B,SAAS,CAACoB,GAAG,CAACS,CAAC,GAAG,CAAC7B,SAAS,CAACkB,KAAK,CAACW,CAAC,EAAE;MACzCgE,IAAI,GAAGA,IAAI,GAAG,GAAG,GAAG7F,SAAS,CAACoB,GAAG,CAACS,CAAC;IACrC;IACA,IAAIyC,SAAS,GAAGc,SAAS,CAAC7C,GAAG,CAAC,WAAW,CAAC;IAC1C,IAAIgC,MAAM,GAAG;MACXrD,KAAK,EAAElB,SAAS,CAACkB,KAAK,CAACW,CAAC;MACxBT,GAAG,EAAEpB,SAAS,CAACoB,GAAG,CAACS,CAAC;MACpB2C,OAAO,EAAEqB;IACX,CAAC;IACD,IAAIC,OAAO,GAAG,IAAI,CAACzB,eAAe,CAACC,SAAS,EAAEC,MAAM,CAAC;IACrD,IAAIwB,QAAQ,GAAG,IAAIpH,OAAO,CAACqH,IAAI,CAAC;MAC9B/B,EAAE,EAAE,EAAE;MACNhC,KAAK,EAAErD,eAAe,CAACwG,SAAS,EAAE;QAChCa,IAAI,EAAEH;MACR,CAAC,CAAC;MACFI,MAAM,EAAEd,SAAS,CAAC7C,GAAG,CAAC,QAAQ;IAChC,CAAC,CAAC;IACFwD,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC1B,wBAAwB,CAACsB,QAAQ,EAAEP,SAAS,CAACH,GAAG,CAAC,EAAEnF,MAAM,EAAEmF,GAAG,EAAET,MAAM,CAAC,CAAC;IAC3FhF,KAAK,CAACsC,GAAG,CAAC6D,QAAQ,CAAC;EACrB,CAAC;EACD9G,YAAY,CAACM,SAAS,CAAC6G,yBAAyB,GAAG,UAAU9E,KAAK,EAAE+E,QAAQ,EAAEnG,MAAM,EAAEyE,QAAQ,EAAEC,MAAM,EAAE;IACtG,IAAIM,KAAK,GAAG,MAAM;IAClB,IAAIoB,MAAM,GAAG,KAAK;IAClB,IAAI1E,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIO,CAAC,GAAGP,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIpB,MAAM,KAAK,YAAY,EAAE;MAC3B2B,CAAC,GAAGA,CAAC,GAAG+C,MAAM;MACd,IAAIyB,QAAQ,EAAE;QACZnB,KAAK,GAAG,QAAQ;MAClB;MACA,IAAIP,QAAQ,KAAK,OAAO,EAAE;QACxB2B,MAAM,GAAG,QAAQ;MACnB;IACF,CAAC,MAAM;MACL1E,CAAC,GAAGA,CAAC,GAAGgD,MAAM;MACd,IAAIyB,QAAQ,EAAE;QACZC,MAAM,GAAG,QAAQ;MACnB;MACA,IAAI3B,QAAQ,KAAK,OAAO,EAAE;QACxBO,KAAK,GAAG,OAAO;MACjB;IACF;IACA,OAAO;MACLtD,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJqD,KAAK,EAAEA,KAAK;MACZC,aAAa,EAAEmB;IACjB,CAAC;EACH,CAAC;EACD;EACArH,YAAY,CAACM,SAAS,CAACiB,gBAAgB,GAAG,UAAUf,aAAa,EAAEW,WAAW,EAAEF,MAAM,EAAEN,KAAK,EAAE;IAC7F,IAAI2G,UAAU,GAAG9G,aAAa,CAACkB,QAAQ,CAAC,YAAY,CAAC;IACrD,IAAI,CAAC4F,UAAU,CAAChE,GAAG,CAAC,MAAM,CAAC,EAAE;MAC3B;IACF;IACA,IAAIiC,OAAO,GAAG+B,UAAU,CAAChE,GAAG,CAAC,SAAS,CAAC;IACvC,IAAIqC,MAAM,GAAG2B,UAAU,CAAChE,GAAG,CAAC,QAAQ,CAAC;IACrC,IAAI8C,GAAG,GAAGkB,UAAU,CAAChE,GAAG,CAAC,UAAU,CAAC;IACpC,IAAI2C,KAAK,GAAGqB,UAAU,CAAChE,GAAG,CAAC,OAAO,CAAC;IACnC,IAAIiE,UAAU,GAAG,CAAC,IAAI,CAAC/D,SAAS,EAAE,IAAI,CAACC,SAAS,CAAC;IACjD,IAAI,CAAC8B,OAAO,IAAIjG,QAAQ,CAACiG,OAAO,CAAC,EAAE;MACjC,IAAIA,OAAO,EAAE;QACX;QACApE,WAAW,GAAGpB,cAAc,CAACwF,OAAO,CAAC,IAAIpE,WAAW;MACtD;MACA;MACA;MACAoE,OAAO,GAAGpE,WAAW,CAACmC,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE;IACxD;IACA,IAAIsB,GAAG,GAAGwB,GAAG,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC;IACjC,IAAIoB,IAAI,GAAGvG,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;IAC1C0E,MAAM,GAAGS,GAAG,KAAK,OAAO,GAAG,CAACT,MAAM,GAAGA,MAAM;IAC3C,IAAIyB,QAAQ,GAAGnB,KAAK,KAAK,QAAQ;IACjC,IAAIwB,WAAW,GAAGH,UAAU,CAAChE,GAAG,CAAC,QAAQ,CAAC;IAC1C,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,UAAU,CAAC3C,GAAG,CAAC,CAACL,MAAM,GAAG,CAAC,EAAEvC,CAAC,EAAE,EAAE;MACnD,IAAI0F,GAAG,GAAGH,UAAU,CAAC3C,GAAG,CAAC,CAAC5C,CAAC,CAAC,CAAC2C,KAAK,CAAC,CAAC;MACpC,IAAIf,QAAQ,GAAG,IAAI,CAACF,gBAAgB,CAAC1B,CAAC,CAAC;MACvC,IAAIoF,QAAQ,EAAE;QACZ,IAAIO,cAAc,GAAG,IAAI,CAAChE,eAAe,CAAC3B,CAAC,CAAC;QAC5C0F,GAAG,CAACF,IAAI,CAAC,GAAG,CAACG,cAAc,CAACH,IAAI,CAAC,GAAGD,UAAU,CAAC,CAAC,CAAC,CAACvF,CAAC,GAAG,CAAC,CAAC,CAACwF,IAAI,CAAC,IAAI,CAAC;MACrE;MACA,IAAInC,SAAS,GAAGiC,UAAU,CAAChE,GAAG,CAAC,WAAW,CAAC;MAC3C,IAAIsE,MAAM,GAAGrC,OAAO,CAAC,CAAC3B,QAAQ,CAACI,CAAC,GAAG,CAAC,CAAC;MACrC,IAAIsB,MAAM,GAAG;QACXuC,IAAI,EAAEjE,QAAQ,CAAChB,CAAC;QAChBkF,EAAE,EAAE,CAAClE,QAAQ,CAAChB,CAAC,GAAG,EAAE,EAAE+B,KAAK,CAAC,CAAC,CAAC;QAC9BoD,EAAE,EAAEnE,QAAQ,CAACI,CAAC;QACdgE,CAAC,EAAE,CAACpE,QAAQ,CAACI,CAAC;QACduB,OAAO,EAAEqC;MACX,CAAC;MACD,IAAIf,OAAO,GAAG,IAAI,CAACzB,eAAe,CAACC,SAAS,EAAEC,MAAM,CAAC;MACrD,IAAI2C,SAAS,GAAG,IAAIvI,OAAO,CAACqH,IAAI,CAAC;QAC/B/B,EAAE,EAAE,EAAE;QACNhC,KAAK,EAAEzD,MAAM,CAACI,eAAe,CAAC2H,UAAU,EAAE;UACxCN,IAAI,EAAEH;QACR,CAAC,CAAC,EAAE,IAAI,CAACM,yBAAyB,CAACO,GAAG,EAAEN,QAAQ,EAAEnG,MAAM,EAAEmF,GAAG,EAAET,MAAM,CAAC,CAAC;QACvEsB,MAAM,EAAEQ;MACV,CAAC,CAAC;MACF9G,KAAK,CAACsC,GAAG,CAACgF,SAAS,CAAC;IACtB;EACF,CAAC;EACDjI,YAAY,CAACM,SAAS,CAAC4H,wBAAwB,GAAG,UAAU7F,KAAK,EAAEpB,MAAM,EAAEyE,QAAQ,EAAEC,MAAM,EAAEwC,QAAQ,EAAE;IACrG,IAAIlC,KAAK,GAAG,QAAQ;IACpB,IAAIoB,MAAM,GAAG,QAAQ;IACrB,IAAI1E,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;IAChB,IAAIO,CAAC,GAAGP,KAAK,CAAC,CAAC,CAAC;IAChB,IAAI+F,OAAO,GAAG1C,QAAQ,KAAK,OAAO;IAClC,IAAIzE,MAAM,KAAK,YAAY,EAAE;MAC3B0B,CAAC,GAAGA,CAAC,GAAGgD,MAAM,GAAG,CAACyC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAID,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;MACrDlC,KAAK,GAAGmC,OAAO,GAAG,OAAO,GAAG,MAAM;IACpC,CAAC,MAAM;MACLxF,CAAC,GAAGA,CAAC,GAAG+C,MAAM,GAAG,CAACyC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAID,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;MACrDd,MAAM,GAAGe,OAAO,GAAG,QAAQ,GAAG,KAAK;IACrC;IACA,OAAO;MACLzF,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJqD,KAAK,EAAEA,KAAK;MACZC,aAAa,EAAEmB;IACjB,CAAC;EACH,CAAC;EACD;EACArH,YAAY,CAACM,SAAS,CAACkB,eAAe,GAAG,UAAUhB,aAAa,EAAEW,WAAW,EAAEJ,SAAS,EAAEE,MAAM,EAAEN,KAAK,EAAE;IACvG,IAAI0H,QAAQ,GAAG7H,aAAa,CAACkB,QAAQ,CAAC,UAAU,CAAC;IACjD,IAAI,CAAC2G,QAAQ,CAAC/E,GAAG,CAAC,MAAM,CAAC,EAAE;MACzB;IACF;IACA,IAAIzC,QAAQ,GAAGL,aAAa,CAACM,gBAAgB;IAC7C,IAAIsF,GAAG,GAAGiC,QAAQ,CAAC/E,GAAG,CAAC,UAAU,CAAC;IAClC,IAAIiC,OAAO,GAAG8C,QAAQ,CAAC/E,GAAG,CAAC,SAAS,CAAC;IACrC,IAAIqC,MAAM,GAAG0C,QAAQ,CAAC/E,GAAG,CAAC,QAAQ,CAAC;IACnC,IAAIgF,cAAc,GAAGzH,QAAQ,CAAC0H,iBAAiB,CAAC,CAAC;IACjD,IAAI,CAAChD,OAAO,IAAIjG,QAAQ,CAACiG,OAAO,CAAC,EAAE;MACjC,IAAIA,OAAO,EAAE;QACX;QACApE,WAAW,GAAGpB,cAAc,CAACwF,OAAO,CAAC,IAAIpE,WAAW;MACtD;MACA;MACA,IAAIqH,cAAc,GAAGrH,WAAW,CAACmC,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;MAChEiC,OAAO,GAAGiD,cAAc,IAAIhJ,GAAG,CAAC2B,WAAW,CAACmC,GAAG,CAAC,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,EAAE,UAAUmF,GAAG,EAAE;QACzF,OAAOA,GAAG,CAAC,CAAC,CAAC;MACf,CAAC,CAAC;IACJ;IACA,IAAIxG,KAAK,GAAGpB,QAAQ,CAACuB,WAAW,CAACrB,SAAS,CAACoB,GAAG,CAACD,IAAI,EAAE,CAAC,GAAGnB,SAAS,CAAC2H,KAAK,CAAC,CAACxG,IAAI;IAC9E,IAAIiG,QAAQ,GAAG,CAACtH,QAAQ,CAACgB,YAAY,CAAC,CAAC,EAAEhB,QAAQ,CAACkB,aAAa,CAAC,CAAC,CAAC;IAClE4D,MAAM,GAAG9F,YAAY,CAAC8F,MAAM,EAAEG,IAAI,CAAC6C,GAAG,CAACR,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI/B,GAAG,KAAK,OAAO,EAAE;MACnBnE,KAAK,GAAGpB,QAAQ,CAACuB,WAAW,CAACrB,SAAS,CAACkB,KAAK,CAACC,IAAI,EAAE,EAAE,CAAC,GAAGnB,SAAS,CAAC6H,KAAK,CAAC,CAAC,CAAC1G,IAAI;MAC/EyD,MAAM,GAAG,CAACA,MAAM;IAClB;IACA,IAAI8B,WAAW,GAAGY,QAAQ,CAAC/E,GAAG,CAAC,QAAQ,CAAC;IACxC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIkD,IAAI,GAAGrE,QAAQ,CAACuB,WAAW,CAACH,KAAK,EAAED,CAAC,CAAC;MACzC,IAAIK,KAAK,GAAGxB,QAAQ,CAACyB,UAAU,CAAC,CAAC4C,IAAI,CAAChD,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC2G,MAAM;MAC1D,IAAI1D,GAAG,GAAGnD,CAAC;MACXmD,GAAG,GAAGW,IAAI,CAACgD,GAAG,CAAC,CAAC9G,CAAC,GAAGsG,cAAc,IAAI,CAAC,CAAC;MACxC,IAAIS,QAAQ,GAAG,IAAIrJ,OAAO,CAACqH,IAAI,CAAC;QAC9B/B,EAAE,EAAE,EAAE;QACNhC,KAAK,EAAEzD,MAAM,CAACI,eAAe,CAAC0I,QAAQ,EAAE;UACtCrB,IAAI,EAAEzB,OAAO,CAACJ,GAAG;QACnB,CAAC,CAAC,EAAE,IAAI,CAAC+C,wBAAwB,CAAC7F,KAAK,EAAEpB,MAAM,EAAEmF,GAAG,EAAET,MAAM,EAAEwC,QAAQ,CAAC,CAAC;QACxElB,MAAM,EAAEQ;MACV,CAAC,CAAC;MACF9G,KAAK,CAACsC,GAAG,CAAC8F,QAAQ,CAAC;IACrB;EACF,CAAC;EACD/I,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAACF,aAAa,CAAC;AAChB,eAAeE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}