{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport PointerPath from './PointerPath.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { createTextStyle, setLabelValueAnimation, animateLabelValue } from '../../label/labelStyle.js';\nimport ChartView from '../../view/Chart.js';\nimport { parsePercent, round, linearMap } from '../../util/number.js';\nimport Sausage from '../../util/shape/sausage.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { extend, isFunction, isString, isNumber, each } from 'zrender/lib/core/util.js';\nimport { setCommonECData } from '../../util/innerStore.js';\nimport { normalizeArcAngles } from 'zrender/lib/core/PathProxy.js';\nfunction parsePosition(seriesModel, api) {\n  var center = seriesModel.get('center');\n  var width = api.getWidth();\n  var height = api.getHeight();\n  var size = Math.min(width, height);\n  var cx = parsePercent(center[0], api.getWidth());\n  var cy = parsePercent(center[1], api.getHeight());\n  var r = parsePercent(seriesModel.get('radius'), size / 2);\n  return {\n    cx: cx,\n    cy: cy,\n    r: r\n  };\n}\nfunction formatLabel(value, labelFormatter) {\n  var label = value == null ? '' : value + '';\n  if (labelFormatter) {\n    if (isString(labelFormatter)) {\n      label = labelFormatter.replace('{value}', label);\n    } else if (isFunction(labelFormatter)) {\n      label = labelFormatter(value);\n    }\n  }\n  return label;\n}\nvar GaugeView = /** @class */function (_super) {\n  __extends(GaugeView, _super);\n  function GaugeView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GaugeView.type;\n    return _this;\n  }\n  GaugeView.prototype.render = function (seriesModel, ecModel, api) {\n    this.group.removeAll();\n    var colorList = seriesModel.get(['axisLine', 'lineStyle', 'color']);\n    var posInfo = parsePosition(seriesModel, api);\n    this._renderMain(seriesModel, ecModel, api, colorList, posInfo);\n    this._data = seriesModel.getData();\n  };\n  GaugeView.prototype.dispose = function () {};\n  GaugeView.prototype._renderMain = function (seriesModel, ecModel, api, colorList, posInfo) {\n    var group = this.group;\n    var clockwise = seriesModel.get('clockwise');\n    var startAngle = -seriesModel.get('startAngle') / 180 * Math.PI;\n    var endAngle = -seriesModel.get('endAngle') / 180 * Math.PI;\n    var axisLineModel = seriesModel.getModel('axisLine');\n    var roundCap = axisLineModel.get('roundCap');\n    var MainPath = roundCap ? Sausage : graphic.Sector;\n    var showAxis = axisLineModel.get('show');\n    var lineStyleModel = axisLineModel.getModel('lineStyle');\n    var axisLineWidth = lineStyleModel.get('width');\n    var angles = [startAngle, endAngle];\n    normalizeArcAngles(angles, !clockwise);\n    startAngle = angles[0];\n    endAngle = angles[1];\n    var angleRangeSpan = endAngle - startAngle;\n    var prevEndAngle = startAngle;\n    var sectors = [];\n    for (var i = 0; showAxis && i < colorList.length; i++) {\n      // Clamp\n      var percent = Math.min(Math.max(colorList[i][0], 0), 1);\n      endAngle = startAngle + angleRangeSpan * percent;\n      var sector = new MainPath({\n        shape: {\n          startAngle: prevEndAngle,\n          endAngle: endAngle,\n          cx: posInfo.cx,\n          cy: posInfo.cy,\n          clockwise: clockwise,\n          r0: posInfo.r - axisLineWidth,\n          r: posInfo.r\n        },\n        silent: true\n      });\n      sector.setStyle({\n        fill: colorList[i][1]\n      });\n      sector.setStyle(lineStyleModel.getLineStyle(\n      // Because we use sector to simulate arc\n      // so the properties for stroking are useless\n      ['color', 'width']));\n      sectors.push(sector);\n      prevEndAngle = endAngle;\n    }\n    sectors.reverse();\n    each(sectors, function (sector) {\n      return group.add(sector);\n    });\n    var getColor = function (percent) {\n      // Less than 0\n      if (percent <= 0) {\n        return colorList[0][1];\n      }\n      var i;\n      for (i = 0; i < colorList.length; i++) {\n        if (colorList[i][0] >= percent && (i === 0 ? 0 : colorList[i - 1][0]) < percent) {\n          return colorList[i][1];\n        }\n      }\n      // More than 1\n      return colorList[i - 1][1];\n    };\n    this._renderTicks(seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth);\n    this._renderTitleAndDetail(seriesModel, ecModel, api, getColor, posInfo);\n    this._renderAnchor(seriesModel, posInfo);\n    this._renderPointer(seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth);\n  };\n  GaugeView.prototype._renderTicks = function (seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth) {\n    var group = this.group;\n    var cx = posInfo.cx;\n    var cy = posInfo.cy;\n    var r = posInfo.r;\n    var minVal = +seriesModel.get('min');\n    var maxVal = +seriesModel.get('max');\n    var splitLineModel = seriesModel.getModel('splitLine');\n    var tickModel = seriesModel.getModel('axisTick');\n    var labelModel = seriesModel.getModel('axisLabel');\n    var splitNumber = seriesModel.get('splitNumber');\n    var subSplitNumber = tickModel.get('splitNumber');\n    var splitLineLen = parsePercent(splitLineModel.get('length'), r);\n    var tickLen = parsePercent(tickModel.get('length'), r);\n    var angle = startAngle;\n    var step = (endAngle - startAngle) / splitNumber;\n    var subStep = step / subSplitNumber;\n    var splitLineStyle = splitLineModel.getModel('lineStyle').getLineStyle();\n    var tickLineStyle = tickModel.getModel('lineStyle').getLineStyle();\n    var splitLineDistance = splitLineModel.get('distance');\n    var unitX;\n    var unitY;\n    for (var i = 0; i <= splitNumber; i++) {\n      unitX = Math.cos(angle);\n      unitY = Math.sin(angle);\n      // Split line\n      if (splitLineModel.get('show')) {\n        var distance = splitLineDistance ? splitLineDistance + axisLineWidth : axisLineWidth;\n        var splitLine = new graphic.Line({\n          shape: {\n            x1: unitX * (r - distance) + cx,\n            y1: unitY * (r - distance) + cy,\n            x2: unitX * (r - splitLineLen - distance) + cx,\n            y2: unitY * (r - splitLineLen - distance) + cy\n          },\n          style: splitLineStyle,\n          silent: true\n        });\n        if (splitLineStyle.stroke === 'auto') {\n          splitLine.setStyle({\n            stroke: getColor(i / splitNumber)\n          });\n        }\n        group.add(splitLine);\n      }\n      // Label\n      if (labelModel.get('show')) {\n        var distance = labelModel.get('distance') + splitLineDistance;\n        var label = formatLabel(round(i / splitNumber * (maxVal - minVal) + minVal), labelModel.get('formatter'));\n        var autoColor = getColor(i / splitNumber);\n        var textStyleX = unitX * (r - splitLineLen - distance) + cx;\n        var textStyleY = unitY * (r - splitLineLen - distance) + cy;\n        var rotateType = labelModel.get('rotate');\n        var rotate = 0;\n        if (rotateType === 'radial') {\n          rotate = -angle + 2 * Math.PI;\n          if (rotate > Math.PI / 2) {\n            rotate += Math.PI;\n          }\n        } else if (rotateType === 'tangential') {\n          rotate = -angle - Math.PI / 2;\n        } else if (isNumber(rotateType)) {\n          rotate = rotateType * Math.PI / 180;\n        }\n        if (rotate === 0) {\n          group.add(new graphic.Text({\n            style: createTextStyle(labelModel, {\n              text: label,\n              x: textStyleX,\n              y: textStyleY,\n              verticalAlign: unitY < -0.8 ? 'top' : unitY > 0.8 ? 'bottom' : 'middle',\n              align: unitX < -0.4 ? 'left' : unitX > 0.4 ? 'right' : 'center'\n            }, {\n              inheritColor: autoColor\n            }),\n            silent: true\n          }));\n        } else {\n          group.add(new graphic.Text({\n            style: createTextStyle(labelModel, {\n              text: label,\n              x: textStyleX,\n              y: textStyleY,\n              verticalAlign: 'middle',\n              align: 'center'\n            }, {\n              inheritColor: autoColor\n            }),\n            silent: true,\n            originX: textStyleX,\n            originY: textStyleY,\n            rotation: rotate\n          }));\n        }\n      }\n      // Axis tick\n      if (tickModel.get('show') && i !== splitNumber) {\n        var distance = tickModel.get('distance');\n        distance = distance ? distance + axisLineWidth : axisLineWidth;\n        for (var j = 0; j <= subSplitNumber; j++) {\n          unitX = Math.cos(angle);\n          unitY = Math.sin(angle);\n          var tickLine = new graphic.Line({\n            shape: {\n              x1: unitX * (r - distance) + cx,\n              y1: unitY * (r - distance) + cy,\n              x2: unitX * (r - tickLen - distance) + cx,\n              y2: unitY * (r - tickLen - distance) + cy\n            },\n            silent: true,\n            style: tickLineStyle\n          });\n          if (tickLineStyle.stroke === 'auto') {\n            tickLine.setStyle({\n              stroke: getColor((i + j / subSplitNumber) / splitNumber)\n            });\n          }\n          group.add(tickLine);\n          angle += subStep;\n        }\n        angle -= subStep;\n      } else {\n        angle += step;\n      }\n    }\n  };\n  GaugeView.prototype._renderPointer = function (seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth) {\n    var group = this.group;\n    var oldData = this._data;\n    var oldProgressData = this._progressEls;\n    var progressList = [];\n    var showPointer = seriesModel.get(['pointer', 'show']);\n    var progressModel = seriesModel.getModel('progress');\n    var showProgress = progressModel.get('show');\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var minVal = +seriesModel.get('min');\n    var maxVal = +seriesModel.get('max');\n    var valueExtent = [minVal, maxVal];\n    var angleExtent = [startAngle, endAngle];\n    function createPointer(idx, angle) {\n      var itemModel = data.getItemModel(idx);\n      var pointerModel = itemModel.getModel('pointer');\n      var pointerWidth = parsePercent(pointerModel.get('width'), posInfo.r);\n      var pointerLength = parsePercent(pointerModel.get('length'), posInfo.r);\n      var pointerStr = seriesModel.get(['pointer', 'icon']);\n      var pointerOffset = pointerModel.get('offsetCenter');\n      var pointerOffsetX = parsePercent(pointerOffset[0], posInfo.r);\n      var pointerOffsetY = parsePercent(pointerOffset[1], posInfo.r);\n      var pointerKeepAspect = pointerModel.get('keepAspect');\n      var pointer;\n      // not exist icon type will be set 'rect'\n      if (pointerStr) {\n        pointer = createSymbol(pointerStr, pointerOffsetX - pointerWidth / 2, pointerOffsetY - pointerLength, pointerWidth, pointerLength, null, pointerKeepAspect);\n      } else {\n        pointer = new PointerPath({\n          shape: {\n            angle: -Math.PI / 2,\n            width: pointerWidth,\n            r: pointerLength,\n            x: pointerOffsetX,\n            y: pointerOffsetY\n          }\n        });\n      }\n      pointer.rotation = -(angle + Math.PI / 2);\n      pointer.x = posInfo.cx;\n      pointer.y = posInfo.cy;\n      return pointer;\n    }\n    function createProgress(idx, endAngle) {\n      var roundCap = progressModel.get('roundCap');\n      var ProgressPath = roundCap ? Sausage : graphic.Sector;\n      var isOverlap = progressModel.get('overlap');\n      var progressWidth = isOverlap ? progressModel.get('width') : axisLineWidth / data.count();\n      var r0 = isOverlap ? posInfo.r - progressWidth : posInfo.r - (idx + 1) * progressWidth;\n      var r = isOverlap ? posInfo.r : posInfo.r - idx * progressWidth;\n      var progress = new ProgressPath({\n        shape: {\n          startAngle: startAngle,\n          endAngle: endAngle,\n          cx: posInfo.cx,\n          cy: posInfo.cy,\n          clockwise: clockwise,\n          r0: r0,\n          r: r\n        }\n      });\n      isOverlap && (progress.z2 = linearMap(data.get(valueDim, idx), [minVal, maxVal], [100, 0], true));\n      return progress;\n    }\n    if (showProgress || showPointer) {\n      data.diff(oldData).add(function (idx) {\n        var val = data.get(valueDim, idx);\n        if (showPointer) {\n          var pointer = createPointer(idx, startAngle);\n          // TODO hide pointer on NaN value?\n          graphic.initProps(pointer, {\n            rotation: -((isNaN(+val) ? angleExtent[0] : linearMap(val, valueExtent, angleExtent, true)) + Math.PI / 2)\n          }, seriesModel);\n          group.add(pointer);\n          data.setItemGraphicEl(idx, pointer);\n        }\n        if (showProgress) {\n          var progress = createProgress(idx, startAngle);\n          var isClip = progressModel.get('clip');\n          graphic.initProps(progress, {\n            shape: {\n              endAngle: linearMap(val, valueExtent, angleExtent, isClip)\n            }\n          }, seriesModel);\n          group.add(progress);\n          // Add data index and series index for indexing the data by element\n          // Useful in tooltip\n          setCommonECData(seriesModel.seriesIndex, data.dataType, idx, progress);\n          progressList[idx] = progress;\n        }\n      }).update(function (newIdx, oldIdx) {\n        var val = data.get(valueDim, newIdx);\n        if (showPointer) {\n          var previousPointer = oldData.getItemGraphicEl(oldIdx);\n          var previousRotate = previousPointer ? previousPointer.rotation : startAngle;\n          var pointer = createPointer(newIdx, previousRotate);\n          pointer.rotation = previousRotate;\n          graphic.updateProps(pointer, {\n            rotation: -((isNaN(+val) ? angleExtent[0] : linearMap(val, valueExtent, angleExtent, true)) + Math.PI / 2)\n          }, seriesModel);\n          group.add(pointer);\n          data.setItemGraphicEl(newIdx, pointer);\n        }\n        if (showProgress) {\n          var previousProgress = oldProgressData[oldIdx];\n          var previousEndAngle = previousProgress ? previousProgress.shape.endAngle : startAngle;\n          var progress = createProgress(newIdx, previousEndAngle);\n          var isClip = progressModel.get('clip');\n          graphic.updateProps(progress, {\n            shape: {\n              endAngle: linearMap(val, valueExtent, angleExtent, isClip)\n            }\n          }, seriesModel);\n          group.add(progress);\n          // Add data index and series index for indexing the data by element\n          // Useful in tooltip\n          setCommonECData(seriesModel.seriesIndex, data.dataType, newIdx, progress);\n          progressList[newIdx] = progress;\n        }\n      }).execute();\n      data.each(function (idx) {\n        var itemModel = data.getItemModel(idx);\n        var emphasisModel = itemModel.getModel('emphasis');\n        var focus = emphasisModel.get('focus');\n        var blurScope = emphasisModel.get('blurScope');\n        var emphasisDisabled = emphasisModel.get('disabled');\n        if (showPointer) {\n          var pointer = data.getItemGraphicEl(idx);\n          var symbolStyle = data.getItemVisual(idx, 'style');\n          var visualColor = symbolStyle.fill;\n          if (pointer instanceof ZRImage) {\n            var pathStyle = pointer.style;\n            pointer.useStyle(extend({\n              image: pathStyle.image,\n              x: pathStyle.x,\n              y: pathStyle.y,\n              width: pathStyle.width,\n              height: pathStyle.height\n            }, symbolStyle));\n          } else {\n            pointer.useStyle(symbolStyle);\n            pointer.type !== 'pointer' && pointer.setColor(visualColor);\n          }\n          pointer.setStyle(itemModel.getModel(['pointer', 'itemStyle']).getItemStyle());\n          if (pointer.style.fill === 'auto') {\n            pointer.setStyle('fill', getColor(linearMap(data.get(valueDim, idx), valueExtent, [0, 1], true)));\n          }\n          pointer.z2EmphasisLift = 0;\n          setStatesStylesFromModel(pointer, itemModel);\n          toggleHoverEmphasis(pointer, focus, blurScope, emphasisDisabled);\n        }\n        if (showProgress) {\n          var progress = progressList[idx];\n          progress.useStyle(data.getItemVisual(idx, 'style'));\n          progress.setStyle(itemModel.getModel(['progress', 'itemStyle']).getItemStyle());\n          progress.z2EmphasisLift = 0;\n          setStatesStylesFromModel(progress, itemModel);\n          toggleHoverEmphasis(progress, focus, blurScope, emphasisDisabled);\n        }\n      });\n      this._progressEls = progressList;\n    }\n  };\n  GaugeView.prototype._renderAnchor = function (seriesModel, posInfo) {\n    var anchorModel = seriesModel.getModel('anchor');\n    var showAnchor = anchorModel.get('show');\n    if (showAnchor) {\n      var anchorSize = anchorModel.get('size');\n      var anchorType = anchorModel.get('icon');\n      var offsetCenter = anchorModel.get('offsetCenter');\n      var anchorKeepAspect = anchorModel.get('keepAspect');\n      var anchor = createSymbol(anchorType, posInfo.cx - anchorSize / 2 + parsePercent(offsetCenter[0], posInfo.r), posInfo.cy - anchorSize / 2 + parsePercent(offsetCenter[1], posInfo.r), anchorSize, anchorSize, null, anchorKeepAspect);\n      anchor.z2 = anchorModel.get('showAbove') ? 1 : 0;\n      anchor.setStyle(anchorModel.getModel('itemStyle').getItemStyle());\n      this.group.add(anchor);\n    }\n  };\n  GaugeView.prototype._renderTitleAndDetail = function (seriesModel, ecModel, api, getColor, posInfo) {\n    var _this = this;\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var minVal = +seriesModel.get('min');\n    var maxVal = +seriesModel.get('max');\n    var contentGroup = new graphic.Group();\n    var newTitleEls = [];\n    var newDetailEls = [];\n    var hasAnimation = seriesModel.isAnimationEnabled();\n    var showPointerAbove = seriesModel.get(['pointer', 'showAbove']);\n    data.diff(this._data).add(function (idx) {\n      newTitleEls[idx] = new graphic.Text({\n        silent: true\n      });\n      newDetailEls[idx] = new graphic.Text({\n        silent: true\n      });\n    }).update(function (idx, oldIdx) {\n      newTitleEls[idx] = _this._titleEls[oldIdx];\n      newDetailEls[idx] = _this._detailEls[oldIdx];\n    }).execute();\n    data.each(function (idx) {\n      var itemModel = data.getItemModel(idx);\n      var value = data.get(valueDim, idx);\n      var itemGroup = new graphic.Group();\n      var autoColor = getColor(linearMap(value, [minVal, maxVal], [0, 1], true));\n      var itemTitleModel = itemModel.getModel('title');\n      if (itemTitleModel.get('show')) {\n        var titleOffsetCenter = itemTitleModel.get('offsetCenter');\n        var titleX = posInfo.cx + parsePercent(titleOffsetCenter[0], posInfo.r);\n        var titleY = posInfo.cy + parsePercent(titleOffsetCenter[1], posInfo.r);\n        var labelEl = newTitleEls[idx];\n        labelEl.attr({\n          z2: showPointerAbove ? 0 : 2,\n          style: createTextStyle(itemTitleModel, {\n            x: titleX,\n            y: titleY,\n            text: data.getName(idx),\n            align: 'center',\n            verticalAlign: 'middle'\n          }, {\n            inheritColor: autoColor\n          })\n        });\n        itemGroup.add(labelEl);\n      }\n      var itemDetailModel = itemModel.getModel('detail');\n      if (itemDetailModel.get('show')) {\n        var detailOffsetCenter = itemDetailModel.get('offsetCenter');\n        var detailX = posInfo.cx + parsePercent(detailOffsetCenter[0], posInfo.r);\n        var detailY = posInfo.cy + parsePercent(detailOffsetCenter[1], posInfo.r);\n        var width = parsePercent(itemDetailModel.get('width'), posInfo.r);\n        var height = parsePercent(itemDetailModel.get('height'), posInfo.r);\n        var detailColor = seriesModel.get(['progress', 'show']) ? data.getItemVisual(idx, 'style').fill : autoColor;\n        var labelEl = newDetailEls[idx];\n        var formatter_1 = itemDetailModel.get('formatter');\n        labelEl.attr({\n          z2: showPointerAbove ? 0 : 2,\n          style: createTextStyle(itemDetailModel, {\n            x: detailX,\n            y: detailY,\n            text: formatLabel(value, formatter_1),\n            width: isNaN(width) ? null : width,\n            height: isNaN(height) ? null : height,\n            align: 'center',\n            verticalAlign: 'middle'\n          }, {\n            inheritColor: detailColor\n          })\n        });\n        setLabelValueAnimation(labelEl, {\n          normal: itemDetailModel\n        }, value, function (value) {\n          return formatLabel(value, formatter_1);\n        });\n        hasAnimation && animateLabelValue(labelEl, idx, data, seriesModel, {\n          getFormattedLabel: function (labelDataIndex, status, dataType, labelDimIndex, fmt, extendParams) {\n            return formatLabel(extendParams ? extendParams.interpolatedValue : value, formatter_1);\n          }\n        });\n        itemGroup.add(labelEl);\n      }\n      contentGroup.add(itemGroup);\n    });\n    this.group.add(contentGroup);\n    this._titleEls = newTitleEls;\n    this._detailEls = newDetailEls;\n  };\n  GaugeView.type = 'gauge';\n  return GaugeView;\n}(ChartView);\nexport default GaugeView;", "map": {"version": 3, "names": ["__extends", "<PERSON><PERSON><PERSON><PERSON>", "graphic", "setStatesStylesFromModel", "toggleHoverEmphasis", "createTextStyle", "setLabelValueAnimation", "animateLabelValue", "ChartView", "parsePercent", "round", "linearMap", "Sausage", "createSymbol", "ZRImage", "extend", "isFunction", "isString", "isNumber", "each", "setCommonECData", "normalizeArcAngles", "parsePosition", "seriesModel", "api", "center", "get", "width", "getWidth", "height", "getHeight", "size", "Math", "min", "cx", "cy", "r", "formatLabel", "value", "labelFormatter", "label", "replace", "GaugeView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "ecModel", "group", "removeAll", "colorList", "posInfo", "_renderMain", "_data", "getData", "dispose", "clockwise", "startAngle", "PI", "endAngle", "axisLineModel", "getModel", "roundCap", "MainPath", "Sector", "showAxis", "lineStyleModel", "axisLineWidth", "angles", "angleRangeSpan", "prevEndAngle", "sectors", "i", "length", "percent", "max", "sector", "shape", "r0", "silent", "setStyle", "fill", "getLineStyle", "push", "reverse", "add", "getColor", "_renderTicks", "_renderTitleAndDetail", "_renderAnchor", "_renderPointer", "minVal", "maxVal", "splitLineModel", "tickModel", "labelModel", "splitNumber", "subSplitNumber", "splitLineLen", "tickLen", "angle", "step", "subStep", "splitLineStyle", "tickLineStyle", "splitLineDistance", "unitX", "unitY", "cos", "sin", "distance", "splitLine", "Line", "x1", "y1", "x2", "y2", "style", "stroke", "autoColor", "textStyleX", "textStyleY", "rotateType", "rotate", "Text", "text", "x", "y", "verticalAlign", "align", "inheritColor", "originX", "originY", "rotation", "j", "tickLine", "oldData", "oldProgressData", "_progressEls", "progressList", "showPointer", "progressModel", "showProgress", "data", "valueDim", "mapDimension", "valueExtent", "angleExtent", "createPointer", "idx", "itemModel", "getItemModel", "pointer<PERSON>odel", "pointer<PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON>", "pointerStr", "pointerOffset", "pointerOffsetX", "pointerOffsetY", "pointer<PERSON>eep<PERSON><PERSON>", "pointer", "createProgress", "ProgressPath", "isOverlap", "progressWidth", "count", "progress", "z2", "diff", "val", "initProps", "isNaN", "setItemGraphicEl", "isClip", "seriesIndex", "dataType", "update", "newIdx", "oldIdx", "previousPointer", "getItemGraphicEl", "previousRotate", "updateProps", "previousProgress", "previousEndAngle", "execute", "emphasisModel", "focus", "blurScope", "emphasisDisabled", "symbolStyle", "getItemVisual", "visualColor", "pathStyle", "useStyle", "image", "setColor", "getItemStyle", "z2EmphasisLift", "anchorModel", "showAnchor", "anchorSize", "anchorType", "offsetCenter", "anchorKeepAspect", "anchor", "contentGroup", "Group", "newTitleEls", "newDetailEls", "hasAnimation", "isAnimationEnabled", "showPointerAbove", "_titleEls", "_detailEls", "itemGroup", "itemTitleModel", "titleOffsetCenter", "titleX", "titleY", "labelEl", "attr", "getName", "itemDetailModel", "detailOffsetCenter", "detailX", "detailY", "detailColor", "formatter_1", "normal", "getFormattedLabel", "labelDataIndex", "status", "labelDimIndex", "fmt", "extendParams", "interpolatedV<PERSON>ue"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/gauge/GaugeView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport PointerPath from './PointerPath.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { createTextStyle, setLabelValueAnimation, animateLabelValue } from '../../label/labelStyle.js';\nimport ChartView from '../../view/Chart.js';\nimport { parsePercent, round, linearMap } from '../../util/number.js';\nimport Sausage from '../../util/shape/sausage.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { extend, isFunction, isString, isNumber, each } from 'zrender/lib/core/util.js';\nimport { setCommonECData } from '../../util/innerStore.js';\nimport { normalizeArcAngles } from 'zrender/lib/core/PathProxy.js';\nfunction parsePosition(seriesModel, api) {\n  var center = seriesModel.get('center');\n  var width = api.getWidth();\n  var height = api.getHeight();\n  var size = Math.min(width, height);\n  var cx = parsePercent(center[0], api.getWidth());\n  var cy = parsePercent(center[1], api.getHeight());\n  var r = parsePercent(seriesModel.get('radius'), size / 2);\n  return {\n    cx: cx,\n    cy: cy,\n    r: r\n  };\n}\nfunction formatLabel(value, labelFormatter) {\n  var label = value == null ? '' : value + '';\n  if (labelFormatter) {\n    if (isString(labelFormatter)) {\n      label = labelFormatter.replace('{value}', label);\n    } else if (isFunction(labelFormatter)) {\n      label = labelFormatter(value);\n    }\n  }\n  return label;\n}\nvar GaugeView = /** @class */function (_super) {\n  __extends(GaugeView, _super);\n  function GaugeView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GaugeView.type;\n    return _this;\n  }\n  GaugeView.prototype.render = function (seriesModel, ecModel, api) {\n    this.group.removeAll();\n    var colorList = seriesModel.get(['axisLine', 'lineStyle', 'color']);\n    var posInfo = parsePosition(seriesModel, api);\n    this._renderMain(seriesModel, ecModel, api, colorList, posInfo);\n    this._data = seriesModel.getData();\n  };\n  GaugeView.prototype.dispose = function () {};\n  GaugeView.prototype._renderMain = function (seriesModel, ecModel, api, colorList, posInfo) {\n    var group = this.group;\n    var clockwise = seriesModel.get('clockwise');\n    var startAngle = -seriesModel.get('startAngle') / 180 * Math.PI;\n    var endAngle = -seriesModel.get('endAngle') / 180 * Math.PI;\n    var axisLineModel = seriesModel.getModel('axisLine');\n    var roundCap = axisLineModel.get('roundCap');\n    var MainPath = roundCap ? Sausage : graphic.Sector;\n    var showAxis = axisLineModel.get('show');\n    var lineStyleModel = axisLineModel.getModel('lineStyle');\n    var axisLineWidth = lineStyleModel.get('width');\n    var angles = [startAngle, endAngle];\n    normalizeArcAngles(angles, !clockwise);\n    startAngle = angles[0];\n    endAngle = angles[1];\n    var angleRangeSpan = endAngle - startAngle;\n    var prevEndAngle = startAngle;\n    var sectors = [];\n    for (var i = 0; showAxis && i < colorList.length; i++) {\n      // Clamp\n      var percent = Math.min(Math.max(colorList[i][0], 0), 1);\n      endAngle = startAngle + angleRangeSpan * percent;\n      var sector = new MainPath({\n        shape: {\n          startAngle: prevEndAngle,\n          endAngle: endAngle,\n          cx: posInfo.cx,\n          cy: posInfo.cy,\n          clockwise: clockwise,\n          r0: posInfo.r - axisLineWidth,\n          r: posInfo.r\n        },\n        silent: true\n      });\n      sector.setStyle({\n        fill: colorList[i][1]\n      });\n      sector.setStyle(lineStyleModel.getLineStyle(\n      // Because we use sector to simulate arc\n      // so the properties for stroking are useless\n      ['color', 'width']));\n      sectors.push(sector);\n      prevEndAngle = endAngle;\n    }\n    sectors.reverse();\n    each(sectors, function (sector) {\n      return group.add(sector);\n    });\n    var getColor = function (percent) {\n      // Less than 0\n      if (percent <= 0) {\n        return colorList[0][1];\n      }\n      var i;\n      for (i = 0; i < colorList.length; i++) {\n        if (colorList[i][0] >= percent && (i === 0 ? 0 : colorList[i - 1][0]) < percent) {\n          return colorList[i][1];\n        }\n      }\n      // More than 1\n      return colorList[i - 1][1];\n    };\n    this._renderTicks(seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth);\n    this._renderTitleAndDetail(seriesModel, ecModel, api, getColor, posInfo);\n    this._renderAnchor(seriesModel, posInfo);\n    this._renderPointer(seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth);\n  };\n  GaugeView.prototype._renderTicks = function (seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth) {\n    var group = this.group;\n    var cx = posInfo.cx;\n    var cy = posInfo.cy;\n    var r = posInfo.r;\n    var minVal = +seriesModel.get('min');\n    var maxVal = +seriesModel.get('max');\n    var splitLineModel = seriesModel.getModel('splitLine');\n    var tickModel = seriesModel.getModel('axisTick');\n    var labelModel = seriesModel.getModel('axisLabel');\n    var splitNumber = seriesModel.get('splitNumber');\n    var subSplitNumber = tickModel.get('splitNumber');\n    var splitLineLen = parsePercent(splitLineModel.get('length'), r);\n    var tickLen = parsePercent(tickModel.get('length'), r);\n    var angle = startAngle;\n    var step = (endAngle - startAngle) / splitNumber;\n    var subStep = step / subSplitNumber;\n    var splitLineStyle = splitLineModel.getModel('lineStyle').getLineStyle();\n    var tickLineStyle = tickModel.getModel('lineStyle').getLineStyle();\n    var splitLineDistance = splitLineModel.get('distance');\n    var unitX;\n    var unitY;\n    for (var i = 0; i <= splitNumber; i++) {\n      unitX = Math.cos(angle);\n      unitY = Math.sin(angle);\n      // Split line\n      if (splitLineModel.get('show')) {\n        var distance = splitLineDistance ? splitLineDistance + axisLineWidth : axisLineWidth;\n        var splitLine = new graphic.Line({\n          shape: {\n            x1: unitX * (r - distance) + cx,\n            y1: unitY * (r - distance) + cy,\n            x2: unitX * (r - splitLineLen - distance) + cx,\n            y2: unitY * (r - splitLineLen - distance) + cy\n          },\n          style: splitLineStyle,\n          silent: true\n        });\n        if (splitLineStyle.stroke === 'auto') {\n          splitLine.setStyle({\n            stroke: getColor(i / splitNumber)\n          });\n        }\n        group.add(splitLine);\n      }\n      // Label\n      if (labelModel.get('show')) {\n        var distance = labelModel.get('distance') + splitLineDistance;\n        var label = formatLabel(round(i / splitNumber * (maxVal - minVal) + minVal), labelModel.get('formatter'));\n        var autoColor = getColor(i / splitNumber);\n        var textStyleX = unitX * (r - splitLineLen - distance) + cx;\n        var textStyleY = unitY * (r - splitLineLen - distance) + cy;\n        var rotateType = labelModel.get('rotate');\n        var rotate = 0;\n        if (rotateType === 'radial') {\n          rotate = -angle + 2 * Math.PI;\n          if (rotate > Math.PI / 2) {\n            rotate += Math.PI;\n          }\n        } else if (rotateType === 'tangential') {\n          rotate = -angle - Math.PI / 2;\n        } else if (isNumber(rotateType)) {\n          rotate = rotateType * Math.PI / 180;\n        }\n        if (rotate === 0) {\n          group.add(new graphic.Text({\n            style: createTextStyle(labelModel, {\n              text: label,\n              x: textStyleX,\n              y: textStyleY,\n              verticalAlign: unitY < -0.8 ? 'top' : unitY > 0.8 ? 'bottom' : 'middle',\n              align: unitX < -0.4 ? 'left' : unitX > 0.4 ? 'right' : 'center'\n            }, {\n              inheritColor: autoColor\n            }),\n            silent: true\n          }));\n        } else {\n          group.add(new graphic.Text({\n            style: createTextStyle(labelModel, {\n              text: label,\n              x: textStyleX,\n              y: textStyleY,\n              verticalAlign: 'middle',\n              align: 'center'\n            }, {\n              inheritColor: autoColor\n            }),\n            silent: true,\n            originX: textStyleX,\n            originY: textStyleY,\n            rotation: rotate\n          }));\n        }\n      }\n      // Axis tick\n      if (tickModel.get('show') && i !== splitNumber) {\n        var distance = tickModel.get('distance');\n        distance = distance ? distance + axisLineWidth : axisLineWidth;\n        for (var j = 0; j <= subSplitNumber; j++) {\n          unitX = Math.cos(angle);\n          unitY = Math.sin(angle);\n          var tickLine = new graphic.Line({\n            shape: {\n              x1: unitX * (r - distance) + cx,\n              y1: unitY * (r - distance) + cy,\n              x2: unitX * (r - tickLen - distance) + cx,\n              y2: unitY * (r - tickLen - distance) + cy\n            },\n            silent: true,\n            style: tickLineStyle\n          });\n          if (tickLineStyle.stroke === 'auto') {\n            tickLine.setStyle({\n              stroke: getColor((i + j / subSplitNumber) / splitNumber)\n            });\n          }\n          group.add(tickLine);\n          angle += subStep;\n        }\n        angle -= subStep;\n      } else {\n        angle += step;\n      }\n    }\n  };\n  GaugeView.prototype._renderPointer = function (seriesModel, ecModel, api, getColor, posInfo, startAngle, endAngle, clockwise, axisLineWidth) {\n    var group = this.group;\n    var oldData = this._data;\n    var oldProgressData = this._progressEls;\n    var progressList = [];\n    var showPointer = seriesModel.get(['pointer', 'show']);\n    var progressModel = seriesModel.getModel('progress');\n    var showProgress = progressModel.get('show');\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var minVal = +seriesModel.get('min');\n    var maxVal = +seriesModel.get('max');\n    var valueExtent = [minVal, maxVal];\n    var angleExtent = [startAngle, endAngle];\n    function createPointer(idx, angle) {\n      var itemModel = data.getItemModel(idx);\n      var pointerModel = itemModel.getModel('pointer');\n      var pointerWidth = parsePercent(pointerModel.get('width'), posInfo.r);\n      var pointerLength = parsePercent(pointerModel.get('length'), posInfo.r);\n      var pointerStr = seriesModel.get(['pointer', 'icon']);\n      var pointerOffset = pointerModel.get('offsetCenter');\n      var pointerOffsetX = parsePercent(pointerOffset[0], posInfo.r);\n      var pointerOffsetY = parsePercent(pointerOffset[1], posInfo.r);\n      var pointerKeepAspect = pointerModel.get('keepAspect');\n      var pointer;\n      // not exist icon type will be set 'rect'\n      if (pointerStr) {\n        pointer = createSymbol(pointerStr, pointerOffsetX - pointerWidth / 2, pointerOffsetY - pointerLength, pointerWidth, pointerLength, null, pointerKeepAspect);\n      } else {\n        pointer = new PointerPath({\n          shape: {\n            angle: -Math.PI / 2,\n            width: pointerWidth,\n            r: pointerLength,\n            x: pointerOffsetX,\n            y: pointerOffsetY\n          }\n        });\n      }\n      pointer.rotation = -(angle + Math.PI / 2);\n      pointer.x = posInfo.cx;\n      pointer.y = posInfo.cy;\n      return pointer;\n    }\n    function createProgress(idx, endAngle) {\n      var roundCap = progressModel.get('roundCap');\n      var ProgressPath = roundCap ? Sausage : graphic.Sector;\n      var isOverlap = progressModel.get('overlap');\n      var progressWidth = isOverlap ? progressModel.get('width') : axisLineWidth / data.count();\n      var r0 = isOverlap ? posInfo.r - progressWidth : posInfo.r - (idx + 1) * progressWidth;\n      var r = isOverlap ? posInfo.r : posInfo.r - idx * progressWidth;\n      var progress = new ProgressPath({\n        shape: {\n          startAngle: startAngle,\n          endAngle: endAngle,\n          cx: posInfo.cx,\n          cy: posInfo.cy,\n          clockwise: clockwise,\n          r0: r0,\n          r: r\n        }\n      });\n      isOverlap && (progress.z2 = linearMap(data.get(valueDim, idx), [minVal, maxVal], [100, 0], true));\n      return progress;\n    }\n    if (showProgress || showPointer) {\n      data.diff(oldData).add(function (idx) {\n        var val = data.get(valueDim, idx);\n        if (showPointer) {\n          var pointer = createPointer(idx, startAngle);\n          // TODO hide pointer on NaN value?\n          graphic.initProps(pointer, {\n            rotation: -((isNaN(+val) ? angleExtent[0] : linearMap(val, valueExtent, angleExtent, true)) + Math.PI / 2)\n          }, seriesModel);\n          group.add(pointer);\n          data.setItemGraphicEl(idx, pointer);\n        }\n        if (showProgress) {\n          var progress = createProgress(idx, startAngle);\n          var isClip = progressModel.get('clip');\n          graphic.initProps(progress, {\n            shape: {\n              endAngle: linearMap(val, valueExtent, angleExtent, isClip)\n            }\n          }, seriesModel);\n          group.add(progress);\n          // Add data index and series index for indexing the data by element\n          // Useful in tooltip\n          setCommonECData(seriesModel.seriesIndex, data.dataType, idx, progress);\n          progressList[idx] = progress;\n        }\n      }).update(function (newIdx, oldIdx) {\n        var val = data.get(valueDim, newIdx);\n        if (showPointer) {\n          var previousPointer = oldData.getItemGraphicEl(oldIdx);\n          var previousRotate = previousPointer ? previousPointer.rotation : startAngle;\n          var pointer = createPointer(newIdx, previousRotate);\n          pointer.rotation = previousRotate;\n          graphic.updateProps(pointer, {\n            rotation: -((isNaN(+val) ? angleExtent[0] : linearMap(val, valueExtent, angleExtent, true)) + Math.PI / 2)\n          }, seriesModel);\n          group.add(pointer);\n          data.setItemGraphicEl(newIdx, pointer);\n        }\n        if (showProgress) {\n          var previousProgress = oldProgressData[oldIdx];\n          var previousEndAngle = previousProgress ? previousProgress.shape.endAngle : startAngle;\n          var progress = createProgress(newIdx, previousEndAngle);\n          var isClip = progressModel.get('clip');\n          graphic.updateProps(progress, {\n            shape: {\n              endAngle: linearMap(val, valueExtent, angleExtent, isClip)\n            }\n          }, seriesModel);\n          group.add(progress);\n          // Add data index and series index for indexing the data by element\n          // Useful in tooltip\n          setCommonECData(seriesModel.seriesIndex, data.dataType, newIdx, progress);\n          progressList[newIdx] = progress;\n        }\n      }).execute();\n      data.each(function (idx) {\n        var itemModel = data.getItemModel(idx);\n        var emphasisModel = itemModel.getModel('emphasis');\n        var focus = emphasisModel.get('focus');\n        var blurScope = emphasisModel.get('blurScope');\n        var emphasisDisabled = emphasisModel.get('disabled');\n        if (showPointer) {\n          var pointer = data.getItemGraphicEl(idx);\n          var symbolStyle = data.getItemVisual(idx, 'style');\n          var visualColor = symbolStyle.fill;\n          if (pointer instanceof ZRImage) {\n            var pathStyle = pointer.style;\n            pointer.useStyle(extend({\n              image: pathStyle.image,\n              x: pathStyle.x,\n              y: pathStyle.y,\n              width: pathStyle.width,\n              height: pathStyle.height\n            }, symbolStyle));\n          } else {\n            pointer.useStyle(symbolStyle);\n            pointer.type !== 'pointer' && pointer.setColor(visualColor);\n          }\n          pointer.setStyle(itemModel.getModel(['pointer', 'itemStyle']).getItemStyle());\n          if (pointer.style.fill === 'auto') {\n            pointer.setStyle('fill', getColor(linearMap(data.get(valueDim, idx), valueExtent, [0, 1], true)));\n          }\n          pointer.z2EmphasisLift = 0;\n          setStatesStylesFromModel(pointer, itemModel);\n          toggleHoverEmphasis(pointer, focus, blurScope, emphasisDisabled);\n        }\n        if (showProgress) {\n          var progress = progressList[idx];\n          progress.useStyle(data.getItemVisual(idx, 'style'));\n          progress.setStyle(itemModel.getModel(['progress', 'itemStyle']).getItemStyle());\n          progress.z2EmphasisLift = 0;\n          setStatesStylesFromModel(progress, itemModel);\n          toggleHoverEmphasis(progress, focus, blurScope, emphasisDisabled);\n        }\n      });\n      this._progressEls = progressList;\n    }\n  };\n  GaugeView.prototype._renderAnchor = function (seriesModel, posInfo) {\n    var anchorModel = seriesModel.getModel('anchor');\n    var showAnchor = anchorModel.get('show');\n    if (showAnchor) {\n      var anchorSize = anchorModel.get('size');\n      var anchorType = anchorModel.get('icon');\n      var offsetCenter = anchorModel.get('offsetCenter');\n      var anchorKeepAspect = anchorModel.get('keepAspect');\n      var anchor = createSymbol(anchorType, posInfo.cx - anchorSize / 2 + parsePercent(offsetCenter[0], posInfo.r), posInfo.cy - anchorSize / 2 + parsePercent(offsetCenter[1], posInfo.r), anchorSize, anchorSize, null, anchorKeepAspect);\n      anchor.z2 = anchorModel.get('showAbove') ? 1 : 0;\n      anchor.setStyle(anchorModel.getModel('itemStyle').getItemStyle());\n      this.group.add(anchor);\n    }\n  };\n  GaugeView.prototype._renderTitleAndDetail = function (seriesModel, ecModel, api, getColor, posInfo) {\n    var _this = this;\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var minVal = +seriesModel.get('min');\n    var maxVal = +seriesModel.get('max');\n    var contentGroup = new graphic.Group();\n    var newTitleEls = [];\n    var newDetailEls = [];\n    var hasAnimation = seriesModel.isAnimationEnabled();\n    var showPointerAbove = seriesModel.get(['pointer', 'showAbove']);\n    data.diff(this._data).add(function (idx) {\n      newTitleEls[idx] = new graphic.Text({\n        silent: true\n      });\n      newDetailEls[idx] = new graphic.Text({\n        silent: true\n      });\n    }).update(function (idx, oldIdx) {\n      newTitleEls[idx] = _this._titleEls[oldIdx];\n      newDetailEls[idx] = _this._detailEls[oldIdx];\n    }).execute();\n    data.each(function (idx) {\n      var itemModel = data.getItemModel(idx);\n      var value = data.get(valueDim, idx);\n      var itemGroup = new graphic.Group();\n      var autoColor = getColor(linearMap(value, [minVal, maxVal], [0, 1], true));\n      var itemTitleModel = itemModel.getModel('title');\n      if (itemTitleModel.get('show')) {\n        var titleOffsetCenter = itemTitleModel.get('offsetCenter');\n        var titleX = posInfo.cx + parsePercent(titleOffsetCenter[0], posInfo.r);\n        var titleY = posInfo.cy + parsePercent(titleOffsetCenter[1], posInfo.r);\n        var labelEl = newTitleEls[idx];\n        labelEl.attr({\n          z2: showPointerAbove ? 0 : 2,\n          style: createTextStyle(itemTitleModel, {\n            x: titleX,\n            y: titleY,\n            text: data.getName(idx),\n            align: 'center',\n            verticalAlign: 'middle'\n          }, {\n            inheritColor: autoColor\n          })\n        });\n        itemGroup.add(labelEl);\n      }\n      var itemDetailModel = itemModel.getModel('detail');\n      if (itemDetailModel.get('show')) {\n        var detailOffsetCenter = itemDetailModel.get('offsetCenter');\n        var detailX = posInfo.cx + parsePercent(detailOffsetCenter[0], posInfo.r);\n        var detailY = posInfo.cy + parsePercent(detailOffsetCenter[1], posInfo.r);\n        var width = parsePercent(itemDetailModel.get('width'), posInfo.r);\n        var height = parsePercent(itemDetailModel.get('height'), posInfo.r);\n        var detailColor = seriesModel.get(['progress', 'show']) ? data.getItemVisual(idx, 'style').fill : autoColor;\n        var labelEl = newDetailEls[idx];\n        var formatter_1 = itemDetailModel.get('formatter');\n        labelEl.attr({\n          z2: showPointerAbove ? 0 : 2,\n          style: createTextStyle(itemDetailModel, {\n            x: detailX,\n            y: detailY,\n            text: formatLabel(value, formatter_1),\n            width: isNaN(width) ? null : width,\n            height: isNaN(height) ? null : height,\n            align: 'center',\n            verticalAlign: 'middle'\n          }, {\n            inheritColor: detailColor\n          })\n        });\n        setLabelValueAnimation(labelEl, {\n          normal: itemDetailModel\n        }, value, function (value) {\n          return formatLabel(value, formatter_1);\n        });\n        hasAnimation && animateLabelValue(labelEl, idx, data, seriesModel, {\n          getFormattedLabel: function (labelDataIndex, status, dataType, labelDimIndex, fmt, extendParams) {\n            return formatLabel(extendParams ? extendParams.interpolatedValue : value, formatter_1);\n          }\n        });\n        itemGroup.add(labelEl);\n      }\n      contentGroup.add(itemGroup);\n    });\n    this.group.add(contentGroup);\n    this._titleEls = newTitleEls;\n    this._detailEls = newDetailEls;\n  };\n  GaugeView.type = 'gauge';\n  return GaugeView;\n}(ChartView);\nexport default GaugeView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACpF,SAASC,eAAe,EAAEC,sBAAsB,EAAEC,iBAAiB,QAAQ,2BAA2B;AACtG,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,YAAY,EAAEC,KAAK,EAAEC,SAAS,QAAQ,sBAAsB;AACrE,OAAOC,OAAO,MAAM,6BAA6B;AACjD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,SAASC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,0BAA0B;AACvF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,aAAaA,CAACC,WAAW,EAAEC,GAAG,EAAE;EACvC,IAAIC,MAAM,GAAGF,WAAW,CAACG,GAAG,CAAC,QAAQ,CAAC;EACtC,IAAIC,KAAK,GAAGH,GAAG,CAACI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAGL,GAAG,CAACM,SAAS,CAAC,CAAC;EAC5B,IAAIC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,EAAEE,MAAM,CAAC;EAClC,IAAIK,EAAE,GAAGzB,YAAY,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAED,GAAG,CAACI,QAAQ,CAAC,CAAC,CAAC;EAChD,IAAIO,EAAE,GAAG1B,YAAY,CAACgB,MAAM,CAAC,CAAC,CAAC,EAAED,GAAG,CAACM,SAAS,CAAC,CAAC,CAAC;EACjD,IAAIM,CAAC,GAAG3B,YAAY,CAACc,WAAW,CAACG,GAAG,CAAC,QAAQ,CAAC,EAAEK,IAAI,GAAG,CAAC,CAAC;EACzD,OAAO;IACLG,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA,EAAE;IACNC,CAAC,EAAEA;EACL,CAAC;AACH;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,cAAc,EAAE;EAC1C,IAAIC,KAAK,GAAGF,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE;EAC3C,IAAIC,cAAc,EAAE;IAClB,IAAItB,QAAQ,CAACsB,cAAc,CAAC,EAAE;MAC5BC,KAAK,GAAGD,cAAc,CAACE,OAAO,CAAC,SAAS,EAAED,KAAK,CAAC;IAClD,CAAC,MAAM,IAAIxB,UAAU,CAACuB,cAAc,CAAC,EAAE;MACrCC,KAAK,GAAGD,cAAc,CAACD,KAAK,CAAC;IAC/B;EACF;EACA,OAAOE,KAAK;AACd;AACA,IAAIE,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7C3C,SAAS,CAAC0C,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAAA,EAAG;IACnB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,SAAS,CAACK,IAAI;IAC3B,OAAOH,KAAK;EACd;EACAF,SAAS,CAACM,SAAS,CAACC,MAAM,GAAG,UAAU1B,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAE;IAChE,IAAI,CAAC2B,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIC,SAAS,GAAG9B,WAAW,CAACG,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IACnE,IAAI4B,OAAO,GAAGhC,aAAa,CAACC,WAAW,EAAEC,GAAG,CAAC;IAC7C,IAAI,CAAC+B,WAAW,CAAChC,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAE6B,SAAS,EAAEC,OAAO,CAAC;IAC/D,IAAI,CAACE,KAAK,GAAGjC,WAAW,CAACkC,OAAO,CAAC,CAAC;EACpC,CAAC;EACDf,SAAS,CAACM,SAAS,CAACU,OAAO,GAAG,YAAY,CAAC,CAAC;EAC5ChB,SAAS,CAACM,SAAS,CAACO,WAAW,GAAG,UAAUhC,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAE6B,SAAS,EAAEC,OAAO,EAAE;IACzF,IAAIH,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIQ,SAAS,GAAGpC,WAAW,CAACG,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIkC,UAAU,GAAG,CAACrC,WAAW,CAACG,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,GAAGM,IAAI,CAAC6B,EAAE;IAC/D,IAAIC,QAAQ,GAAG,CAACvC,WAAW,CAACG,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,GAAGM,IAAI,CAAC6B,EAAE;IAC3D,IAAIE,aAAa,GAAGxC,WAAW,CAACyC,QAAQ,CAAC,UAAU,CAAC;IACpD,IAAIC,QAAQ,GAAGF,aAAa,CAACrC,GAAG,CAAC,UAAU,CAAC;IAC5C,IAAIwC,QAAQ,GAAGD,QAAQ,GAAGrD,OAAO,GAAGV,OAAO,CAACiE,MAAM;IAClD,IAAIC,QAAQ,GAAGL,aAAa,CAACrC,GAAG,CAAC,MAAM,CAAC;IACxC,IAAI2C,cAAc,GAAGN,aAAa,CAACC,QAAQ,CAAC,WAAW,CAAC;IACxD,IAAIM,aAAa,GAAGD,cAAc,CAAC3C,GAAG,CAAC,OAAO,CAAC;IAC/C,IAAI6C,MAAM,GAAG,CAACX,UAAU,EAAEE,QAAQ,CAAC;IACnCzC,kBAAkB,CAACkD,MAAM,EAAE,CAACZ,SAAS,CAAC;IACtCC,UAAU,GAAGW,MAAM,CAAC,CAAC,CAAC;IACtBT,QAAQ,GAAGS,MAAM,CAAC,CAAC,CAAC;IACpB,IAAIC,cAAc,GAAGV,QAAQ,GAAGF,UAAU;IAC1C,IAAIa,YAAY,GAAGb,UAAU;IAC7B,IAAIc,OAAO,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,QAAQ,IAAIO,CAAC,GAAGtB,SAAS,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;MACrD;MACA,IAAIE,OAAO,GAAG7C,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC8C,GAAG,CAACzB,SAAS,CAACsB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACvDb,QAAQ,GAAGF,UAAU,GAAGY,cAAc,GAAGK,OAAO;MAChD,IAAIE,MAAM,GAAG,IAAIb,QAAQ,CAAC;QACxBc,KAAK,EAAE;UACLpB,UAAU,EAAEa,YAAY;UACxBX,QAAQ,EAAEA,QAAQ;UAClB5B,EAAE,EAAEoB,OAAO,CAACpB,EAAE;UACdC,EAAE,EAAEmB,OAAO,CAACnB,EAAE;UACdwB,SAAS,EAAEA,SAAS;UACpBsB,EAAE,EAAE3B,OAAO,CAAClB,CAAC,GAAGkC,aAAa;UAC7BlC,CAAC,EAAEkB,OAAO,CAAClB;QACb,CAAC;QACD8C,MAAM,EAAE;MACV,CAAC,CAAC;MACFH,MAAM,CAACI,QAAQ,CAAC;QACdC,IAAI,EAAE/B,SAAS,CAACsB,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC;MACFI,MAAM,CAACI,QAAQ,CAACd,cAAc,CAACgB,YAAY;MAC3C;MACA;MACA,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;MACpBX,OAAO,CAACY,IAAI,CAACP,MAAM,CAAC;MACpBN,YAAY,GAAGX,QAAQ;IACzB;IACAY,OAAO,CAACa,OAAO,CAAC,CAAC;IACjBpE,IAAI,CAACuD,OAAO,EAAE,UAAUK,MAAM,EAAE;MAC9B,OAAO5B,KAAK,CAACqC,GAAG,CAACT,MAAM,CAAC;IAC1B,CAAC,CAAC;IACF,IAAIU,QAAQ,GAAG,SAAAA,CAAUZ,OAAO,EAAE;MAChC;MACA,IAAIA,OAAO,IAAI,CAAC,EAAE;QAChB,OAAOxB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;MACA,IAAIsB,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,SAAS,CAACuB,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,IAAItB,SAAS,CAACsB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIE,OAAO,IAAI,CAACF,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGtB,SAAS,CAACsB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIE,OAAO,EAAE;UAC/E,OAAOxB,SAAS,CAACsB,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB;MACF;MACA;MACA,OAAOtB,SAAS,CAACsB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAACe,YAAY,CAACnE,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAEiE,QAAQ,EAAEnC,OAAO,EAAEM,UAAU,EAAEE,QAAQ,EAAEH,SAAS,EAAEW,aAAa,CAAC;IAC/G,IAAI,CAACqB,qBAAqB,CAACpE,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAEiE,QAAQ,EAAEnC,OAAO,CAAC;IACxE,IAAI,CAACsC,aAAa,CAACrE,WAAW,EAAE+B,OAAO,CAAC;IACxC,IAAI,CAACuC,cAAc,CAACtE,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAEiE,QAAQ,EAAEnC,OAAO,EAAEM,UAAU,EAAEE,QAAQ,EAAEH,SAAS,EAAEW,aAAa,CAAC;EACnH,CAAC;EACD5B,SAAS,CAACM,SAAS,CAAC0C,YAAY,GAAG,UAAUnE,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAEiE,QAAQ,EAAEnC,OAAO,EAAEM,UAAU,EAAEE,QAAQ,EAAEH,SAAS,EAAEW,aAAa,EAAE;IACzI,IAAInB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIjB,EAAE,GAAGoB,OAAO,CAACpB,EAAE;IACnB,IAAIC,EAAE,GAAGmB,OAAO,CAACnB,EAAE;IACnB,IAAIC,CAAC,GAAGkB,OAAO,CAAClB,CAAC;IACjB,IAAI0D,MAAM,GAAG,CAACvE,WAAW,CAACG,GAAG,CAAC,KAAK,CAAC;IACpC,IAAIqE,MAAM,GAAG,CAACxE,WAAW,CAACG,GAAG,CAAC,KAAK,CAAC;IACpC,IAAIsE,cAAc,GAAGzE,WAAW,CAACyC,QAAQ,CAAC,WAAW,CAAC;IACtD,IAAIiC,SAAS,GAAG1E,WAAW,CAACyC,QAAQ,CAAC,UAAU,CAAC;IAChD,IAAIkC,UAAU,GAAG3E,WAAW,CAACyC,QAAQ,CAAC,WAAW,CAAC;IAClD,IAAImC,WAAW,GAAG5E,WAAW,CAACG,GAAG,CAAC,aAAa,CAAC;IAChD,IAAI0E,cAAc,GAAGH,SAAS,CAACvE,GAAG,CAAC,aAAa,CAAC;IACjD,IAAI2E,YAAY,GAAG5F,YAAY,CAACuF,cAAc,CAACtE,GAAG,CAAC,QAAQ,CAAC,EAAEU,CAAC,CAAC;IAChE,IAAIkE,OAAO,GAAG7F,YAAY,CAACwF,SAAS,CAACvE,GAAG,CAAC,QAAQ,CAAC,EAAEU,CAAC,CAAC;IACtD,IAAImE,KAAK,GAAG3C,UAAU;IACtB,IAAI4C,IAAI,GAAG,CAAC1C,QAAQ,GAAGF,UAAU,IAAIuC,WAAW;IAChD,IAAIM,OAAO,GAAGD,IAAI,GAAGJ,cAAc;IACnC,IAAIM,cAAc,GAAGV,cAAc,CAAChC,QAAQ,CAAC,WAAW,CAAC,CAACqB,YAAY,CAAC,CAAC;IACxE,IAAIsB,aAAa,GAAGV,SAAS,CAACjC,QAAQ,CAAC,WAAW,CAAC,CAACqB,YAAY,CAAC,CAAC;IAClE,IAAIuB,iBAAiB,GAAGZ,cAAc,CAACtE,GAAG,CAAC,UAAU,CAAC;IACtD,IAAImF,KAAK;IACT,IAAIC,KAAK;IACT,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIwB,WAAW,EAAExB,CAAC,EAAE,EAAE;MACrCkC,KAAK,GAAG7E,IAAI,CAAC+E,GAAG,CAACR,KAAK,CAAC;MACvBO,KAAK,GAAG9E,IAAI,CAACgF,GAAG,CAACT,KAAK,CAAC;MACvB;MACA,IAAIP,cAAc,CAACtE,GAAG,CAAC,MAAM,CAAC,EAAE;QAC9B,IAAIuF,QAAQ,GAAGL,iBAAiB,GAAGA,iBAAiB,GAAGtC,aAAa,GAAGA,aAAa;QACpF,IAAI4C,SAAS,GAAG,IAAIhH,OAAO,CAACiH,IAAI,CAAC;UAC/BnC,KAAK,EAAE;YACLoC,EAAE,EAAEP,KAAK,IAAIzE,CAAC,GAAG6E,QAAQ,CAAC,GAAG/E,EAAE;YAC/BmF,EAAE,EAAEP,KAAK,IAAI1E,CAAC,GAAG6E,QAAQ,CAAC,GAAG9E,EAAE;YAC/BmF,EAAE,EAAET,KAAK,IAAIzE,CAAC,GAAGiE,YAAY,GAAGY,QAAQ,CAAC,GAAG/E,EAAE;YAC9CqF,EAAE,EAAET,KAAK,IAAI1E,CAAC,GAAGiE,YAAY,GAAGY,QAAQ,CAAC,GAAG9E;UAC9C,CAAC;UACDqF,KAAK,EAAEd,cAAc;UACrBxB,MAAM,EAAE;QACV,CAAC,CAAC;QACF,IAAIwB,cAAc,CAACe,MAAM,KAAK,MAAM,EAAE;UACpCP,SAAS,CAAC/B,QAAQ,CAAC;YACjBsC,MAAM,EAAEhC,QAAQ,CAACd,CAAC,GAAGwB,WAAW;UAClC,CAAC,CAAC;QACJ;QACAhD,KAAK,CAACqC,GAAG,CAAC0B,SAAS,CAAC;MACtB;MACA;MACA,IAAIhB,UAAU,CAACxE,GAAG,CAAC,MAAM,CAAC,EAAE;QAC1B,IAAIuF,QAAQ,GAAGf,UAAU,CAACxE,GAAG,CAAC,UAAU,CAAC,GAAGkF,iBAAiB;QAC7D,IAAIpE,KAAK,GAAGH,WAAW,CAAC3B,KAAK,CAACiE,CAAC,GAAGwB,WAAW,IAAIJ,MAAM,GAAGD,MAAM,CAAC,GAAGA,MAAM,CAAC,EAAEI,UAAU,CAACxE,GAAG,CAAC,WAAW,CAAC,CAAC;QACzG,IAAIgG,SAAS,GAAGjC,QAAQ,CAACd,CAAC,GAAGwB,WAAW,CAAC;QACzC,IAAIwB,UAAU,GAAGd,KAAK,IAAIzE,CAAC,GAAGiE,YAAY,GAAGY,QAAQ,CAAC,GAAG/E,EAAE;QAC3D,IAAI0F,UAAU,GAAGd,KAAK,IAAI1E,CAAC,GAAGiE,YAAY,GAAGY,QAAQ,CAAC,GAAG9E,EAAE;QAC3D,IAAI0F,UAAU,GAAG3B,UAAU,CAACxE,GAAG,CAAC,QAAQ,CAAC;QACzC,IAAIoG,MAAM,GAAG,CAAC;QACd,IAAID,UAAU,KAAK,QAAQ,EAAE;UAC3BC,MAAM,GAAG,CAACvB,KAAK,GAAG,CAAC,GAAGvE,IAAI,CAAC6B,EAAE;UAC7B,IAAIiE,MAAM,GAAG9F,IAAI,CAAC6B,EAAE,GAAG,CAAC,EAAE;YACxBiE,MAAM,IAAI9F,IAAI,CAAC6B,EAAE;UACnB;QACF,CAAC,MAAM,IAAIgE,UAAU,KAAK,YAAY,EAAE;UACtCC,MAAM,GAAG,CAACvB,KAAK,GAAGvE,IAAI,CAAC6B,EAAE,GAAG,CAAC;QAC/B,CAAC,MAAM,IAAI3C,QAAQ,CAAC2G,UAAU,CAAC,EAAE;UAC/BC,MAAM,GAAGD,UAAU,GAAG7F,IAAI,CAAC6B,EAAE,GAAG,GAAG;QACrC;QACA,IAAIiE,MAAM,KAAK,CAAC,EAAE;UAChB3E,KAAK,CAACqC,GAAG,CAAC,IAAItF,OAAO,CAAC6H,IAAI,CAAC;YACzBP,KAAK,EAAEnH,eAAe,CAAC6F,UAAU,EAAE;cACjC8B,IAAI,EAAExF,KAAK;cACXyF,CAAC,EAAEN,UAAU;cACbO,CAAC,EAAEN,UAAU;cACbO,aAAa,EAAErB,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,GAAGA,KAAK,GAAG,GAAG,GAAG,QAAQ,GAAG,QAAQ;cACvEsB,KAAK,EAAEvB,KAAK,GAAG,CAAC,GAAG,GAAG,MAAM,GAAGA,KAAK,GAAG,GAAG,GAAG,OAAO,GAAG;YACzD,CAAC,EAAE;cACDwB,YAAY,EAAEX;YAChB,CAAC,CAAC;YACFxC,MAAM,EAAE;UACV,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL/B,KAAK,CAACqC,GAAG,CAAC,IAAItF,OAAO,CAAC6H,IAAI,CAAC;YACzBP,KAAK,EAAEnH,eAAe,CAAC6F,UAAU,EAAE;cACjC8B,IAAI,EAAExF,KAAK;cACXyF,CAAC,EAAEN,UAAU;cACbO,CAAC,EAAEN,UAAU;cACbO,aAAa,EAAE,QAAQ;cACvBC,KAAK,EAAE;YACT,CAAC,EAAE;cACDC,YAAY,EAAEX;YAChB,CAAC,CAAC;YACFxC,MAAM,EAAE,IAAI;YACZoD,OAAO,EAAEX,UAAU;YACnBY,OAAO,EAAEX,UAAU;YACnBY,QAAQ,EAAEV;UACZ,CAAC,CAAC,CAAC;QACL;MACF;MACA;MACA,IAAI7B,SAAS,CAACvE,GAAG,CAAC,MAAM,CAAC,IAAIiD,CAAC,KAAKwB,WAAW,EAAE;QAC9C,IAAIc,QAAQ,GAAGhB,SAAS,CAACvE,GAAG,CAAC,UAAU,CAAC;QACxCuF,QAAQ,GAAGA,QAAQ,GAAGA,QAAQ,GAAG3C,aAAa,GAAGA,aAAa;QAC9D,KAAK,IAAImE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIrC,cAAc,EAAEqC,CAAC,EAAE,EAAE;UACxC5B,KAAK,GAAG7E,IAAI,CAAC+E,GAAG,CAACR,KAAK,CAAC;UACvBO,KAAK,GAAG9E,IAAI,CAACgF,GAAG,CAACT,KAAK,CAAC;UACvB,IAAImC,QAAQ,GAAG,IAAIxI,OAAO,CAACiH,IAAI,CAAC;YAC9BnC,KAAK,EAAE;cACLoC,EAAE,EAAEP,KAAK,IAAIzE,CAAC,GAAG6E,QAAQ,CAAC,GAAG/E,EAAE;cAC/BmF,EAAE,EAAEP,KAAK,IAAI1E,CAAC,GAAG6E,QAAQ,CAAC,GAAG9E,EAAE;cAC/BmF,EAAE,EAAET,KAAK,IAAIzE,CAAC,GAAGkE,OAAO,GAAGW,QAAQ,CAAC,GAAG/E,EAAE;cACzCqF,EAAE,EAAET,KAAK,IAAI1E,CAAC,GAAGkE,OAAO,GAAGW,QAAQ,CAAC,GAAG9E;YACzC,CAAC;YACD+C,MAAM,EAAE,IAAI;YACZsC,KAAK,EAAEb;UACT,CAAC,CAAC;UACF,IAAIA,aAAa,CAACc,MAAM,KAAK,MAAM,EAAE;YACnCiB,QAAQ,CAACvD,QAAQ,CAAC;cAChBsC,MAAM,EAAEhC,QAAQ,CAAC,CAACd,CAAC,GAAG8D,CAAC,GAAGrC,cAAc,IAAID,WAAW;YACzD,CAAC,CAAC;UACJ;UACAhD,KAAK,CAACqC,GAAG,CAACkD,QAAQ,CAAC;UACnBnC,KAAK,IAAIE,OAAO;QAClB;QACAF,KAAK,IAAIE,OAAO;MAClB,CAAC,MAAM;QACLF,KAAK,IAAIC,IAAI;MACf;IACF;EACF,CAAC;EACD9D,SAAS,CAACM,SAAS,CAAC6C,cAAc,GAAG,UAAUtE,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAEiE,QAAQ,EAAEnC,OAAO,EAAEM,UAAU,EAAEE,QAAQ,EAAEH,SAAS,EAAEW,aAAa,EAAE;IAC3I,IAAInB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIwF,OAAO,GAAG,IAAI,CAACnF,KAAK;IACxB,IAAIoF,eAAe,GAAG,IAAI,CAACC,YAAY;IACvC,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,WAAW,GAAGxH,WAAW,CAACG,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IACtD,IAAIsH,aAAa,GAAGzH,WAAW,CAACyC,QAAQ,CAAC,UAAU,CAAC;IACpD,IAAIiF,YAAY,GAAGD,aAAa,CAACtH,GAAG,CAAC,MAAM,CAAC;IAC5C,IAAIwH,IAAI,GAAG3H,WAAW,CAACkC,OAAO,CAAC,CAAC;IAChC,IAAI0F,QAAQ,GAAGD,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC;IACzC,IAAItD,MAAM,GAAG,CAACvE,WAAW,CAACG,GAAG,CAAC,KAAK,CAAC;IACpC,IAAIqE,MAAM,GAAG,CAACxE,WAAW,CAACG,GAAG,CAAC,KAAK,CAAC;IACpC,IAAI2H,WAAW,GAAG,CAACvD,MAAM,EAAEC,MAAM,CAAC;IAClC,IAAIuD,WAAW,GAAG,CAAC1F,UAAU,EAAEE,QAAQ,CAAC;IACxC,SAASyF,aAAaA,CAACC,GAAG,EAAEjD,KAAK,EAAE;MACjC,IAAIkD,SAAS,GAAGP,IAAI,CAACQ,YAAY,CAACF,GAAG,CAAC;MACtC,IAAIG,YAAY,GAAGF,SAAS,CAACzF,QAAQ,CAAC,SAAS,CAAC;MAChD,IAAI4F,YAAY,GAAGnJ,YAAY,CAACkJ,YAAY,CAACjI,GAAG,CAAC,OAAO,CAAC,EAAE4B,OAAO,CAAClB,CAAC,CAAC;MACrE,IAAIyH,aAAa,GAAGpJ,YAAY,CAACkJ,YAAY,CAACjI,GAAG,CAAC,QAAQ,CAAC,EAAE4B,OAAO,CAAClB,CAAC,CAAC;MACvE,IAAI0H,UAAU,GAAGvI,WAAW,CAACG,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;MACrD,IAAIqI,aAAa,GAAGJ,YAAY,CAACjI,GAAG,CAAC,cAAc,CAAC;MACpD,IAAIsI,cAAc,GAAGvJ,YAAY,CAACsJ,aAAa,CAAC,CAAC,CAAC,EAAEzG,OAAO,CAAClB,CAAC,CAAC;MAC9D,IAAI6H,cAAc,GAAGxJ,YAAY,CAACsJ,aAAa,CAAC,CAAC,CAAC,EAAEzG,OAAO,CAAClB,CAAC,CAAC;MAC9D,IAAI8H,iBAAiB,GAAGP,YAAY,CAACjI,GAAG,CAAC,YAAY,CAAC;MACtD,IAAIyI,OAAO;MACX;MACA,IAAIL,UAAU,EAAE;QACdK,OAAO,GAAGtJ,YAAY,CAACiJ,UAAU,EAAEE,cAAc,GAAGJ,YAAY,GAAG,CAAC,EAAEK,cAAc,GAAGJ,aAAa,EAAED,YAAY,EAAEC,aAAa,EAAE,IAAI,EAAEK,iBAAiB,CAAC;MAC7J,CAAC,MAAM;QACLC,OAAO,GAAG,IAAIlK,WAAW,CAAC;UACxB+E,KAAK,EAAE;YACLuB,KAAK,EAAE,CAACvE,IAAI,CAAC6B,EAAE,GAAG,CAAC;YACnBlC,KAAK,EAAEiI,YAAY;YACnBxH,CAAC,EAAEyH,aAAa;YAChB5B,CAAC,EAAE+B,cAAc;YACjB9B,CAAC,EAAE+B;UACL;QACF,CAAC,CAAC;MACJ;MACAE,OAAO,CAAC3B,QAAQ,GAAG,EAAEjC,KAAK,GAAGvE,IAAI,CAAC6B,EAAE,GAAG,CAAC,CAAC;MACzCsG,OAAO,CAAClC,CAAC,GAAG3E,OAAO,CAACpB,EAAE;MACtBiI,OAAO,CAACjC,CAAC,GAAG5E,OAAO,CAACnB,EAAE;MACtB,OAAOgI,OAAO;IAChB;IACA,SAASC,cAAcA,CAACZ,GAAG,EAAE1F,QAAQ,EAAE;MACrC,IAAIG,QAAQ,GAAG+E,aAAa,CAACtH,GAAG,CAAC,UAAU,CAAC;MAC5C,IAAI2I,YAAY,GAAGpG,QAAQ,GAAGrD,OAAO,GAAGV,OAAO,CAACiE,MAAM;MACtD,IAAImG,SAAS,GAAGtB,aAAa,CAACtH,GAAG,CAAC,SAAS,CAAC;MAC5C,IAAI6I,aAAa,GAAGD,SAAS,GAAGtB,aAAa,CAACtH,GAAG,CAAC,OAAO,CAAC,GAAG4C,aAAa,GAAG4E,IAAI,CAACsB,KAAK,CAAC,CAAC;MACzF,IAAIvF,EAAE,GAAGqF,SAAS,GAAGhH,OAAO,CAAClB,CAAC,GAAGmI,aAAa,GAAGjH,OAAO,CAAClB,CAAC,GAAG,CAACoH,GAAG,GAAG,CAAC,IAAIe,aAAa;MACtF,IAAInI,CAAC,GAAGkI,SAAS,GAAGhH,OAAO,CAAClB,CAAC,GAAGkB,OAAO,CAAClB,CAAC,GAAGoH,GAAG,GAAGe,aAAa;MAC/D,IAAIE,QAAQ,GAAG,IAAIJ,YAAY,CAAC;QAC9BrF,KAAK,EAAE;UACLpB,UAAU,EAAEA,UAAU;UACtBE,QAAQ,EAAEA,QAAQ;UAClB5B,EAAE,EAAEoB,OAAO,CAACpB,EAAE;UACdC,EAAE,EAAEmB,OAAO,CAACnB,EAAE;UACdwB,SAAS,EAAEA,SAAS;UACpBsB,EAAE,EAAEA,EAAE;UACN7C,CAAC,EAAEA;QACL;MACF,CAAC,CAAC;MACFkI,SAAS,KAAKG,QAAQ,CAACC,EAAE,GAAG/J,SAAS,CAACuI,IAAI,CAACxH,GAAG,CAACyH,QAAQ,EAAEK,GAAG,CAAC,EAAE,CAAC1D,MAAM,EAAEC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;MACjG,OAAO0E,QAAQ;IACjB;IACA,IAAIxB,YAAY,IAAIF,WAAW,EAAE;MAC/BG,IAAI,CAACyB,IAAI,CAAChC,OAAO,CAAC,CAACnD,GAAG,CAAC,UAAUgE,GAAG,EAAE;QACpC,IAAIoB,GAAG,GAAG1B,IAAI,CAACxH,GAAG,CAACyH,QAAQ,EAAEK,GAAG,CAAC;QACjC,IAAIT,WAAW,EAAE;UACf,IAAIoB,OAAO,GAAGZ,aAAa,CAACC,GAAG,EAAE5F,UAAU,CAAC;UAC5C;UACA1D,OAAO,CAAC2K,SAAS,CAACV,OAAO,EAAE;YACzB3B,QAAQ,EAAE,EAAE,CAACsC,KAAK,CAAC,CAACF,GAAG,CAAC,GAAGtB,WAAW,CAAC,CAAC,CAAC,GAAG3I,SAAS,CAACiK,GAAG,EAAEvB,WAAW,EAAEC,WAAW,EAAE,IAAI,CAAC,IAAItH,IAAI,CAAC6B,EAAE,GAAG,CAAC;UAC3G,CAAC,EAAEtC,WAAW,CAAC;UACf4B,KAAK,CAACqC,GAAG,CAAC2E,OAAO,CAAC;UAClBjB,IAAI,CAAC6B,gBAAgB,CAACvB,GAAG,EAAEW,OAAO,CAAC;QACrC;QACA,IAAIlB,YAAY,EAAE;UAChB,IAAIwB,QAAQ,GAAGL,cAAc,CAACZ,GAAG,EAAE5F,UAAU,CAAC;UAC9C,IAAIoH,MAAM,GAAGhC,aAAa,CAACtH,GAAG,CAAC,MAAM,CAAC;UACtCxB,OAAO,CAAC2K,SAAS,CAACJ,QAAQ,EAAE;YAC1BzF,KAAK,EAAE;cACLlB,QAAQ,EAAEnD,SAAS,CAACiK,GAAG,EAAEvB,WAAW,EAAEC,WAAW,EAAE0B,MAAM;YAC3D;UACF,CAAC,EAAEzJ,WAAW,CAAC;UACf4B,KAAK,CAACqC,GAAG,CAACiF,QAAQ,CAAC;UACnB;UACA;UACArJ,eAAe,CAACG,WAAW,CAAC0J,WAAW,EAAE/B,IAAI,CAACgC,QAAQ,EAAE1B,GAAG,EAAEiB,QAAQ,CAAC;UACtE3B,YAAY,CAACU,GAAG,CAAC,GAAGiB,QAAQ;QAC9B;MACF,CAAC,CAAC,CAACU,MAAM,CAAC,UAAUC,MAAM,EAAEC,MAAM,EAAE;QAClC,IAAIT,GAAG,GAAG1B,IAAI,CAACxH,GAAG,CAACyH,QAAQ,EAAEiC,MAAM,CAAC;QACpC,IAAIrC,WAAW,EAAE;UACf,IAAIuC,eAAe,GAAG3C,OAAO,CAAC4C,gBAAgB,CAACF,MAAM,CAAC;UACtD,IAAIG,cAAc,GAAGF,eAAe,GAAGA,eAAe,CAAC9C,QAAQ,GAAG5E,UAAU;UAC5E,IAAIuG,OAAO,GAAGZ,aAAa,CAAC6B,MAAM,EAAEI,cAAc,CAAC;UACnDrB,OAAO,CAAC3B,QAAQ,GAAGgD,cAAc;UACjCtL,OAAO,CAACuL,WAAW,CAACtB,OAAO,EAAE;YAC3B3B,QAAQ,EAAE,EAAE,CAACsC,KAAK,CAAC,CAACF,GAAG,CAAC,GAAGtB,WAAW,CAAC,CAAC,CAAC,GAAG3I,SAAS,CAACiK,GAAG,EAAEvB,WAAW,EAAEC,WAAW,EAAE,IAAI,CAAC,IAAItH,IAAI,CAAC6B,EAAE,GAAG,CAAC;UAC3G,CAAC,EAAEtC,WAAW,CAAC;UACf4B,KAAK,CAACqC,GAAG,CAAC2E,OAAO,CAAC;UAClBjB,IAAI,CAAC6B,gBAAgB,CAACK,MAAM,EAAEjB,OAAO,CAAC;QACxC;QACA,IAAIlB,YAAY,EAAE;UAChB,IAAIyC,gBAAgB,GAAG9C,eAAe,CAACyC,MAAM,CAAC;UAC9C,IAAIM,gBAAgB,GAAGD,gBAAgB,GAAGA,gBAAgB,CAAC1G,KAAK,CAAClB,QAAQ,GAAGF,UAAU;UACtF,IAAI6G,QAAQ,GAAGL,cAAc,CAACgB,MAAM,EAAEO,gBAAgB,CAAC;UACvD,IAAIX,MAAM,GAAGhC,aAAa,CAACtH,GAAG,CAAC,MAAM,CAAC;UACtCxB,OAAO,CAACuL,WAAW,CAAChB,QAAQ,EAAE;YAC5BzF,KAAK,EAAE;cACLlB,QAAQ,EAAEnD,SAAS,CAACiK,GAAG,EAAEvB,WAAW,EAAEC,WAAW,EAAE0B,MAAM;YAC3D;UACF,CAAC,EAAEzJ,WAAW,CAAC;UACf4B,KAAK,CAACqC,GAAG,CAACiF,QAAQ,CAAC;UACnB;UACA;UACArJ,eAAe,CAACG,WAAW,CAAC0J,WAAW,EAAE/B,IAAI,CAACgC,QAAQ,EAAEE,MAAM,EAAEX,QAAQ,CAAC;UACzE3B,YAAY,CAACsC,MAAM,CAAC,GAAGX,QAAQ;QACjC;MACF,CAAC,CAAC,CAACmB,OAAO,CAAC,CAAC;MACZ1C,IAAI,CAAC/H,IAAI,CAAC,UAAUqI,GAAG,EAAE;QACvB,IAAIC,SAAS,GAAGP,IAAI,CAACQ,YAAY,CAACF,GAAG,CAAC;QACtC,IAAIqC,aAAa,GAAGpC,SAAS,CAACzF,QAAQ,CAAC,UAAU,CAAC;QAClD,IAAI8H,KAAK,GAAGD,aAAa,CAACnK,GAAG,CAAC,OAAO,CAAC;QACtC,IAAIqK,SAAS,GAAGF,aAAa,CAACnK,GAAG,CAAC,WAAW,CAAC;QAC9C,IAAIsK,gBAAgB,GAAGH,aAAa,CAACnK,GAAG,CAAC,UAAU,CAAC;QACpD,IAAIqH,WAAW,EAAE;UACf,IAAIoB,OAAO,GAAGjB,IAAI,CAACqC,gBAAgB,CAAC/B,GAAG,CAAC;UACxC,IAAIyC,WAAW,GAAG/C,IAAI,CAACgD,aAAa,CAAC1C,GAAG,EAAE,OAAO,CAAC;UAClD,IAAI2C,WAAW,GAAGF,WAAW,CAAC7G,IAAI;UAClC,IAAI+E,OAAO,YAAYrJ,OAAO,EAAE;YAC9B,IAAIsL,SAAS,GAAGjC,OAAO,CAAC3C,KAAK;YAC7B2C,OAAO,CAACkC,QAAQ,CAACtL,MAAM,CAAC;cACtBuL,KAAK,EAAEF,SAAS,CAACE,KAAK;cACtBrE,CAAC,EAAEmE,SAAS,CAACnE,CAAC;cACdC,CAAC,EAAEkE,SAAS,CAAClE,CAAC;cACdvG,KAAK,EAAEyK,SAAS,CAACzK,KAAK;cACtBE,MAAM,EAAEuK,SAAS,CAACvK;YACpB,CAAC,EAAEoK,WAAW,CAAC,CAAC;UAClB,CAAC,MAAM;YACL9B,OAAO,CAACkC,QAAQ,CAACJ,WAAW,CAAC;YAC7B9B,OAAO,CAACpH,IAAI,KAAK,SAAS,IAAIoH,OAAO,CAACoC,QAAQ,CAACJ,WAAW,CAAC;UAC7D;UACAhC,OAAO,CAAChF,QAAQ,CAACsE,SAAS,CAACzF,QAAQ,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAACwI,YAAY,CAAC,CAAC,CAAC;UAC7E,IAAIrC,OAAO,CAAC3C,KAAK,CAACpC,IAAI,KAAK,MAAM,EAAE;YACjC+E,OAAO,CAAChF,QAAQ,CAAC,MAAM,EAAEM,QAAQ,CAAC9E,SAAS,CAACuI,IAAI,CAACxH,GAAG,CAACyH,QAAQ,EAAEK,GAAG,CAAC,EAAEH,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;UACnG;UACAc,OAAO,CAACsC,cAAc,GAAG,CAAC;UAC1BtM,wBAAwB,CAACgK,OAAO,EAAEV,SAAS,CAAC;UAC5CrJ,mBAAmB,CAAC+J,OAAO,EAAE2B,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;QAClE;QACA,IAAI/C,YAAY,EAAE;UAChB,IAAIwB,QAAQ,GAAG3B,YAAY,CAACU,GAAG,CAAC;UAChCiB,QAAQ,CAAC4B,QAAQ,CAACnD,IAAI,CAACgD,aAAa,CAAC1C,GAAG,EAAE,OAAO,CAAC,CAAC;UACnDiB,QAAQ,CAACtF,QAAQ,CAACsE,SAAS,CAACzF,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAACwI,YAAY,CAAC,CAAC,CAAC;UAC/E/B,QAAQ,CAACgC,cAAc,GAAG,CAAC;UAC3BtM,wBAAwB,CAACsK,QAAQ,EAAEhB,SAAS,CAAC;UAC7CrJ,mBAAmB,CAACqK,QAAQ,EAAEqB,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;QACnE;MACF,CAAC,CAAC;MACF,IAAI,CAACnD,YAAY,GAAGC,YAAY;IAClC;EACF,CAAC;EACDpG,SAAS,CAACM,SAAS,CAAC4C,aAAa,GAAG,UAAUrE,WAAW,EAAE+B,OAAO,EAAE;IAClE,IAAIoJ,WAAW,GAAGnL,WAAW,CAACyC,QAAQ,CAAC,QAAQ,CAAC;IAChD,IAAI2I,UAAU,GAAGD,WAAW,CAAChL,GAAG,CAAC,MAAM,CAAC;IACxC,IAAIiL,UAAU,EAAE;MACd,IAAIC,UAAU,GAAGF,WAAW,CAAChL,GAAG,CAAC,MAAM,CAAC;MACxC,IAAImL,UAAU,GAAGH,WAAW,CAAChL,GAAG,CAAC,MAAM,CAAC;MACxC,IAAIoL,YAAY,GAAGJ,WAAW,CAAChL,GAAG,CAAC,cAAc,CAAC;MAClD,IAAIqL,gBAAgB,GAAGL,WAAW,CAAChL,GAAG,CAAC,YAAY,CAAC;MACpD,IAAIsL,MAAM,GAAGnM,YAAY,CAACgM,UAAU,EAAEvJ,OAAO,CAACpB,EAAE,GAAG0K,UAAU,GAAG,CAAC,GAAGnM,YAAY,CAACqM,YAAY,CAAC,CAAC,CAAC,EAAExJ,OAAO,CAAClB,CAAC,CAAC,EAAEkB,OAAO,CAACnB,EAAE,GAAGyK,UAAU,GAAG,CAAC,GAAGnM,YAAY,CAACqM,YAAY,CAAC,CAAC,CAAC,EAAExJ,OAAO,CAAClB,CAAC,CAAC,EAAEwK,UAAU,EAAEA,UAAU,EAAE,IAAI,EAAEG,gBAAgB,CAAC;MACrOC,MAAM,CAACtC,EAAE,GAAGgC,WAAW,CAAChL,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;MAChDsL,MAAM,CAAC7H,QAAQ,CAACuH,WAAW,CAAC1I,QAAQ,CAAC,WAAW,CAAC,CAACwI,YAAY,CAAC,CAAC,CAAC;MACjE,IAAI,CAACrJ,KAAK,CAACqC,GAAG,CAACwH,MAAM,CAAC;IACxB;EACF,CAAC;EACDtK,SAAS,CAACM,SAAS,CAAC2C,qBAAqB,GAAG,UAAUpE,WAAW,EAAE2B,OAAO,EAAE1B,GAAG,EAAEiE,QAAQ,EAAEnC,OAAO,EAAE;IAClG,IAAIV,KAAK,GAAG,IAAI;IAChB,IAAIsG,IAAI,GAAG3H,WAAW,CAACkC,OAAO,CAAC,CAAC;IAChC,IAAI0F,QAAQ,GAAGD,IAAI,CAACE,YAAY,CAAC,OAAO,CAAC;IACzC,IAAItD,MAAM,GAAG,CAACvE,WAAW,CAACG,GAAG,CAAC,KAAK,CAAC;IACpC,IAAIqE,MAAM,GAAG,CAACxE,WAAW,CAACG,GAAG,CAAC,KAAK,CAAC;IACpC,IAAIuL,YAAY,GAAG,IAAI/M,OAAO,CAACgN,KAAK,CAAC,CAAC;IACtC,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,YAAY,GAAG9L,WAAW,CAAC+L,kBAAkB,CAAC,CAAC;IACnD,IAAIC,gBAAgB,GAAGhM,WAAW,CAACG,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IAChEwH,IAAI,CAACyB,IAAI,CAAC,IAAI,CAACnH,KAAK,CAAC,CAACgC,GAAG,CAAC,UAAUgE,GAAG,EAAE;MACvC2D,WAAW,CAAC3D,GAAG,CAAC,GAAG,IAAItJ,OAAO,CAAC6H,IAAI,CAAC;QAClC7C,MAAM,EAAE;MACV,CAAC,CAAC;MACFkI,YAAY,CAAC5D,GAAG,CAAC,GAAG,IAAItJ,OAAO,CAAC6H,IAAI,CAAC;QACnC7C,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,CAACiG,MAAM,CAAC,UAAU3B,GAAG,EAAE6B,MAAM,EAAE;MAC/B8B,WAAW,CAAC3D,GAAG,CAAC,GAAG5G,KAAK,CAAC4K,SAAS,CAACnC,MAAM,CAAC;MAC1C+B,YAAY,CAAC5D,GAAG,CAAC,GAAG5G,KAAK,CAAC6K,UAAU,CAACpC,MAAM,CAAC;IAC9C,CAAC,CAAC,CAACO,OAAO,CAAC,CAAC;IACZ1C,IAAI,CAAC/H,IAAI,CAAC,UAAUqI,GAAG,EAAE;MACvB,IAAIC,SAAS,GAAGP,IAAI,CAACQ,YAAY,CAACF,GAAG,CAAC;MACtC,IAAIlH,KAAK,GAAG4G,IAAI,CAACxH,GAAG,CAACyH,QAAQ,EAAEK,GAAG,CAAC;MACnC,IAAIkE,SAAS,GAAG,IAAIxN,OAAO,CAACgN,KAAK,CAAC,CAAC;MACnC,IAAIxF,SAAS,GAAGjC,QAAQ,CAAC9E,SAAS,CAAC2B,KAAK,EAAE,CAACwD,MAAM,EAAEC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;MAC1E,IAAI4H,cAAc,GAAGlE,SAAS,CAACzF,QAAQ,CAAC,OAAO,CAAC;MAChD,IAAI2J,cAAc,CAACjM,GAAG,CAAC,MAAM,CAAC,EAAE;QAC9B,IAAIkM,iBAAiB,GAAGD,cAAc,CAACjM,GAAG,CAAC,cAAc,CAAC;QAC1D,IAAImM,MAAM,GAAGvK,OAAO,CAACpB,EAAE,GAAGzB,YAAY,CAACmN,iBAAiB,CAAC,CAAC,CAAC,EAAEtK,OAAO,CAAClB,CAAC,CAAC;QACvE,IAAI0L,MAAM,GAAGxK,OAAO,CAACnB,EAAE,GAAG1B,YAAY,CAACmN,iBAAiB,CAAC,CAAC,CAAC,EAAEtK,OAAO,CAAClB,CAAC,CAAC;QACvE,IAAI2L,OAAO,GAAGZ,WAAW,CAAC3D,GAAG,CAAC;QAC9BuE,OAAO,CAACC,IAAI,CAAC;UACXtD,EAAE,EAAE6C,gBAAgB,GAAG,CAAC,GAAG,CAAC;UAC5B/F,KAAK,EAAEnH,eAAe,CAACsN,cAAc,EAAE;YACrC1F,CAAC,EAAE4F,MAAM;YACT3F,CAAC,EAAE4F,MAAM;YACT9F,IAAI,EAAEkB,IAAI,CAAC+E,OAAO,CAACzE,GAAG,CAAC;YACvBpB,KAAK,EAAE,QAAQ;YACfD,aAAa,EAAE;UACjB,CAAC,EAAE;YACDE,YAAY,EAAEX;UAChB,CAAC;QACH,CAAC,CAAC;QACFgG,SAAS,CAAClI,GAAG,CAACuI,OAAO,CAAC;MACxB;MACA,IAAIG,eAAe,GAAGzE,SAAS,CAACzF,QAAQ,CAAC,QAAQ,CAAC;MAClD,IAAIkK,eAAe,CAACxM,GAAG,CAAC,MAAM,CAAC,EAAE;QAC/B,IAAIyM,kBAAkB,GAAGD,eAAe,CAACxM,GAAG,CAAC,cAAc,CAAC;QAC5D,IAAI0M,OAAO,GAAG9K,OAAO,CAACpB,EAAE,GAAGzB,YAAY,CAAC0N,kBAAkB,CAAC,CAAC,CAAC,EAAE7K,OAAO,CAAClB,CAAC,CAAC;QACzE,IAAIiM,OAAO,GAAG/K,OAAO,CAACnB,EAAE,GAAG1B,YAAY,CAAC0N,kBAAkB,CAAC,CAAC,CAAC,EAAE7K,OAAO,CAAClB,CAAC,CAAC;QACzE,IAAIT,KAAK,GAAGlB,YAAY,CAACyN,eAAe,CAACxM,GAAG,CAAC,OAAO,CAAC,EAAE4B,OAAO,CAAClB,CAAC,CAAC;QACjE,IAAIP,MAAM,GAAGpB,YAAY,CAACyN,eAAe,CAACxM,GAAG,CAAC,QAAQ,CAAC,EAAE4B,OAAO,CAAClB,CAAC,CAAC;QACnE,IAAIkM,WAAW,GAAG/M,WAAW,CAACG,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,GAAGwH,IAAI,CAACgD,aAAa,CAAC1C,GAAG,EAAE,OAAO,CAAC,CAACpE,IAAI,GAAGsC,SAAS;QAC3G,IAAIqG,OAAO,GAAGX,YAAY,CAAC5D,GAAG,CAAC;QAC/B,IAAI+E,WAAW,GAAGL,eAAe,CAACxM,GAAG,CAAC,WAAW,CAAC;QAClDqM,OAAO,CAACC,IAAI,CAAC;UACXtD,EAAE,EAAE6C,gBAAgB,GAAG,CAAC,GAAG,CAAC;UAC5B/F,KAAK,EAAEnH,eAAe,CAAC6N,eAAe,EAAE;YACtCjG,CAAC,EAAEmG,OAAO;YACVlG,CAAC,EAAEmG,OAAO;YACVrG,IAAI,EAAE3F,WAAW,CAACC,KAAK,EAAEiM,WAAW,CAAC;YACrC5M,KAAK,EAAEmJ,KAAK,CAACnJ,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK;YAClCE,MAAM,EAAEiJ,KAAK,CAACjJ,MAAM,CAAC,GAAG,IAAI,GAAGA,MAAM;YACrCuG,KAAK,EAAE,QAAQ;YACfD,aAAa,EAAE;UACjB,CAAC,EAAE;YACDE,YAAY,EAAEiG;UAChB,CAAC;QACH,CAAC,CAAC;QACFhO,sBAAsB,CAACyN,OAAO,EAAE;UAC9BS,MAAM,EAAEN;QACV,CAAC,EAAE5L,KAAK,EAAE,UAAUA,KAAK,EAAE;UACzB,OAAOD,WAAW,CAACC,KAAK,EAAEiM,WAAW,CAAC;QACxC,CAAC,CAAC;QACFlB,YAAY,IAAI9M,iBAAiB,CAACwN,OAAO,EAAEvE,GAAG,EAAEN,IAAI,EAAE3H,WAAW,EAAE;UACjEkN,iBAAiB,EAAE,SAAAA,CAAUC,cAAc,EAAEC,MAAM,EAAEzD,QAAQ,EAAE0D,aAAa,EAAEC,GAAG,EAAEC,YAAY,EAAE;YAC/F,OAAOzM,WAAW,CAACyM,YAAY,GAAGA,YAAY,CAACC,iBAAiB,GAAGzM,KAAK,EAAEiM,WAAW,CAAC;UACxF;QACF,CAAC,CAAC;QACFb,SAAS,CAAClI,GAAG,CAACuI,OAAO,CAAC;MACxB;MACAd,YAAY,CAACzH,GAAG,CAACkI,SAAS,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAACvK,KAAK,CAACqC,GAAG,CAACyH,YAAY,CAAC;IAC5B,IAAI,CAACO,SAAS,GAAGL,WAAW;IAC5B,IAAI,CAACM,UAAU,GAAGL,YAAY;EAChC,CAAC;EACD1K,SAAS,CAACK,IAAI,GAAG,OAAO;EACxB,OAAOL,SAAS;AAClB,CAAC,CAAClC,SAAS,CAAC;AACZ,eAAekC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}