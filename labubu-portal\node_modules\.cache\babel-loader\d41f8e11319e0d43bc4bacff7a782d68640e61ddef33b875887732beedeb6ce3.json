{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createVNode as _createVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"min-h-screen bg-gray-50 font-sans\"\n};\nconst _hoisted_2 = {\n  class: \"sticky top-0 z-50 bg-white bg-opacity-90 backdrop-blur-sm shadow-sm\"\n};\nconst _hoisted_3 = {\n  class: \"container mx-auto px-4 py-3 flex items-center justify-between\"\n};\nconst _hoisted_4 = {\n  class: \"flex items-center\"\n};\nconst _hoisted_5 = [\"src\"];\nconst _hoisted_6 = {\n  class: \"hidden md:flex space-x-8\"\n};\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = {\n  class: \"relative overflow-hidden\",\n  style: {\n    \"min-height\": \"500px\"\n  }\n};\nconst _hoisted_9 = {\n  class: \"absolute inset-0 z-0\"\n};\nconst _hoisted_10 = [\"src\"];\nconst _hoisted_11 = {\n  key: 0,\n  class: \"py-16 bg-white\"\n};\nconst _hoisted_12 = {\n  class: \"container mx-auto px-4\"\n};\nconst _hoisted_13 = {\n  class: \"mb-16\"\n};\nconst _hoisted_14 = {\n  class: \"relative h-96\"\n};\nconst _hoisted_15 = [\"src\", \"alt\"];\nconst _hoisted_16 = {\n  class: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6\"\n};\nconst _hoisted_17 = {\n  class: \"text-2xl font-bold text-white mb-2\"\n};\nconst _hoisted_18 = {\n  class: \"text-white text-opacity-90\"\n};\nconst _hoisted_19 = {\n  class: \"grid grid-cols-1 md:grid-cols-3 gap-8\"\n};\nconst _hoisted_20 = {\n  class: \"p-6\"\n};\nconst _hoisted_21 = {\n  class: \"text-xl font-bold mb-4 text-gray-800 flex items-center\"\n};\nconst _hoisted_22 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_23 = {\n  href: \"#\",\n  class: \"text-gray-700 hover:text-pink-500 flex items-start cursor-pointer\"\n};\nconst _hoisted_24 = {\n  key: 1,\n  class: \"py-16 bg-gray-50\"\n};\nconst _hoisted_25 = {\n  class: \"container mx-auto px-4\"\n};\nconst _hoisted_26 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\"\n};\nconst _hoisted_27 = {\n  class: \"h-48 overflow-hidden\"\n};\nconst _hoisted_28 = [\"src\", \"alt\"];\nconst _hoisted_29 = {\n  class: \"p-6\"\n};\nconst _hoisted_30 = {\n  class: \"text-xl font-bold mb-3 text-gray-800\"\n};\nconst _hoisted_31 = {\n  class: \"text-gray-600 mb-4\"\n};\nconst _hoisted_32 = {\n  class: \"bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition w-full\"\n};\nconst _hoisted_33 = {\n  class: \"mt-16 bg-white rounded-xl overflow-hidden shadow-lg\"\n};\nconst _hoisted_34 = {\n  class: \"grid grid-cols-1 md:grid-cols-2\"\n};\nconst _hoisted_35 = {\n  class: \"p-8\"\n};\nconst _hoisted_36 = {\n  class: \"space-y-3\"\n};\nconst _hoisted_37 = {\n  class: \"text-gray-700\"\n};\nconst _hoisted_38 = {\n  class: \"relative h-full min-h-[300px]\"\n};\nconst _hoisted_39 = [\"src\"];\nconst _hoisted_40 = {\n  key: 2,\n  class: \"py-16 bg-white\"\n};\nconst _hoisted_41 = {\n  class: \"container mx-auto px-4\"\n};\nconst _hoisted_42 = {\n  class: \"mb-16\"\n};\nconst _hoisted_43 = {\n  class: \"grid grid-cols-2 md:grid-cols-4 gap-4\"\n};\nconst _hoisted_44 = [\"src\", \"alt\"];\nconst _hoisted_45 = {\n  class: \"absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-100 transition flex flex-col justify-end p-4\"\n};\nconst _hoisted_46 = {\n  class: \"text-white font-bold\"\n};\nconst _hoisted_47 = {\n  class: \"text-white text-sm\"\n};\nconst _hoisted_48 = {\n  class: \"grid grid-cols-1 md:grid-cols-3 gap-8\"\n};\nconst _hoisted_49 = {\n  class: \"col-span-2\"\n};\nconst _hoisted_50 = {\n  class: \"space-y-6\"\n};\nconst _hoisted_51 = {\n  class: \"flex items-start\"\n};\nconst _hoisted_52 = [\"src\"];\nconst _hoisted_53 = {\n  class: \"flex-1\"\n};\nconst _hoisted_54 = {\n  class: \"font-bold text-gray-800\"\n};\nconst _hoisted_55 = {\n  class: \"text-gray-600 mt-2\"\n};\nconst _hoisted_56 = {\n  class: \"flex items-center mt-4 text-sm text-gray-500\"\n};\nconst _hoisted_57 = {\n  class: \"bg-gray-50 rounded-xl p-6 shadow-md\"\n};\nconst _hoisted_58 = {\n  class: \"space-y-6\"\n};\nconst _hoisted_59 = {\n  class: \"flex items-start mb-3\"\n};\nconst _hoisted_60 = {\n  class: \"font-medium text-gray-800\"\n};\nconst _hoisted_61 = {\n  class: \"flex items-start ml-9\"\n};\nconst _hoisted_62 = {\n  class: \"text-gray-600\"\n};\nconst _hoisted_63 = {\n  key: 3,\n  class: \"py-16 bg-gray-50\"\n};\nconst _hoisted_64 = {\n  class: \"container mx-auto px-4\"\n};\nconst _hoisted_65 = {\n  class: \"bg-white rounded-xl shadow-lg p-8 mb-12\"\n};\nconst _hoisted_66 = {\n  class: \"grid grid-cols-1 md:grid-cols-3 gap-6\"\n};\nconst _hoisted_67 = {\n  class: \"flex items-center mb-4\"\n};\nconst _hoisted_68 = {\n  class: \"text-xl font-bold text-gray-800\"\n};\nconst _hoisted_69 = {\n  class: \"text-gray-600 mb-4\"\n};\nconst _hoisted_70 = {\n  class: \"flex items-center text-sm text-gray-500\"\n};\nconst _hoisted_71 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12\"\n};\nconst _hoisted_72 = {\n  class: \"bg-white rounded-xl shadow-lg p-6\"\n};\nconst _hoisted_73 = {\n  ref: \"priceChart\",\n  class: \"h-80\"\n};\nconst _hoisted_74 = {\n  class: \"bg-white rounded-xl shadow-lg p-6\"\n};\nconst _hoisted_75 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_76 = {\n  class: \"bg-pink-100 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n};\nconst _hoisted_77 = {\n  class: \"text-pink-500 font-bold\"\n};\nconst _hoisted_78 = {\n  class: \"font-bold text-gray-800\"\n};\nconst _hoisted_79 = {\n  class: \"text-gray-600\"\n};\nconst _hoisted_80 = {\n  class: \"bg-white rounded-xl shadow-lg p-8\"\n};\nconst _hoisted_81 = {\n  class: \"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\"\n};\nconst _hoisted_82 = {\n  class: \"space-y-4\"\n};\nconst _hoisted_83 = {\n  class: \"font-bold text-red-600 mb-1\"\n};\nconst _hoisted_84 = {\n  class: \"text-gray-700\"\n};\nconst _hoisted_85 = {\n  class: \"relative h-80\"\n};\nconst _hoisted_86 = [\"src\"];\nconst _hoisted_87 = {\n  class: \"bg-gray-800 text-white py-12\"\n};\nconst _hoisted_88 = {\n  class: \"container mx-auto px-4\"\n};\nconst _hoisted_89 = {\n  class: \"grid grid-cols-1 md:grid-cols-4 gap-8\"\n};\nconst _hoisted_90 = {\n  class: \"space-y-2\"\n};\nconst _hoisted_91 = {\n  href: \"#\",\n  class: \"text-gray-400 hover:text-pink-300 cursor-pointer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_swiper_slide = _resolveComponent(\"swiper-slide\");\n  const _component_swiper = _resolveComponent(\"swiper\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 导航栏 \"), _createElementVNode(\"header\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Logo \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"img\", {\n    src: $data.logoUrl,\n    alt: \"Labubu Logo\",\n    class: \"h-12 mr-3\"\n  }, null, 8 /* PROPS */, _hoisted_5), _cache[0] || (_cache[0] = _createElementVNode(\"h1\", {\n    class: \"text-2xl font-bold text-pink-500\"\n  }, \"Labubu Portal\", -1 /* HOISTED */))]), _createCommentVNode(\" 主导航 \"), _createElementVNode(\"nav\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.navItems, (item, index) => {\n    return _openBlock(), _createElementBlock(\"a\", {\n      key: index,\n      class: _normalizeClass(['nav-link cursor-pointer whitespace-nowrap', $data.activeNav === item.id ? 'text-pink-500 font-bold' : 'text-gray-600 hover:text-pink-400']),\n      onClick: $event => $data.activeNav = item.id\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass(['mr-2', item.icon])\n    }, null, 2 /* CLASS */), _createTextVNode(_toDisplayString(item.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 搜索和用户中心 \"), _cache[1] || (_cache[1] = _createStaticVNode(\"<div class=\\\"flex items-center space-x-4\\\" data-v-7ba5bd90><div class=\\\"relative\\\" data-v-7ba5bd90><input type=\\\"text\\\" placeholder=\\\"搜索 Labubu 内容...\\\" class=\\\"pl-10 pr-4 py-2 rounded-full border-none bg-gray-100 text-sm focus:ring-2 focus:ring-pink-300 focus:outline-none w-40 md:w-64\\\" data-v-7ba5bd90><i class=\\\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\\\" data-v-7ba5bd90></i></div><button class=\\\"bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\\\" data-v-7ba5bd90><i class=\\\"fas fa-user mr-2\\\" data-v-7ba5bd90></i>登录 </button></div>\", 1))])]), _createCommentVNode(\" 主内容区 \"), _createElementVNode(\"main\", null, [_createCommentVNode(\" Hero 区域 \"), _createElementVNode(\"section\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"img\", {\n    src: $data.heroBackgroundUrl,\n    alt: \"Labubu 背景\",\n    class: \"w-full h-full object-cover object-top\"\n  }, null, 8 /* PROPS */, _hoisted_10)]), _cache[2] || (_cache[2] = _createStaticVNode(\"<div class=\\\"container mx-auto px-4 py-20 relative z-10 flex flex-col md:flex-row items-center\\\" data-v-7ba5bd90><div class=\\\"md:w-1/2 text-white bg-pink-500 bg-opacity-80 p-8 rounded-lg backdrop-blur-sm\\\" data-v-7ba5bd90><h2 class=\\\"text-4xl font-bold mb-4\\\" data-v-7ba5bd90>欢迎来到 Labubu 世界</h2><p class=\\\"text-lg mb-6\\\" data-v-7ba5bd90> 探索可爱的 Labubu 收藏品，获取最新资讯，与收藏家社区交流互动 </p><div class=\\\"flex space-x-4\\\" data-v-7ba5bd90><button class=\\\"bg-white text-pink-500 px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-gray-100 transition font-bold\\\" data-v-7ba5bd90><i class=\\\"fas fa-book-open mr-2\\\" data-v-7ba5bd90></i>收藏指南 </button><button class=\\\"bg-transparent border-2 border-white text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-white hover:bg-opacity-20 transition\\\" data-v-7ba5bd90><i class=\\\"fas fa-shopping-cart mr-2\\\" data-v-7ba5bd90></i>购买渠道 </button></div></div></div>\", 1))]), _createCommentVNode(\" 最新资讯区 \"), $data.activeNav === 'news' ? (_openBlock(), _createElementBlock(\"section\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[6] || (_cache[6] = _createElementVNode(\"h2\", {\n    class: \"text-3xl font-bold text-center mb-12 text-gray-800\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-newspaper text-pink-500 mr-2\"\n  }), _createTextVNode(\"最新资讯 \")], -1 /* HOISTED */)), _createCommentVNode(\" 轮播图 \"), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_swiper, {\n    class: \"rounded-xl overflow-hidden shadow-lg\",\n    options: $data.swiperOptions\n  }, {\n    default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.newsSlides, (slide, index) => {\n      return _openBlock(), _createBlock(_component_swiper_slide, {\n        key: index\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"img\", {\n          src: slide.image,\n          alt: slide.title,\n          class: \"w-full h-full object-cover object-top\"\n        }, null, 8 /* PROPS */, _hoisted_15), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"h3\", _hoisted_17, _toDisplayString(slide.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_18, _toDisplayString(slide.description), 1 /* TEXT */), _cache[3] || (_cache[3] = _createElementVNode(\"button\", {\n          class: \"mt-4 bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n        }, \" 阅读更多 \", -1 /* HOISTED */))])])]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */);\n    }), 128 /* KEYED_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"options\"])]), _createCommentVNode(\" 资讯卡片区 \"), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.newsCategories, (category, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"bg-gray-50 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition\"\n    }, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"h3\", _hoisted_21, [_createElementVNode(\"i\", {\n      class: _normalizeClass(['mr-2 text-pink-500', category.icon])\n    }, null, 2 /* CLASS */), _createTextVNode(\" \" + _toDisplayString(category.title), 1 /* TEXT */)]), _createElementVNode(\"ul\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(category.items, (item, itemIndex) => {\n      return _openBlock(), _createElementBlock(\"li\", {\n        key: itemIndex,\n        class: \"border-b border-gray-200 pb-3 last:border-0\"\n      }, [_createElementVNode(\"a\", _hoisted_23, [_cache[4] || (_cache[4] = _createElementVNode(\"span\", {\n        class: \"text-pink-400 mr-2\"\n      }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString(item), 1 /* TEXT */)])]);\n    }), 128 /* KEYED_FRAGMENT */))]), _cache[5] || (_cache[5] = _createElementVNode(\"button\", {\n      class: \"mt-4 text-pink-500 flex items-center cursor-pointer whitespace-nowrap hover:text-pink-600\"\n    }, [_createTextVNode(\" 查看更多 \"), _createElementVNode(\"i\", {\n      class: \"fas fa-arrow-right ml-2\"\n    })], -1 /* HOISTED */))])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 收藏指南区 \"), $data.activeNav === 'guide' ? (_openBlock(), _createElementBlock(\"section\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_cache[12] || (_cache[12] = _createElementVNode(\"h2\", {\n    class: \"text-3xl font-bold text-center mb-12 text-gray-800\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-book text-pink-500 mr-2\"\n  }), _createTextVNode(\"收藏指南 \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_26, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.collectionGuides, (guide, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition transform hover:-translate-y-1\"\n    }, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"img\", {\n      src: guide.image,\n      alt: guide.title,\n      class: \"w-full h-full object-cover object-top\"\n    }, null, 8 /* PROPS */, _hoisted_28)]), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"h3\", _hoisted_30, _toDisplayString(guide.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_31, _toDisplayString(guide.description), 1 /* TEXT */), _createElementVNode(\"button\", _hoisted_32, [_createElementVNode(\"i\", {\n      class: _normalizeClass(['mr-2', guide.icon])\n    }, null, 2 /* CLASS */), _cache[7] || (_cache[7] = _createTextVNode(\"查看详情 \"))])])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 鉴别技巧特别区域 \"), _createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_cache[9] || (_cache[9] = _createElementVNode(\"h3\", {\n    class: \"text-2xl font-bold mb-4 text-gray-800\"\n  }, \" 真伪鉴别技巧 \", -1 /* HOISTED */)), _cache[10] || (_cache[10] = _createElementVNode(\"p\", {\n    class: \"text-gray-600 mb-6\"\n  }, \" 了解如何辨别 Labubu 正品与仿制品，保护您的收藏投资。 \", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_36, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.authenticationTips, (tip, index) => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: index,\n      class: \"flex items-start\"\n    }, [_cache[8] || (_cache[8] = _createElementVNode(\"i\", {\n      class: \"fas fa-check-circle text-green-500 mt-1 mr-3\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_37, _toDisplayString(tip), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))]), _cache[11] || (_cache[11] = _createElementVNode(\"button\", {\n    class: \"mt-6 bg-pink-500 text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n  }, \" 查看完整鉴别指南 \", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"img\", {\n    src: $data.authenticationImage,\n    alt: \"真伪鉴别\",\n    class: \"absolute inset-0 w-full h-full object-cover object-top\"\n  }, null, 8 /* PROPS */, _hoisted_39)])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 社区讨论区 \"), $data.activeNav === 'community' ? (_openBlock(), _createElementBlock(\"section\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_cache[25] || (_cache[25] = _createElementVNode(\"h2\", {\n    class: \"text-3xl font-bold text-center mb-12 text-gray-800\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-users text-pink-500 mr-2\"\n  }), _createTextVNode(\"社区讨论 \")], -1 /* HOISTED */)), _createCommentVNode(\" 收藏品展示 \"), _createElementVNode(\"div\", _hoisted_42, [_cache[13] || (_cache[13] = _createElementVNode(\"h3\", {\n    class: \"text-2xl font-bold mb-8 text-gray-800\"\n  }, \"收藏品展示\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_43, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.communityShowcase, (item, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"relative group overflow-hidden rounded-lg shadow-md cursor-pointer\"\n    }, [_createElementVNode(\"img\", {\n      src: item.image,\n      alt: item.title,\n      class: \"w-full h-64 object-cover object-top transition transform group-hover:scale-105\"\n    }, null, 8 /* PROPS */, _hoisted_44), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"h4\", _hoisted_46, _toDisplayString(item.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_47, \"分享者: \" + _toDisplayString(item.user), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n    class: \"text-center mt-8\"\n  }, [_createElementVNode(\"button\", {\n    class: \"bg-pink-500 text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n  }, \" 分享我的收藏 \")], -1 /* HOISTED */))]), _createCommentVNode(\" 热门讨论 \"), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_cache[20] || (_cache[20] = _createElementVNode(\"h3\", {\n    class: \"text-2xl font-bold mb-6 text-gray-800\"\n  }, \"热门讨论\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_50, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.communityDiscussions, (discussion, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"bg-gray-50 rounded-xl p-6 shadow-md hover:shadow-lg transition cursor-pointer\"\n    }, [_createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"img\", {\n      src: discussion.userAvatar,\n      alt: \"用户头像\",\n      class: \"w-12 h-12 rounded-full mr-4\"\n    }, null, 8 /* PROPS */, _hoisted_52), _createElementVNode(\"div\", _hoisted_53, [_createElementVNode(\"h4\", _hoisted_54, _toDisplayString(discussion.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_55, _toDisplayString(discussion.preview), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"span\", null, _toDisplayString(discussion.user), 1 /* TEXT */), _cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n      class: \"mx-2\"\n    }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString(discussion.date), 1 /* TEXT */), _cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n      class: \"mx-2\"\n    }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, [_cache[15] || (_cache[15] = _createElementVNode(\"i\", {\n      class: \"far fa-comment mr-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(_toDisplayString(discussion.comments), 1 /* TEXT */)]), _cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n      class: \"mx-2\"\n    }, \"•\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, [_cache[16] || (_cache[16] = _createElementVNode(\"i\", {\n      class: \"far fa-heart mr-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(_toDisplayString(discussion.likes), 1 /* TEXT */)])])])])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 专家问答 \"), _createElementVNode(\"div\", null, [_cache[24] || (_cache[24] = _createElementVNode(\"h3\", {\n    class: \"text-2xl font-bold mb-6 text-gray-800\"\n  }, \"专家问答\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.expertQA, (qa, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"border-b border-gray-200 pb-4 last:border-0\"\n    }, [_createElementVNode(\"div\", _hoisted_59, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n      class: \"bg-pink-100 text-pink-500 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n    }, \" Q \", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_60, _toDisplayString(qa.question), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_61, [_cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n      class: \"bg-blue-100 text-blue-500 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n    }, \" A \", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_62, _toDisplayString(qa.answer), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _cache[23] || (_cache[23] = _createElementVNode(\"button\", {\n    class: \"mt-4 w-full bg-gray-200 text-gray-700 px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-gray-300 transition\"\n  }, \" 提问专家 \", -1 /* HOISTED */))])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 购买指南区 \"), $data.activeNav === 'purchase' ? (_openBlock(), _createElementBlock(\"section\", _hoisted_63, [_createElementVNode(\"div\", _hoisted_64, [_cache[32] || (_cache[32] = _createElementVNode(\"h2\", {\n    class: \"text-3xl font-bold text-center mb-12 text-gray-800\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-shopping-bag text-pink-500 mr-2\"\n  }), _createTextVNode(\"购买指南 \")], -1 /* HOISTED */)), _createCommentVNode(\" 官方渠道 \"), _createElementVNode(\"div\", _hoisted_65, [_cache[27] || (_cache[27] = _createElementVNode(\"h3\", {\n    class: \"text-2xl font-bold mb-6 text-gray-800\"\n  }, \"官方渠道推荐\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_66, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.officialChannels, (channel, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"border border-gray-200 rounded-lg p-6 hover:border-pink-300 transition cursor-pointer\"\n    }, [_createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"i\", {\n      class: _normalizeClass(['text-2xl mr-3 text-pink-500', channel.icon])\n    }, null, 2 /* CLASS */), _createElementVNode(\"h4\", _hoisted_68, _toDisplayString(channel.name), 1 /* TEXT */)]), _createElementVNode(\"p\", _hoisted_69, _toDisplayString(channel.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_70, [_cache[26] || (_cache[26] = _createElementVNode(\"i\", {\n      class: \"fas fa-check-circle text-green-500 mr-2\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString(channel.verification), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createCommentVNode(\" 价格趋势 \"), _createElementVNode(\"div\", _hoisted_71, [_createElementVNode(\"div\", _hoisted_72, [_cache[28] || (_cache[28] = _createElementVNode(\"h3\", {\n    class: \"text-xl font-bold mb-6 text-gray-800\"\n  }, \"价格趋势分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_73, null, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_74, [_cache[29] || (_cache[29] = _createElementVNode(\"h3\", {\n    class: \"text-xl font-bold mb-6 text-gray-800\"\n  }, \"购买注意事项\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_75, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.purchaseTips, (tip, index) => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: index,\n      class: \"flex items-start\"\n    }, [_createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"span\", _hoisted_77, _toDisplayString(index + 1), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_createElementVNode(\"h4\", _hoisted_78, _toDisplayString(tip.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_79, _toDisplayString(tip.content), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])]), _createCommentVNode(\" 防骗指南 \"), _createElementVNode(\"div\", _hoisted_80, [_cache[31] || (_cache[31] = _createElementVNode(\"h3\", {\n    class: \"text-2xl font-bold mb-6 text-gray-800\"\n  }, \"防骗指南\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_81, [_createElementVNode(\"div\", null, [_cache[30] || (_cache[30] = _createElementVNode(\"p\", {\n    class: \"text-gray-600 mb-6\"\n  }, \" 在购买 Labubu 收藏品时，请警惕以下常见骗局，保护您的权益。 \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_82, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.antiScamGuide, (scam, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: \"bg-red-50 border-l-4 border-red-500 p-4\"\n    }, [_createElementVNode(\"h4\", _hoisted_83, _toDisplayString(scam.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_84, _toDisplayString(scam.description), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_85, [_createElementVNode(\"img\", {\n    src: $data.antiScamImage,\n    alt: \"防骗指南\",\n    class: \"w-full h-full object-cover object-top rounded-lg\"\n  }, null, 8 /* PROPS */, _hoisted_86)])])])])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 页脚 \"), _createElementVNode(\"footer\", _hoisted_87, [_createElementVNode(\"div\", _hoisted_88, [_createElementVNode(\"div\", _hoisted_89, [_cache[36] || (_cache[36] = _createElementVNode(\"div\", null, [_createElementVNode(\"h3\", {\n    class: \"text-xl font-bold mb-4\"\n  }, \"关于 Labubu\"), _createElementVNode(\"p\", {\n    class: \"text-gray-400\"\n  }, \" Labubu 是一款备受喜爱的可爱卡通收藏品，以其独特的设计和高品质而闻名。 \")], -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_cache[35] || (_cache[35] = _createElementVNode(\"h3\", {\n    class: \"text-xl font-bold mb-4\"\n  }, \"快速链接\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_90, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.navItems, (item, index) => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: index\n    }, [_createElementVNode(\"a\", _hoisted_91, _toDisplayString(item.name), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */)), _cache[33] || (_cache[33] = _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"text-gray-400 hover:text-pink-300 cursor-pointer\"\n  }, \"关于我们\")], -1 /* HOISTED */)), _cache[34] || (_cache[34] = _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"text-gray-400 hover:text-pink-300 cursor-pointer\"\n  }, \"联系我们\")], -1 /* HOISTED */))])]), _cache[37] || (_cache[37] = _createStaticVNode(\"<div data-v-7ba5bd90><h3 class=\\\"text-xl font-bold mb-4\\\" data-v-7ba5bd90>联系方式</h3><ul class=\\\"space-y-2 text-gray-400\\\" data-v-7ba5bd90><li class=\\\"flex items-center\\\" data-v-7ba5bd90><i class=\\\"fas fa-envelope mr-2 text-pink-400\\\" data-v-7ba5bd90></i> <EMAIL> </li><li class=\\\"flex items-center\\\" data-v-7ba5bd90><i class=\\\"fas fa-phone mr-2 text-pink-400\\\" data-v-7ba5bd90></i> +86 123 4567 8910 </li><li class=\\\"flex items-center\\\" data-v-7ba5bd90><i class=\\\"fas fa-map-marker-alt mr-2 text-pink-400\\\" data-v-7ba5bd90></i> 上海市浦东新区 </li></ul></div><div data-v-7ba5bd90><h3 class=\\\"text-xl font-bold mb-4\\\" data-v-7ba5bd90>关注我们</h3><div class=\\\"flex space-x-4\\\" data-v-7ba5bd90><a href=\\\"#\\\" class=\\\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\\\" data-v-7ba5bd90><i class=\\\"fab fa-weibo\\\" data-v-7ba5bd90></i></a><a href=\\\"#\\\" class=\\\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\\\" data-v-7ba5bd90><i class=\\\"fab fa-weixin\\\" data-v-7ba5bd90></i></a><a href=\\\"#\\\" class=\\\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\\\" data-v-7ba5bd90><i class=\\\"fab fa-instagram\\\" data-v-7ba5bd90></i></a><a href=\\\"#\\\" class=\\\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\\\" data-v-7ba5bd90><i class=\\\"fab fa-bilibili\\\" data-v-7ba5bd90></i></a></div><div class=\\\"mt-4\\\" data-v-7ba5bd90><p class=\\\"text-gray-400\\\" data-v-7ba5bd90>支付方式</p><div class=\\\"flex space-x-3 mt-2\\\" data-v-7ba5bd90><i class=\\\"fab fa-alipay text-2xl text-gray-400\\\" data-v-7ba5bd90></i><i class=\\\"fab fa-weixin text-2xl text-gray-400\\\" data-v-7ba5bd90></i><i class=\\\"fab fa-cc-visa text-2xl text-gray-400\\\" data-v-7ba5bd90></i><i class=\\\"fab fa-cc-mastercard text-2xl text-gray-400\\\" data-v-7ba5bd90></i><i class=\\\"fab fa-paypal text-2xl text-gray-400\\\" data-v-7ba5bd90></i></div></div></div>\", 2))]), _cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n    class: \"border-t border-gray-700 mt-8 pt-8 text-center text-gray-500\"\n  }, [_createElementVNode(\"p\", null, \"© 2025 Labubu Portal. 保留所有权利。\")], -1 /* HOISTED */))])])]);\n}", "map": {"version": 3, "names": ["class", "style", "href", "ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "src", "$data", "logoUrl", "alt", "_hoisted_6", "_Fragment", "_renderList", "navItems", "item", "index", "key", "_normalizeClass", "activeNav", "id", "onClick", "$event", "icon", "name", "_hoisted_8", "_hoisted_9", "heroBackgroundUrl", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_createVNode", "_component_swiper", "options", "swiperOptions", "newsSlides", "slide", "_createBlock", "_component_swiper_slide", "_hoisted_14", "image", "title", "_hoisted_16", "_hoisted_17", "_toDisplayString", "_hoisted_18", "description", "_hoisted_19", "newsCategories", "category", "_hoisted_20", "_hoisted_21", "_hoisted_22", "items", "itemIndex", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "collectionGuides", "guide", "_hoisted_27", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "authenticationTips", "tip", "_hoisted_37", "_hoisted_38", "authenticationImage", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "communityShowcase", "_hoisted_45", "_hoisted_46", "_hoisted_47", "user", "_hoisted_48", "_hoisted_49", "_hoisted_50", "communityDiscussions", "discussion", "_hoisted_51", "userAvatar", "_hoisted_53", "_hoisted_54", "_hoisted_55", "preview", "_hoisted_56", "date", "comments", "likes", "_hoisted_57", "_hoisted_58", "expertQA", "qa", "_hoisted_59", "_hoisted_60", "question", "_hoisted_61", "_hoisted_62", "answer", "_hoisted_63", "_hoisted_64", "_hoisted_65", "_hoisted_66", "officialChannels", "channel", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "verification", "_hoisted_71", "_hoisted_72", "_hoisted_73", "_hoisted_74", "_hoisted_75", "purchaseTips", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "content", "_hoisted_80", "_hoisted_81", "_hoisted_82", "antiScamGuide", "scam", "_hoisted_83", "_hoisted_84", "_hoisted_85", "antiScamImage", "_hoisted_87", "_hoisted_88", "_hoisted_89", "_hoisted_90", "_hoisted_91"], "sources": ["C:\\suibianwanwan\\labubu-portal\\src\\App.vue"], "sourcesContent": ["<template>\n  <div class=\"min-h-screen bg-gray-50 font-sans\">\n    <!-- 导航栏 -->\n    <header\n      class=\"sticky top-0 z-50 bg-white bg-opacity-90 backdrop-blur-sm shadow-sm\"\n    >\n      <div\n        class=\"container mx-auto px-4 py-3 flex items-center justify-between\"\n      >\n        <!-- Logo -->\n        <div class=\"flex items-center\">\n          <img :src=\"logoUrl\" alt=\"Labubu Logo\" class=\"h-12 mr-3\" />\n          <h1 class=\"text-2xl font-bold text-pink-500\">Labubu Portal</h1>\n        </div>\n\n        <!-- 主导航 -->\n        <nav class=\"hidden md:flex space-x-8\">\n          <a\n            v-for=\"(item, index) in navItems\"\n            :key=\"index\"\n            :class=\"['nav-link cursor-pointer whitespace-nowrap', activeNav === item.id ? 'text-pink-500 font-bold' : 'text-gray-600 hover:text-pink-400']\"\n            @click=\"activeNav = item.id\"\n          >\n            <i :class=\"['mr-2', item.icon]\"></i>{{ item.name }}\n          </a>\n        </nav>\n\n        <!-- 搜索和用户中心 -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"relative\">\n            <input\n              type=\"text\"\n              placeholder=\"搜索 Labubu 内容...\"\n              class=\"pl-10 pr-4 py-2 rounded-full border-none bg-gray-100 text-sm focus:ring-2 focus:ring-pink-300 focus:outline-none w-40 md:w-64\"\n            />\n            <i\n              class=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n            ></i>\n          </div>\n          <button\n            class=\"bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n          >\n            <i class=\"fas fa-user mr-2\"></i>登录\n          </button>\n        </div>\n      </div>\n    </header>\n\n    <!-- 主内容区 -->\n    <main>\n      <!-- Hero 区域 -->\n      <section class=\"relative overflow-hidden\" style=\"min-height: 500px;\">\n        <div class=\"absolute inset-0 z-0\">\n          <img\n            :src=\"heroBackgroundUrl\"\n            alt=\"Labubu 背景\"\n            class=\"w-full h-full object-cover object-top\"\n          />\n        </div>\n        <div\n          class=\"container mx-auto px-4 py-20 relative z-10 flex flex-col md:flex-row items-center\"\n        >\n          <div\n            class=\"md:w-1/2 text-white bg-pink-500 bg-opacity-80 p-8 rounded-lg backdrop-blur-sm\"\n          >\n            <h2 class=\"text-4xl font-bold mb-4\">欢迎来到 Labubu 世界</h2>\n            <p class=\"text-lg mb-6\">\n              探索可爱的 Labubu 收藏品，获取最新资讯，与收藏家社区交流互动\n            </p>\n            <div class=\"flex space-x-4\">\n              <button\n                class=\"bg-white text-pink-500 px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-gray-100 transition font-bold\"\n              >\n                <i class=\"fas fa-book-open mr-2\"></i>收藏指南\n              </button>\n              <button\n                class=\"bg-transparent border-2 border-white text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-white hover:bg-opacity-20 transition\"\n              >\n                <i class=\"fas fa-shopping-cart mr-2\"></i>购买渠道\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 最新资讯区 -->\n      <section v-if=\"activeNav === 'news'\" class=\"py-16 bg-white\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-newspaper text-pink-500 mr-2\"></i>最新资讯\n          </h2>\n\n          <!-- 轮播图 -->\n          <div class=\"mb-16\">\n            <swiper\n              class=\"rounded-xl overflow-hidden shadow-lg\"\n              :options=\"swiperOptions\"\n            >\n              <swiper-slide v-for=\"(slide, index) in newsSlides\" :key=\"index\">\n                <div class=\"relative h-96\">\n                  <img\n                    :src=\"slide.image\"\n                    :alt=\"slide.title\"\n                    class=\"w-full h-full object-cover object-top\"\n                  />\n                  <div\n                    class=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6\"\n                  >\n                    <h3 class=\"text-2xl font-bold text-white mb-2\">\n                      {{ slide.title }}\n                    </h3>\n                    <p class=\"text-white text-opacity-90\">\n                      {{ slide.description }}\n                    </p>\n                    <button\n                      class=\"mt-4 bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n                    >\n                      阅读更多\n                    </button>\n                  </div>\n                </div>\n              </swiper-slide>\n            </swiper>\n          </div>\n\n          <!-- 资讯卡片区 -->\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div\n              v-for=\"(category, index) in newsCategories\"\n              :key=\"index\"\n              class=\"bg-gray-50 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition\"\n            >\n              <div class=\"p-6\">\n                <h3\n                  class=\"text-xl font-bold mb-4 text-gray-800 flex items-center\"\n                >\n                  <i :class=\"['mr-2 text-pink-500', category.icon]\"></i>\n                  {{ category.title }}\n                </h3>\n                <ul class=\"space-y-4\">\n                  <li\n                    v-for=\"(item, itemIndex) in category.items\"\n                    :key=\"itemIndex\"\n                    class=\"border-b border-gray-200 pb-3 last:border-0\"\n                  >\n                    <a\n                      href=\"#\"\n                      class=\"text-gray-700 hover:text-pink-500 flex items-start cursor-pointer\"\n                    >\n                      <span class=\"text-pink-400 mr-2\">•</span>\n                      <span>{{ item }}</span>\n                    </a>\n                  </li>\n                </ul>\n                <button\n                  class=\"mt-4 text-pink-500 flex items-center cursor-pointer whitespace-nowrap hover:text-pink-600\"\n                >\n                  查看更多 <i class=\"fas fa-arrow-right ml-2\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 收藏指南区 -->\n      <section v-if=\"activeNav === 'guide'\" class=\"py-16 bg-gray-50\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-book text-pink-500 mr-2\"></i>收藏指南\n          </h2>\n\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <div\n              v-for=\"(guide, index) in collectionGuides\"\n              :key=\"index\"\n              class=\"bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition transform hover:-translate-y-1\"\n            >\n              <div class=\"h-48 overflow-hidden\">\n                <img\n                  :src=\"guide.image\"\n                  :alt=\"guide.title\"\n                  class=\"w-full h-full object-cover object-top\"\n                />\n              </div>\n              <div class=\"p-6\">\n                <h3 class=\"text-xl font-bold mb-3 text-gray-800\">\n                  {{ guide.title }}\n                </h3>\n                <p class=\"text-gray-600 mb-4\">{{ guide.description }}</p>\n                <button\n                  class=\"bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition w-full\"\n                >\n                  <i :class=\"['mr-2', guide.icon]\"></i>查看详情\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 鉴别技巧特别区域 -->\n          <div class=\"mt-16 bg-white rounded-xl overflow-hidden shadow-lg\">\n            <div class=\"grid grid-cols-1 md:grid-cols-2\">\n              <div class=\"p-8\">\n                <h3 class=\"text-2xl font-bold mb-4 text-gray-800\">\n                  真伪鉴别技巧\n                </h3>\n                <p class=\"text-gray-600 mb-6\">\n                  了解如何辨别 Labubu 正品与仿制品，保护您的收藏投资。\n                </p>\n                <ul class=\"space-y-3\">\n                  <li\n                    v-for=\"(tip, index) in authenticationTips\"\n                    :key=\"index\"\n                    class=\"flex items-start\"\n                  >\n                    <i class=\"fas fa-check-circle text-green-500 mt-1 mr-3\"></i>\n                    <span class=\"text-gray-700\">{{ tip }}</span>\n                  </li>\n                </ul>\n                <button\n                  class=\"mt-6 bg-pink-500 text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n                >\n                  查看完整鉴别指南\n                </button>\n              </div>\n              <div class=\"relative h-full min-h-[300px]\">\n                <img\n                  :src=\"authenticationImage\"\n                  alt=\"真伪鉴别\"\n                  class=\"absolute inset-0 w-full h-full object-cover object-top\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 社区讨论区 -->\n      <section v-if=\"activeNav === 'community'\" class=\"py-16 bg-white\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-users text-pink-500 mr-2\"></i>社区讨论\n          </h2>\n\n          <!-- 收藏品展示 -->\n          <div class=\"mb-16\">\n            <h3 class=\"text-2xl font-bold mb-8 text-gray-800\">收藏品展示</h3>\n            <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div\n                v-for=\"(item, index) in communityShowcase\"\n                :key=\"index\"\n                class=\"relative group overflow-hidden rounded-lg shadow-md cursor-pointer\"\n              >\n                <img\n                  :src=\"item.image\"\n                  :alt=\"item.title\"\n                  class=\"w-full h-64 object-cover object-top transition transform group-hover:scale-105\"\n                />\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-100 transition flex flex-col justify-end p-4\"\n                >\n                  <h4 class=\"text-white font-bold\">{{ item.title }}</h4>\n                  <p class=\"text-white text-sm\">分享者: {{ item.user }}</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"text-center mt-8\">\n              <button\n                class=\"bg-pink-500 text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n              >\n                分享我的收藏\n              </button>\n            </div>\n          </div>\n\n          <!-- 热门讨论 -->\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div class=\"col-span-2\">\n              <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">热门讨论</h3>\n              <div class=\"space-y-6\">\n                <div\n                  v-for=\"(discussion, index) in communityDiscussions\"\n                  :key=\"index\"\n                  class=\"bg-gray-50 rounded-xl p-6 shadow-md hover:shadow-lg transition cursor-pointer\"\n                >\n                  <div class=\"flex items-start\">\n                    <img\n                      :src=\"discussion.userAvatar\"\n                      alt=\"用户头像\"\n                      class=\"w-12 h-12 rounded-full mr-4\"\n                    />\n                    <div class=\"flex-1\">\n                      <h4 class=\"font-bold text-gray-800\">\n                        {{ discussion.title }}\n                      </h4>\n                      <p class=\"text-gray-600 mt-2\">{{ discussion.preview }}</p>\n                      <div class=\"flex items-center mt-4 text-sm text-gray-500\">\n                        <span>{{ discussion.user }}</span>\n                        <span class=\"mx-2\">•</span>\n                        <span>{{ discussion.date }}</span>\n                        <span class=\"mx-2\">•</span>\n                        <span\n                          ><i class=\"far fa-comment mr-1\"></i>{{\n                          discussion.comments }}</span\n                        >\n                        <span class=\"mx-2\">•</span>\n                        <span\n                          ><i class=\"far fa-heart mr-1\"></i>{{ discussion.likes\n                          }}</span\n                        >\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 专家问答 -->\n            <div>\n              <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">专家问答</h3>\n              <div class=\"bg-gray-50 rounded-xl p-6 shadow-md\">\n                <div class=\"space-y-6\">\n                  <div\n                    v-for=\"(qa, index) in expertQA\"\n                    :key=\"index\"\n                    class=\"border-b border-gray-200 pb-4 last:border-0\"\n                  >\n                    <div class=\"flex items-start mb-3\">\n                      <div\n                        class=\"bg-pink-100 text-pink-500 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n                      >\n                        Q\n                      </div>\n                      <p class=\"font-medium text-gray-800\">{{ qa.question }}</p>\n                    </div>\n                    <div class=\"flex items-start ml-9\">\n                      <div\n                        class=\"bg-blue-100 text-blue-500 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n                      >\n                        A\n                      </div>\n                      <p class=\"text-gray-600\">{{ qa.answer }}</p>\n                    </div>\n                  </div>\n                </div>\n                <button\n                  class=\"mt-4 w-full bg-gray-200 text-gray-700 px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-gray-300 transition\"\n                >\n                  提问专家\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 购买指南区 -->\n      <section v-if=\"activeNav === 'purchase'\" class=\"py-16 bg-gray-50\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-shopping-bag text-pink-500 mr-2\"></i>购买指南\n          </h2>\n\n          <!-- 官方渠道 -->\n          <div class=\"bg-white rounded-xl shadow-lg p-8 mb-12\">\n            <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">官方渠道推荐</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div\n                v-for=\"(channel, index) in officialChannels\"\n                :key=\"index\"\n                class=\"border border-gray-200 rounded-lg p-6 hover:border-pink-300 transition cursor-pointer\"\n              >\n                <div class=\"flex items-center mb-4\">\n                  <i :class=\"['text-2xl mr-3 text-pink-500', channel.icon]\"></i>\n                  <h4 class=\"text-xl font-bold text-gray-800\">\n                    {{ channel.name }}\n                  </h4>\n                </div>\n                <p class=\"text-gray-600 mb-4\">{{ channel.description }}</p>\n                <div class=\"flex items-center text-sm text-gray-500\">\n                  <i class=\"fas fa-check-circle text-green-500 mr-2\"></i>\n                  <span>{{ channel.verification }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 价格趋势 -->\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12\">\n            <div class=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 class=\"text-xl font-bold mb-6 text-gray-800\">价格趋势分析</h3>\n              <div ref=\"priceChart\" class=\"h-80\"></div>\n            </div>\n\n            <div class=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 class=\"text-xl font-bold mb-6 text-gray-800\">购买注意事项</h3>\n              <ul class=\"space-y-4\">\n                <li\n                  v-for=\"(tip, index) in purchaseTips\"\n                  :key=\"index\"\n                  class=\"flex items-start\"\n                >\n                  <div\n                    class=\"bg-pink-100 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n                  >\n                    <span class=\"text-pink-500 font-bold\">{{ index + 1 }}</span>\n                  </div>\n                  <div>\n                    <h4 class=\"font-bold text-gray-800\">{{ tip.title }}</h4>\n                    <p class=\"text-gray-600\">{{ tip.content }}</p>\n                  </div>\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          <!-- 防骗指南 -->\n          <div class=\"bg-white rounded-xl shadow-lg p-8\">\n            <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">防骗指南</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n              <div>\n                <p class=\"text-gray-600 mb-6\">\n                  在购买 Labubu 收藏品时，请警惕以下常见骗局，保护您的权益。\n                </p>\n                <div class=\"space-y-4\">\n                  <div\n                    v-for=\"(scam, index) in antiScamGuide\"\n                    :key=\"index\"\n                    class=\"bg-red-50 border-l-4 border-red-500 p-4\"\n                  >\n                    <h4 class=\"font-bold text-red-600 mb-1\">\n                      {{ scam.title }}\n                    </h4>\n                    <p class=\"text-gray-700\">{{ scam.description }}</p>\n                  </div>\n                </div>\n              </div>\n              <div class=\"relative h-80\">\n                <img\n                  :src=\"antiScamImage\"\n                  alt=\"防骗指南\"\n                  class=\"w-full h-full object-cover object-top rounded-lg\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </main>\n\n    <!-- 页脚 -->\n    <footer class=\"bg-gray-800 text-white py-12\">\n      <div class=\"container mx-auto px-4\">\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">关于 Labubu</h3>\n            <p class=\"text-gray-400\">\n              Labubu\n              是一款备受喜爱的可爱卡通收藏品，以其独特的设计和高品质而闻名。\n            </p>\n          </div>\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">快速链接</h3>\n            <ul class=\"space-y-2\">\n              <li v-for=\"(item, index) in navItems\" :key=\"index\">\n                <a\n                  href=\"#\"\n                  class=\"text-gray-400 hover:text-pink-300 cursor-pointer\"\n                  >{{ item.name }}</a\n                >\n              </li>\n              <li>\n                <a\n                  href=\"#\"\n                  class=\"text-gray-400 hover:text-pink-300 cursor-pointer\"\n                  >关于我们</a\n                >\n              </li>\n              <li>\n                <a\n                  href=\"#\"\n                  class=\"text-gray-400 hover:text-pink-300 cursor-pointer\"\n                  >联系我们</a\n                >\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">联系方式</h3>\n            <ul class=\"space-y-2 text-gray-400\">\n              <li class=\"flex items-center\">\n                <i class=\"fas fa-envelope mr-2 text-pink-400\"></i>\n                <EMAIL>\n              </li>\n              <li class=\"flex items-center\">\n                <i class=\"fas fa-phone mr-2 text-pink-400\"></i> +86 123 4567\n                8910\n              </li>\n              <li class=\"flex items-center\">\n                <i class=\"fas fa-map-marker-alt mr-2 text-pink-400\"></i>\n                上海市浦东新区\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">关注我们</h3>\n            <div class=\"flex space-x-4\">\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-weibo\"></i>\n              </a>\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-weixin\"></i>\n              </a>\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-instagram\"></i>\n              </a>\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-bilibili\"></i>\n              </a>\n            </div>\n            <div class=\"mt-4\">\n              <p class=\"text-gray-400\">支付方式</p>\n              <div class=\"flex space-x-3 mt-2\">\n                <i class=\"fab fa-alipay text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-weixin text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-cc-visa text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-cc-mastercard text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-paypal text-2xl text-gray-400\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div\n          class=\"border-t border-gray-700 mt-8 pt-8 text-center text-gray-500\"\n        >\n          <p>&copy; 2025 Labubu Portal. 保留所有权利。</p>\n        </div>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nimport { Swiper, SwiperSlide, directive } from \"vue-awesome-swiper\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  components: {\n    Swiper,\n    SwiperSlide,\n  },\n  directives: {\n    swiper: directive,\n  },\n  data() {\n    return {\n      activeNav: \"news\",\n      logoUrl:\n        \"https://readdy.ai/api/search-image?query=cute%20cartoon%20logo%20for%20Labubu%2C%20minimalist%2C%20pastel%20colors%2C%20simple%20design%2C%20kawaii%20style%2C%20rounded%20edges%2C%20pink%20and%20mint%20color%20palette%2C%20white%20background%2C%20high%20quality&width=200&height=200&seq=1&orientation=squarish\",\n      heroBackgroundUrl:\n        \"https://readdy.ai/api/search-image?query=cute%20cartoon%20characters%20collection%2C%20pastel%20colors%2C%20kawaii%20style%2C%20soft%20gradient%20background%2C%20pink%20and%20mint%20color%20palette%2C%20adorable%20fantasy%20creatures%2C%20high%20quality%20illustration%2C%20simple%20and%20clean%20design&width=1440&height=500&seq=2&orientation=landscape\",\n      navItems: [\n        { id: \"news\", name: \"最新资讯\", icon: \"fas fa-newspaper\" },\n        { id: \"guide\", name: \"收藏指南\", icon: \"fas fa-book\" },\n        { id: \"community\", name: \"社区讨论\", icon: \"fas fa-users\" },\n        { id: \"purchase\", name: \"购买指南\", icon: \"fas fa-shopping-bag\" },\n      ],\n      swiperOptions: {\n        slidesPerView: 1,\n        spaceBetween: 0,\n        loop: true,\n        autoplay: {\n          delay: 5000,\n          disableOnInteraction: false,\n        },\n        pagination: {\n          el: \".swiper-pagination\",\n          clickable: true,\n        },\n        navigation: {\n          nextEl: \".swiper-button-next\",\n          prevEl: \".swiper-button-prev\",\n        },\n      },\n      newsSlides: [\n        {\n          title: \"Labubu 2025 限量版系列即将发布\",\n          description:\n            \"全新限量版 Labubu 系列将于下月发布，包含 5 款全新角色设计\",\n          image:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20figurine%20collection%20on%20display%2C%20pastel%20colors%2C%20kawaii%20style%2C%20soft%20lighting%2C%20professional%20product%20photography%2C%20clean%20background%2C%20high%20quality%2C%20detailed%20figurines&width=1200&height=600&seq=3&orientation=landscape\",\n        },\n        {\n          title: \"第三届 Labubu 收藏家大会圆满结束\",\n          description: \"来自全球的 Labubu 爱好者齐聚上海，分享收藏心得与经验\",\n          image:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20convention%20with%20people%20displaying%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20exhibition%20hall%2C%20collectors%20meeting%2C%20bright%20and%20colorful%20atmosphere%2C%20high%20quality&width=1200&height=600&seq=4&orientation=landscape\",\n        },\n        {\n          title: \"Labubu 艺术家签售会即将举行\",\n          description: \"原创设计师将在北京、上海、广州三地举办签售会\",\n          image:\n            \"https://readdy.ai/api/search-image?query=artist%20signing%20cute%20cartoon%20figurines%2C%20fans%20waiting%20in%20line%2C%20pastel%20colors%2C%20kawaii%20style%2C%20bright%20indoor%20setting%2C%20art%20gallery%20atmosphere%2C%20high%20quality&width=1200&height=600&seq=5&orientation=landscape\",\n        },\n      ],\n      newsCategories: [\n        {\n          title: \"官方资讯\",\n          icon: \"fas fa-bullhorn\",\n          items: [\n            \"Labubu 2025 夏季限定款预售开启\",\n            \"设计师访谈：Labubu 灵感来源揭秘\",\n            \"官方商城六一儿童节特别活动\",\n            \"新品发布：Labubu 海洋系列\",\n          ],\n        },\n        {\n          title: \"行业动态\",\n          icon: \"fas fa-chart-line\",\n          items: [\n            \"2025 年潮玩市场发展趋势分析\",\n            \"Labubu 在国际收藏市场的地位\",\n            \"潮玩收藏品投资价值研究报告\",\n            \"全球限量版收藏品拍卖记录\",\n          ],\n        },\n        {\n          title: \"社区热点\",\n          icon: \"fas fa-fire\",\n          items: [\n            \"收藏家故事：我与 Labubu 的十年之约\",\n            \"社区投票：最受欢迎 Labubu 系列\",\n            \"粉丝创作：Labubu 同人作品展示\",\n            \"线下活动：各地 Labubu 粉丝聚会\",\n          ],\n        },\n      ],\n      collectionGuides: [\n        {\n          title: \"新手入门指南\",\n          description: \"从零开始了解 Labubu 收藏，掌握基础知识和收藏技巧\",\n          image:\n            \"https://readdy.ai/api/search-image?query=beginner%20guide%20to%20collecting%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20educational%20illustration%2C%20simple%20steps%20shown%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=6&orientation=landscape\",\n          icon: \"fas fa-star\",\n        },\n        {\n          title: \"真伪鉴别技巧\",\n          description: \"学习如何辨别 Labubu 正品与仿制品，保护您的收藏投资\",\n          image:\n            \"https://readdy.ai/api/search-image?query=comparing%20authentic%20vs%20fake%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20side%20by%20side%20comparison%2C%20magnifying%20glass%2C%20detailed%20differences%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=7&orientation=landscape\",\n          icon: \"fas fa-check-circle\",\n        },\n        {\n          title: \"收藏保养方法\",\n          description: \"专业的 Labubu 收藏品保养技巧，让您的收藏保持最佳状态\",\n          image:\n            \"https://readdy.ai/api/search-image?query=caring%20for%20cute%20cartoon%20figurines%2C%20cleaning%20and%20maintenance%2C%20pastel%20colors%2C%20kawaii%20style%2C%20gentle%20handling%2C%20display%20case%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=8&orientation=landscape\",\n          icon: \"fas fa-heart\",\n        },\n        {\n          title: \"投资价值分析\",\n          description: \"深入分析 Labubu 收藏品的市场价值和投资潜力\",\n          image:\n            \"https://readdy.ai/api/search-image?query=investment%20value%20chart%20with%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20financial%20graphs%2C%20upward%20trends%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=9&orientation=landscape\",\n          icon: \"fas fa-chart-line\",\n        },\n      ],\n      authenticationTips: [\n        \"检查包装盒上的官方防伪标识和序列号\",\n        \"注意 Labubu 的材质质感和细节处理\",\n        \"辨别正品特有的颜色饱和度和色彩过渡\",\n        \"了解不同系列的限量编号规则\",\n        \"通过官方渠道验证产品真伪\",\n      ],\n      authenticationImage:\n        \"https://readdy.ai/api/search-image?query=authentication%20guide%20for%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20showing%20details%20for%20verification%2C%20magnifying%20glass%20examining%20details%2C%20clean%20background%2C%20high%20quality&width=600&height=600&seq=10&orientation=squarish\",\n      communityShowcase: [\n        {\n          title: \"梦幻系列全收藏\",\n          user: \"星辰收藏家\",\n          image:\n            \"https://readdy.ai/api/search-image?query=complete%20collection%20of%20cute%20cartoon%20figurines%20on%20display%20shelf%2C%20pastel%20colors%2C%20kawaii%20style%2C%20dream%20series%2C%20organized%20display%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=11&orientation=squarish\",\n        },\n        {\n          title: \"限量版珍藏\",\n          user: \"稀有收集者\",\n          image:\n            \"https://readdy.ai/api/search-image?query=limited%20edition%20cute%20cartoon%20figurines%20in%20display%20case%2C%20pastel%20colors%2C%20kawaii%20style%2C%20rare%20collectibles%2C%20special%20lighting%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=12&orientation=squarish\",\n        },\n        {\n          title: \"海洋主题系列\",\n          user: \"蓝色梦想\",\n          image:\n            \"https://readdy.ai/api/search-image?query=ocean%20themed%20cute%20cartoon%20figurines%20collection%2C%20pastel%20colors%2C%20kawaii%20style%2C%20blue%20color%20palette%2C%20sea%20creatures%20design%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=13&orientation=squarish\",\n        },\n        {\n          title: \"节日特别版\",\n          user: \"四季收藏家\",\n          image:\n            \"https://readdy.ai/api/search-image?query=holiday%20special%20edition%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20festive%20themed%2C%20christmas%20and%20halloween%20designs%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=14&orientation=squarish\",\n        },\n      ],\n      communityDiscussions: [\n        {\n          title: \"分享我的 Labubu 收藏之旅：从入门到痴迷\",\n          preview:\n            \"作为一名收藏 Labubu 已有五年的老粉，想和大家分享我的收藏经验和心得...\",\n          user: \"粉红收藏家\",\n          userAvatar:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20female%20character%2C%20pastel%20pink%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=15&orientation=squarish\",\n          date: \"2025-06-15\",\n          comments: 42,\n          likes: 156,\n        },\n        {\n          title: \"求助：如何为 Labubu 收藏品打造完美展示空间？\",\n          preview:\n            \"最近我的收藏越来越多，想请教各位大神如何设计一个既美观又实用的展示柜...\",\n          user: \"新手收藏者\",\n          userAvatar:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20male%20character%2C%20pastel%20blue%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=16&orientation=squarish\",\n          date: \"2025-06-14\",\n          comments: 28,\n          likes: 73,\n        },\n        {\n          title: \"讨论：2025年最值得投资的 Labubu 系列是哪个？\",\n          preview:\n            \"根据市场趋势和历年数据分析，我认为今年最具投资潜力的系列是...\",\n          user: \"投资分析师\",\n          userAvatar:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20professional%20character%20with%20glasses%2C%20pastel%20green%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=17&orientation=squarish\",\n          date: \"2025-06-12\",\n          comments: 56,\n          likes: 210,\n        },\n      ],\n      expertQA: [\n        {\n          question: \"如何判断早期 Labubu 系列的收藏价值？\",\n          answer:\n            \"早期系列的价值主要看限量程度、保存状态和市场需求。建议关注官方发行量数据，以及历年拍卖成交价格走势。\",\n        },\n        {\n          question: \"Labubu 收藏品应该拆盒展示还是保持原包装？\",\n          answer:\n            \"从保值角度看，保持原包装完好更有利于维持价值。但个人收藏可根据自己喜好，拆盒展示能更好欣赏细节。\",\n        },\n        {\n          question: \"如何防止 Labubu 收藏品褪色或变形？\",\n          answer:\n            \"避免阳光直射、高温和潮湿环境，定期用软毛刷轻轻除尘，不要用湿布擦拭，展示柜最好选择防紫外线玻璃。\",\n        },\n      ],\n      officialChannels: [\n        {\n          name: \"官方网店\",\n          icon: \"fas fa-store\",\n          description: \"Labubu 官方授权网店，提供最新发布和限量版收藏品\",\n          verification: \"官方认证销售渠道\",\n        },\n        {\n          name: \"授权实体店\",\n          icon: \"fas fa-map-marker-alt\",\n          description: \"遍布全国的 Labubu 授权实体店，可实地挑选和购买\",\n          verification: \"线下官方授权店铺\",\n        },\n        {\n          name: \"特许经销商\",\n          icon: \"fas fa-certificate\",\n          description: \"经过严格筛选的特许经销商，确保产品正品保障\",\n          verification: \"官方认证合作伙伴\",\n        },\n      ],\n      purchaseTips: [\n        {\n          title: \"关注发售时间\",\n          content:\n            \"限量版 Labubu 通常会在特定时间发售，提前关注官方公告，设置提醒避免错过。\",\n        },\n        {\n          title: \"验证销售渠道\",\n          content:\n            \"只从官方认证的渠道购买，避免购买到假冒产品，保障收藏品的真实性和价值。\",\n        },\n        {\n          title: \"检查包装完整性\",\n          content:\n            \"收到商品后，仔细检查包装是否完好，防伪标识是否清晰，附件是否齐全。\",\n        },\n        {\n          title: \"保留购买凭证\",\n          content:\n            \"妥善保管购买凭证和收据，这对于后续鉴定真伪和转售都非常重要。\",\n        },\n      ],\n      antiScamGuide: [\n        {\n          title: \"虚假预售骗局\",\n          description:\n            \"谨防声称可预订未发布系列的卖家，官方预售只通过官方渠道进行。\",\n        },\n        {\n          title: \"以次充好\",\n          description:\n            \"警惕价格明显低于市场的商品，可能是仿制品或有质量问题的次品。\",\n        },\n        {\n          title: \"虚假限量编号\",\n          description: \"核实限量版编号的真实性，某些骗子会伪造限量编号标签。\",\n        },\n      ],\n      antiScamImage:\n        \"https://readdy.ai/api/search-image?query=anti-scam%20guide%20for%20buying%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20warning%20signs%2C%20security%20verification%2C%20safe%20shopping%20tips%2C%20clean%20background%2C%20high%20quality&width=600&height=500&seq=18&orientation=landscape\",\n    };\n  },\n  mounted() {\n    this.initPriceChart();\n  },\n  methods: {\n    initPriceChart() {\n      const chartDom = this.$refs.priceChart;\n      const myChart = echarts.init(chartDom);\n\n      const option = {\n        animation: false,\n        title: {\n          text: \"Labubu 系列价格趋势 (2023-2025)\",\n          left: \"center\",\n          textStyle: {\n            color: \"#333\",\n          },\n        },\n        tooltip: {\n          trigger: \"axis\",\n        },\n        legend: {\n          data: [\"限量版系列\", \"常规系列\", \"特别合作款\"],\n          bottom: 0,\n        },\n        grid: {\n          left: \"3%\",\n          right: \"4%\",\n          bottom: \"15%\",\n          top: \"15%\",\n          containLabel: true,\n        },\n        xAxis: {\n          type: \"category\",\n          boundaryGap: false,\n          data: [\n            \"2023 Q1\",\n            \"2023 Q2\",\n            \"2023 Q3\",\n            \"2023 Q4\",\n            \"2024 Q1\",\n            \"2024 Q2\",\n            \"2024 Q3\",\n            \"2024 Q4\",\n            \"2025 Q1\",\n            \"2025 Q2\",\n          ],\n        },\n        yAxis: {\n          type: \"value\",\n          name: \"价格 (¥)\",\n          nameLocation: \"end\",\n        },\n        series: [\n          {\n            name: \"限量版系列\",\n            type: \"line\",\n            data: [1200, 1350, 1500, 1800, 2100, 2400, 2700, 3100, 3500, 3800],\n            smooth: true,\n            lineStyle: {\n              color: \"#ff6b81\",\n            },\n            itemStyle: {\n              color: \"#ff6b81\",\n            },\n          },\n          {\n            name: \"常规系列\",\n            type: \"line\",\n            data: [300, 320, 350, 370, 390, 410, 430, 450, 480, 500],\n            smooth: true,\n            lineStyle: {\n              color: \"#70a1ff\",\n            },\n            itemStyle: {\n              color: \"#70a1ff\",\n            },\n          },\n          {\n            name: \"特别合作款\",\n            type: \"line\",\n            data: [800, 950, 1100, 1300, 1500, 1650, 1800, 2000, 2300, 2600],\n            smooth: true,\n            lineStyle: {\n              color: \"#7bed9f\",\n            },\n            itemStyle: {\n              color: \"#7bed9f\",\n            },\n          },\n        ],\n      };\n\n      myChart.setOption(option);\n\n      window.addEventListener(\"resize\", () => {\n        myChart.resize();\n      });\n    },\n  },\n};\n</script>\n\n<style scoped>\n.nav-link {\n  position: relative;\n  padding-bottom: 4px;\n}\n\n.nav-link::after {\n  content: \"\";\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background-color: #ec4899;\n  transition: width 0.3s ease;\n}\n\n.nav-link:hover::after,\n.nav-link.active::after {\n  width: 100%;\n}\n\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\n.labubu-card {\n  transition:\n    transform 0.3s ease,\n    box-shadow 0.3s ease;\n}\n\n.labubu-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmC;;EAG1CA,KAAK,EAAC;AAAqE;;EAGzEA,KAAK,EAAC;AAA+D;;EAGhEA,KAAK,EAAC;AAAmB;;;EAMzBA,KAAK,EAAC;AAA0B;;;EAmC9BA,KAAK,EAAC,0BAA0B;EAACC,KAA0B,EAA1B;IAAA;EAAA;;;EACnCD,KAAK,EAAC;AAAsB;;;;EAkCEA,KAAK,EAAC;;;EACpCA,KAAK,EAAC;AAAwB;;EAM5BA,KAAK,EAAC;AAAO;;EAMPA,KAAK,EAAC;AAAe;;;EAOtBA,KAAK,EAAC;AAAiF;;EAEnFA,KAAK,EAAC;AAAoC;;EAG3CA,KAAK,EAAC;AAA4B;;EAe1CA,KAAK,EAAC;AAAuC;;EAMzCA,KAAK,EAAC;AAAK;;EAEZA,KAAK,EAAC;AAAwD;;EAK5DA,KAAK,EAAC;AAAW;;EAOfE,IAAI,EAAC,GAAG;EACRF,KAAK,EAAC;;;;EAmBgBA,KAAK,EAAC;;;EACrCA,KAAK,EAAC;AAAwB;;EAK5BA,KAAK,EAAC;AAAsD;;EAMxDA,KAAK,EAAC;AAAsB;;;EAO5BA,KAAK,EAAC;AAAK;;EACVA,KAAK,EAAC;AAAsC;;EAG7CA,KAAK,EAAC;AAAoB;;EAE3BA,KAAK,EAAC;AAAuH;;EAShIA,KAAK,EAAC;AAAqD;;EACzDA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC;AAAK;;EAOVA,KAAK,EAAC;AAAW;;EAOXA,KAAK,EAAC;AAAe;;EAS5BA,KAAK,EAAC;AAA+B;;;;EAaRA,KAAK,EAAC;;;EACzCA,KAAK,EAAC;AAAwB;;EAM5BA,KAAK,EAAC;AAAO;;EAEXA,KAAK,EAAC;AAAuC;;;EAY5CA,KAAK,EAAC;AAAwI;;EAE1IA,KAAK,EAAC;AAAsB;;EAC7BA,KAAK,EAAC;AAAoB;;EAchCA,KAAK,EAAC;AAAuC;;EAC3CA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAW;;EAMbA,KAAK,EAAC;AAAkB;;;EAMtBA,KAAK,EAAC;AAAQ;;EACbA,KAAK,EAAC;AAAyB;;EAGhCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAA8C;;EAwB5DA,KAAK,EAAC;AAAqC;;EACzCA,KAAK,EAAC;AAAW;;EAMbA,KAAK,EAAC;AAAuB;;EAM7BA,KAAK,EAAC;AAA2B;;EAEjCA,KAAK,EAAC;AAAuB;;EAM7BA,KAAK,EAAC;AAAe;;;EAgBCA,KAAK,EAAC;;;EACxCA,KAAK,EAAC;AAAwB;;EAM5BA,KAAK,EAAC;AAAyC;;EAE7CA,KAAK,EAAC;AAAuC;;EAMzCA,KAAK,EAAC;AAAwB;;EAE7BA,KAAK,EAAC;AAAiC;;EAI1CA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAyC;;EASrDA,KAAK,EAAC;AAA6C;;EACjDA,KAAK,EAAC;AAAmC;;EAEvCG,GAAG,EAAC,YAAY;EAACH,KAAK,EAAC;;;EAGzBA,KAAK,EAAC;AAAmC;;EAExCA,KAAK,EAAC;AAAW;;EAOfA,KAAK,EAAC;AAA6E;;EAE7EA,KAAK,EAAC;AAAyB;;EAGjCA,KAAK,EAAC;AAAyB;;EAChCA,KAAK,EAAC;AAAe;;EAQ7BA,KAAK,EAAC;AAAmC;;EAEvCA,KAAK,EAAC;AAAoD;;EAKtDA,KAAK,EAAC;AAAW;;EAMdA,KAAK,EAAC;AAA6B;;EAGpCA,KAAK,EAAC;AAAe;;EAIzBA,KAAK,EAAC;AAAe;;;EAc5BA,KAAK,EAAC;AAA8B;;EACrCA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAuC;;EAU1CA,KAAK,EAAC;AAAW;;EAGfE,IAAI,EAAC,GAAG;EACRF,KAAK,EAAC;;;;;uBAldtBI,mBAAA,CAsiBM,OAtiBNC,UAsiBM,GAriBJC,mBAAA,SAAY,EACZC,mBAAA,CA2CS,UA3CTC,UA2CS,GAxCPD,mBAAA,CAuCM,OAvCNE,UAuCM,GApCJH,mBAAA,UAAa,EACbC,mBAAA,CAGM,OAHNG,UAGM,GAFJH,mBAAA,CAA0D;IAApDI,GAAG,EAAEC,KAAA,CAAAC,OAAO;IAAEC,GAAG,EAAC,aAAa;IAACd,KAAK,EAAC;iEAC5CO,mBAAA,CAA+D;IAA3DP,KAAK,EAAC;EAAkC,GAAC,eAAa,qB,GAG5DM,mBAAA,SAAY,EACZC,mBAAA,CASM,OATNQ,UASM,I,kBARJX,mBAAA,CAOIY,SAAA,QAAAC,WAAA,CANsBL,KAAA,CAAAM,QAAQ,GAAxBC,IAAI,EAAEC,KAAK;yBADrBhB,mBAAA,CAOI;MALDiB,GAAG,EAAED,KAAK;MACVpB,KAAK,EAAAsB,eAAA,+CAAgDV,KAAA,CAAAW,SAAS,KAAKJ,IAAI,CAACK,EAAE;MAC1EC,OAAK,EAAAC,MAAA,IAAEd,KAAA,CAAAW,SAAS,GAAGJ,IAAI,CAACK;QAEzBjB,mBAAA,CAAoC;MAAhCP,KAAK,EAAAsB,eAAA,UAAWH,IAAI,CAACQ,IAAI;+DAAUR,IAAI,CAACS,IAAI,iB;oCAIpDtB,mBAAA,aAAgB,E,qrBAqBpBA,mBAAA,UAAa,EACbC,mBAAA,CA+YO,eA9YLD,mBAAA,aAAgB,EAChBC,mBAAA,CAgCU,WAhCVsB,UAgCU,GA/BRtB,mBAAA,CAMM,OANNuB,UAMM,GALJvB,mBAAA,CAIE;IAHCI,GAAG,EAAEC,KAAA,CAAAmB,iBAAiB;IACvBjB,GAAG,EAAC,WAAW;IACfd,KAAK,EAAC;+/BA6BZM,mBAAA,WAAc,EACCM,KAAA,CAAAW,SAAS,e,cAAxBnB,mBAAA,CA6EU,WA7EV4B,WA6EU,GA5ERzB,mBAAA,CA2EM,OA3EN0B,WA2EM,G,0BA1EJ1B,mBAAA,CAEK;IAFDP,KAAK,EAAC;EAAoD,IAC5DO,mBAAA,CAAmD;IAAhDP,KAAK,EAAC;EAAqC,I,iBAAK,OACrD,E,sBAEAM,mBAAA,SAAY,EACZC,mBAAA,CA8BM,OA9BN2B,WA8BM,GA7BJC,YAAA,CA4BSC,iBAAA;IA3BPpC,KAAK,EAAC,sCAAsC;IAC3CqC,OAAO,EAAEzB,KAAA,CAAA0B;;sBAEI,MAAoC,E,kBAAlDlC,mBAAA,CAuBeY,SAAA,QAAAC,WAAA,CAvBwBL,KAAA,CAAA2B,UAAU,GAA3BC,KAAK,EAAEpB,KAAK;2BAAlCqB,YAAA,CAuBeC,uBAAA;QAvBqCrB,GAAG,EAAED;MAAK;0BAC5D,MAqBM,CArBNb,mBAAA,CAqBM,OArBNoC,WAqBM,GApBJpC,mBAAA,CAIE;UAHCI,GAAG,EAAE6B,KAAK,CAACI,KAAK;UAChB9B,GAAG,EAAE0B,KAAK,CAACK,KAAK;UACjB7C,KAAK,EAAC;8CAERO,mBAAA,CAcM,OAdNuC,WAcM,GAXJvC,mBAAA,CAEK,MAFLwC,WAEK,EAAAC,gBAAA,CADAR,KAAK,CAACK,KAAK,kBAEhBtC,mBAAA,CAEI,KAFJ0C,WAEI,EAAAD,gBAAA,CADCR,KAAK,CAACU,WAAW,kB,0BAEtB3C,mBAAA,CAIS;UAHPP,KAAK,EAAC;QAAqH,GAC5H,QAED,qB;;;;;oCAOVM,mBAAA,WAAc,EACdC,mBAAA,CAmCM,OAnCN4C,WAmCM,I,kBAlCJ/C,mBAAA,CAiCMY,SAAA,QAAAC,WAAA,CAhCwBL,KAAA,CAAAwC,cAAc,GAAlCC,QAAQ,EAAEjC,KAAK;yBADzBhB,mBAAA,CAiCM;MA/BHiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CA2BM,OA3BN+C,WA2BM,GA1BJ/C,mBAAA,CAKK,MALLgD,WAKK,GAFHhD,mBAAA,CAAsD;MAAlDP,KAAK,EAAAsB,eAAA,wBAAyB+B,QAAQ,CAAC1B,IAAI;8CAAO,GACtD,GAAAqB,gBAAA,CAAGK,QAAQ,CAACR,KAAK,iB,GAEnBtC,mBAAA,CAcK,MAdLiD,WAcK,I,kBAbHpD,mBAAA,CAYKY,SAAA,QAAAC,WAAA,CAXyBoC,QAAQ,CAACI,KAAK,GAAlCtC,IAAI,EAAEuC,SAAS;2BADzBtD,mBAAA,CAYK;QAVFiB,GAAG,EAAEqC,SAAS;QACf1D,KAAK,EAAC;UAENO,mBAAA,CAMI,KANJoD,WAMI,G,0BAFFpD,mBAAA,CAAyC;QAAnCP,KAAK,EAAC;MAAoB,GAAC,GAAC,sBAClCO,mBAAA,CAAuB,cAAAyC,gBAAA,CAAd7B,IAAI,iB;gEAInBZ,mBAAA,CAIS;MAHPP,KAAK,EAAC;IAA2F,I,iBAClG,QACM,GAAAO,mBAAA,CAAuC;MAApCP,KAAK,EAAC;IAAyB,G;6EAQnDM,mBAAA,WAAc,EACCM,KAAA,CAAAW,SAAS,gB,cAAxBnB,mBAAA,CAqEU,WArEVwD,WAqEU,GApERrD,mBAAA,CAmEM,OAnENsD,WAmEM,G,4BAlEJtD,mBAAA,CAEK;IAFDP,KAAK,EAAC;EAAoD,IAC5DO,mBAAA,CAA8C;IAA3CP,KAAK,EAAC;EAAgC,I,iBAAK,OAChD,E,sBAEAO,mBAAA,CAyBM,OAzBNuD,WAyBM,I,kBAxBJ1D,mBAAA,CAuBMY,SAAA,QAAAC,WAAA,CAtBqBL,KAAA,CAAAmD,gBAAgB,GAAjCC,KAAK,EAAE5C,KAAK;yBADtBhB,mBAAA,CAuBM;MArBHiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CAMM,OANN0D,WAMM,GALJ1D,mBAAA,CAIE;MAHCI,GAAG,EAAEqD,KAAK,CAACpB,KAAK;MAChB9B,GAAG,EAAEkD,KAAK,CAACnB,KAAK;MACjB7C,KAAK,EAAC;4CAGVO,mBAAA,CAUM,OAVN2D,WAUM,GATJ3D,mBAAA,CAEK,MAFL4D,WAEK,EAAAnB,gBAAA,CADAgB,KAAK,CAACnB,KAAK,kBAEhBtC,mBAAA,CAAyD,KAAzD6D,WAAyD,EAAApB,gBAAA,CAAxBgB,KAAK,CAACd,WAAW,kBAClD3C,mBAAA,CAIS,UAJT8D,WAIS,GADP9D,mBAAA,CAAqC;MAAjCP,KAAK,EAAAsB,eAAA,UAAW0C,KAAK,CAACrC,IAAI;wEAAO,OACvC,G;oCAKNrB,mBAAA,cAAiB,EACjBC,mBAAA,CAiCM,OAjCN+D,WAiCM,GAhCJ/D,mBAAA,CA+BM,OA/BNgE,WA+BM,GA9BJhE,mBAAA,CAsBM,OAtBNiE,WAsBM,G,0BArBJjE,mBAAA,CAEK;IAFDP,KAAK,EAAC;EAAuC,GAAC,UAElD,sB,4BACAO,mBAAA,CAEI;IAFDP,KAAK,EAAC;EAAoB,GAAC,kCAE9B,sBACAO,mBAAA,CASK,MATLkE,WASK,I,kBARHrE,mBAAA,CAOKY,SAAA,QAAAC,WAAA,CANoBL,KAAA,CAAA8D,kBAAkB,GAAjCC,GAAG,EAAEvD,KAAK;yBADpBhB,mBAAA,CAOK;MALFiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;kCAENO,mBAAA,CAA4D;MAAzDP,KAAK,EAAC;IAA8C,6BACvDO,mBAAA,CAA4C,QAA5CqE,WAA4C,EAAA5B,gBAAA,CAAb2B,GAAG,iB;gEAGtCpE,mBAAA,CAIS;IAHPP,KAAK,EAAC;EAAqH,GAC5H,YAED,qB,GAEFO,mBAAA,CAMM,OANNsE,WAMM,GALJtE,mBAAA,CAIE;IAHCI,GAAG,EAAEC,KAAA,CAAAkE,mBAAmB;IACzBhE,GAAG,EAAC,MAAM;IACVd,KAAK,EAAC;uFAQlBM,mBAAA,WAAc,EACCM,KAAA,CAAAW,SAAS,oB,cAAxBnB,mBAAA,CAoHU,WApHV2E,WAoHU,GAnHRxE,mBAAA,CAkHM,OAlHNyE,WAkHM,G,4BAjHJzE,mBAAA,CAEK;IAFDP,KAAK,EAAC;EAAoD,IAC5DO,mBAAA,CAA+C;IAA5CP,KAAK,EAAC;EAAiC,I,iBAAK,OACjD,E,sBAEAM,mBAAA,WAAc,EACdC,mBAAA,CA4BM,OA5BN0E,WA4BM,G,4BA3BJ1E,mBAAA,CAA4D;IAAxDP,KAAK,EAAC;EAAuC,GAAC,OAAK,sBACvDO,mBAAA,CAkBM,OAlBN2E,WAkBM,I,kBAjBJ9E,mBAAA,CAgBMY,SAAA,QAAAC,WAAA,CAfoBL,KAAA,CAAAuE,iBAAiB,GAAjChE,IAAI,EAAEC,KAAK;yBADrBhB,mBAAA,CAgBM;MAdHiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CAIE;MAHCI,GAAG,EAAEQ,IAAI,CAACyB,KAAK;MACf9B,GAAG,EAAEK,IAAI,CAAC0B,KAAK;MAChB7C,KAAK,EAAC;0CAERO,mBAAA,CAKM,OALN6E,WAKM,GAFJ7E,mBAAA,CAAsD,MAAtD8E,WAAsD,EAAArC,gBAAA,CAAlB7B,IAAI,CAAC0B,KAAK,kBAC9CtC,mBAAA,CAAsD,KAAtD+E,WAAsD,EAAxB,OAAK,GAAAtC,gBAAA,CAAG7B,IAAI,CAACoE,IAAI,iB;gEAIrDhF,mBAAA,CAMM;IANDP,KAAK,EAAC;EAAkB,IAC3BO,mBAAA,CAIS;IAHPP,KAAK,EAAC;EAAgH,GACvH,UAED,E,wBAIJM,mBAAA,UAAa,EACbC,mBAAA,CA4EM,OA5ENiF,WA4EM,GA3EJjF,mBAAA,CAsCM,OAtCNkF,WAsCM,G,4BArCJlF,mBAAA,CAA2D;IAAvDP,KAAK,EAAC;EAAuC,GAAC,MAAI,sBACtDO,mBAAA,CAmCM,OAnCNmF,WAmCM,I,kBAlCJtF,mBAAA,CAiCMY,SAAA,QAAAC,WAAA,CAhC0BL,KAAA,CAAA+E,oBAAoB,GAA1CC,UAAU,EAAExE,KAAK;yBAD3BhB,mBAAA,CAiCM;MA/BHiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CA2BM,OA3BNsF,WA2BM,GA1BJtF,mBAAA,CAIE;MAHCI,GAAG,EAAEiF,UAAU,CAACE,UAAU;MAC3BhF,GAAG,EAAC,MAAM;MACVd,KAAK,EAAC;0CAERO,mBAAA,CAoBM,OApBNwF,WAoBM,GAnBJxF,mBAAA,CAEK,MAFLyF,WAEK,EAAAhD,gBAAA,CADA4C,UAAU,CAAC/C,KAAK,kBAErBtC,mBAAA,CAA0D,KAA1D0F,WAA0D,EAAAjD,gBAAA,CAAzB4C,UAAU,CAACM,OAAO,kBACnD3F,mBAAA,CAcM,OAdN4F,WAcM,GAbJ5F,mBAAA,CAAkC,cAAAyC,gBAAA,CAAzB4C,UAAU,CAACL,IAAI,kB,4BACxBhF,mBAAA,CAA2B;MAArBP,KAAK,EAAC;IAAM,GAAC,GAAC,sBACpBO,mBAAA,CAAkC,cAAAyC,gBAAA,CAAzB4C,UAAU,CAACQ,IAAI,kB,4BACxB7F,mBAAA,CAA2B;MAArBP,KAAK,EAAC;IAAM,GAAC,GAAC,sBACpBO,mBAAA,CAGC,e,4BAFEA,mBAAA,CAAmC;MAAhCP,KAAK,EAAC;IAAqB,6B,kCAC/B4F,UAAU,CAACS,QAAQ,iB,+BAErB9F,mBAAA,CAA2B;MAArBP,KAAK,EAAC;IAAM,GAAC,GAAC,sBACpBO,mBAAA,CAGC,e,4BAFEA,mBAAA,CAAiC;MAA9BP,KAAK,EAAC;IAAmB,6B,kCAAQ4F,UAAU,CAACU,KAAK,iB;sCAUnEhG,mBAAA,UAAa,EACbC,mBAAA,CAiCM,c,4BAhCJA,mBAAA,CAA2D;IAAvDP,KAAK,EAAC;EAAuC,GAAC,MAAI,sBACtDO,mBAAA,CA8BM,OA9BNgG,WA8BM,GA7BJhG,mBAAA,CAuBM,OAvBNiG,WAuBM,I,kBAtBJpG,mBAAA,CAqBMY,SAAA,QAAAC,WAAA,CApBkBL,KAAA,CAAA6F,QAAQ,GAAtBC,EAAE,EAAEtF,KAAK;yBADnBhB,mBAAA,CAqBM;MAnBHiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CAOM,OAPNoG,WAOM,G,4BANJpG,mBAAA,CAIM;MAHJP,KAAK,EAAC;IAA2F,GAClG,KAED,sBACAO,mBAAA,CAA0D,KAA1DqG,WAA0D,EAAA5D,gBAAA,CAAlB0D,EAAE,CAACG,QAAQ,iB,GAErDtG,mBAAA,CAOM,OAPNuG,WAOM,G,4BANJvG,mBAAA,CAIM;MAHJP,KAAK,EAAC;IAA2F,GAClG,KAED,sBACAO,mBAAA,CAA4C,KAA5CwG,WAA4C,EAAA/D,gBAAA,CAAhB0D,EAAE,CAACM,MAAM,iB;gEAI3CzG,mBAAA,CAIS;IAHPP,KAAK,EAAC;EAA+H,GACtI,QAED,qB,gDAOVM,mBAAA,WAAc,EACCM,KAAA,CAAAW,SAAS,mB,cAAxBnB,mBAAA,CA0FU,WA1FV6G,WA0FU,GAzFR1G,mBAAA,CAwFM,OAxFN2G,WAwFM,G,4BAvFJ3G,mBAAA,CAEK;IAFDP,KAAK,EAAC;EAAoD,IAC5DO,mBAAA,CAAsD;IAAnDP,KAAK,EAAC;EAAwC,I,iBAAK,OACxD,E,sBAEAM,mBAAA,UAAa,EACbC,mBAAA,CAqBM,OArBN4G,WAqBM,G,4BApBJ5G,mBAAA,CAA6D;IAAzDP,KAAK,EAAC;EAAuC,GAAC,QAAM,sBACxDO,mBAAA,CAkBM,OAlBN6G,WAkBM,I,kBAjBJhH,mBAAA,CAgBMY,SAAA,QAAAC,WAAA,CAfuBL,KAAA,CAAAyG,gBAAgB,GAAnCC,OAAO,EAAElG,KAAK;yBADxBhB,mBAAA,CAgBM;MAdHiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CAKM,OALNgH,WAKM,GAJJhH,mBAAA,CAA8D;MAA1DP,KAAK,EAAAsB,eAAA,iCAAkCgG,OAAO,CAAC3F,IAAI;6BACvDpB,mBAAA,CAEK,MAFLiH,WAEK,EAAAxE,gBAAA,CADAsE,OAAO,CAAC1F,IAAI,iB,GAGnBrB,mBAAA,CAA2D,KAA3DkH,WAA2D,EAAAzE,gBAAA,CAA1BsE,OAAO,CAACpE,WAAW,kBACpD3C,mBAAA,CAGM,OAHNmH,WAGM,G,4BAFJnH,mBAAA,CAAuD;MAApDP,KAAK,EAAC;IAAyC,6BAClDO,mBAAA,CAAuC,cAAAyC,gBAAA,CAA9BsE,OAAO,CAACK,YAAY,iB;sCAMrCrH,mBAAA,UAAa,EACbC,mBAAA,CA0BM,OA1BNqH,WA0BM,GAzBJrH,mBAAA,CAGM,OAHNsH,WAGM,G,4BAFJtH,mBAAA,CAA4D;IAAxDP,KAAK,EAAC;EAAsC,GAAC,QAAM,sBACvDO,mBAAA,CAAyC,OAAzCuH,WAAyC,8B,GAG3CvH,mBAAA,CAmBM,OAnBNwH,WAmBM,G,4BAlBJxH,mBAAA,CAA4D;IAAxDP,KAAK,EAAC;EAAsC,GAAC,QAAM,sBACvDO,mBAAA,CAgBK,MAhBLyH,WAgBK,I,kBAfH5H,mBAAA,CAcKY,SAAA,QAAAC,WAAA,CAboBL,KAAA,CAAAqH,YAAY,GAA3BtD,GAAG,EAAEvD,KAAK;yBADpBhB,mBAAA,CAcK;MAZFiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CAIM,OAJN2H,WAIM,GADJ3H,mBAAA,CAA4D,QAA5D4H,WAA4D,EAAAnF,gBAAA,CAAnB5B,KAAK,qB,GAEhDb,mBAAA,CAGM,cAFJA,mBAAA,CAAwD,MAAxD6H,WAAwD,EAAApF,gBAAA,CAAjB2B,GAAG,CAAC9B,KAAK,kBAChDtC,mBAAA,CAA8C,KAA9C8H,WAA8C,EAAArF,gBAAA,CAAlB2B,GAAG,CAAC2D,OAAO,iB;wCAOjDhI,mBAAA,UAAa,EACbC,mBAAA,CA4BM,OA5BNgI,WA4BM,G,4BA3BJhI,mBAAA,CAA2D;IAAvDP,KAAK,EAAC;EAAuC,GAAC,MAAI,sBACtDO,mBAAA,CAyBM,OAzBNiI,WAyBM,GAxBJjI,mBAAA,CAgBM,c,4BAfJA,mBAAA,CAEI;IAFDP,KAAK,EAAC;EAAoB,GAAC,qCAE9B,sBACAO,mBAAA,CAWM,OAXNkI,WAWM,I,kBAVJrI,mBAAA,CASMY,SAAA,QAAAC,WAAA,CARoBL,KAAA,CAAA8H,aAAa,GAA7BC,IAAI,EAAEvH,KAAK;yBADrBhB,mBAAA,CASM;MAPHiB,GAAG,EAAED,KAAK;MACXpB,KAAK,EAAC;QAENO,mBAAA,CAEK,MAFLqI,WAEK,EAAA5F,gBAAA,CADA2F,IAAI,CAAC9F,KAAK,kBAEftC,mBAAA,CAAmD,KAAnDsI,WAAmD,EAAA7F,gBAAA,CAAvB2F,IAAI,CAACzF,WAAW,iB;sCAIlD3C,mBAAA,CAMM,OANNuI,WAMM,GALJvI,mBAAA,CAIE;IAHCI,GAAG,EAAEC,KAAA,CAAAmI,aAAa;IACnBjI,GAAG,EAAC,MAAM;IACVd,KAAK,EAAC;yFASpBM,mBAAA,QAAW,EACXC,mBAAA,CAmGS,UAnGTyI,WAmGS,GAlGPzI,mBAAA,CAiGM,OAjGN0I,WAiGM,GAhGJ1I,mBAAA,CA0FM,OA1FN2I,WA0FM,G,4BAzFJ3I,mBAAA,CAMM,cALJA,mBAAA,CAAiD;IAA7CP,KAAK,EAAC;EAAwB,GAAC,WAAS,GAC5CO,mBAAA,CAGI;IAHDP,KAAK,EAAC;EAAe,GAAC,0CAGzB,E,sBAEFO,mBAAA,CAyBM,c,4BAxBJA,mBAAA,CAA4C;IAAxCP,KAAK,EAAC;EAAwB,GAAC,MAAI,sBACvCO,mBAAA,CAsBK,MAtBL4I,WAsBK,I,kBArBH/I,mBAAA,CAMKY,SAAA,QAAAC,WAAA,CANuBL,KAAA,CAAAM,QAAQ,GAAxBC,IAAI,EAAEC,KAAK;yBAAvBhB,mBAAA,CAMK;MANkCiB,GAAG,EAAED;IAAK,IAC/Cb,mBAAA,CAIC,KAJD6I,WAIC,EAAApG,gBAAA,CADK7B,IAAI,CAACS,IAAI,iB;8DAGjBrB,mBAAA,CAMK,aALHA,mBAAA,CAIC;IAHCL,IAAI,EAAC,GAAG;IACRF,KAAK,EAAC;KACL,MAAI,E,kDAGTO,mBAAA,CAMK,aALHA,mBAAA,CAIC;IAHCL,IAAI,EAAC,GAAG;IACRF,KAAK,EAAC;KACL,MAAI,E,2lEA8DfO,mBAAA,CAIM;IAHJP,KAAK,EAAC;EAA8D,IAEpEO,mBAAA,CAAyC,WAAtC,+BAAkC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}