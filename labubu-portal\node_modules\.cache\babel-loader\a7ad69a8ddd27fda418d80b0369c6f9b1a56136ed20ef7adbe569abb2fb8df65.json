{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport BrushController from '../helper/BrushController.js';\nimport * as brushHelper from '../helper/brushHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport ComponentView from '../../view/Component.js';\nvar elementList = ['axisLine', 'axisTickLabel', 'axisName'];\nvar ParallelAxisView = /** @class */function (_super) {\n  __extends(ParallelAxisView, _super);\n  function ParallelAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelAxisView.type;\n    return _this;\n  }\n  ParallelAxisView.prototype.init = function (ecModel, api) {\n    _super.prototype.init.apply(this, arguments);\n    (this._brushController = new BrushController(api.getZr())).on('brush', zrUtil.bind(this._onBrush, this));\n  };\n  ParallelAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    if (fromAxisAreaSelect(axisModel, ecModel, payload)) {\n      return;\n    }\n    this.axisModel = axisModel;\n    this.api = api;\n    this.group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    this.group.add(this._axisGroup);\n    if (!axisModel.get('show')) {\n      return;\n    }\n    var coordSysModel = getCoordSysModel(axisModel, ecModel);\n    var coordSys = coordSysModel.coordinateSystem;\n    var areaSelectStyle = axisModel.getAreaSelectStyle();\n    var areaWidth = areaSelectStyle.width;\n    var dim = axisModel.axis.dim;\n    var axisLayout = coordSys.getAxisLayout(dim);\n    var builderOpt = zrUtil.extend({\n      strokeContainThreshold: areaWidth\n    }, axisLayout);\n    var axisBuilder = new AxisBuilder(axisModel, builderOpt);\n    zrUtil.each(elementList, axisBuilder.add, axisBuilder);\n    this._axisGroup.add(axisBuilder.getGroup());\n    this._refreshBrushController(builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n  };\n  // /**\n  //  * @override\n  //  */\n  // updateVisual(axisModel, ecModel, api, payload) {\n  //     this._brushController && this._brushController\n  //         .updateCovers(getCoverInfoList(axisModel));\n  // }\n  ParallelAxisView.prototype._refreshBrushController = function (builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api) {\n    // After filtering, axis may change, select area needs to be update.\n    var extent = axisModel.axis.getExtent();\n    var extentLen = extent[1] - extent[0];\n    var extra = Math.min(30, Math.abs(extentLen) * 0.1); // Arbitrary value.\n    // width/height might be negative, which will be\n    // normalized in BoundingRect.\n    var rect = graphic.BoundingRect.create({\n      x: extent[0],\n      y: -areaWidth / 2,\n      width: extentLen,\n      height: areaWidth\n    });\n    rect.x -= extra;\n    rect.width += 2 * extra;\n    this._brushController.mount({\n      enableGlobalPan: true,\n      rotation: builderOpt.rotation,\n      x: builderOpt.position[0],\n      y: builderOpt.position[1]\n    }).setPanels([{\n      panelId: 'pl',\n      clipPath: brushHelper.makeRectPanelClipPath(rect),\n      isTargetByCursor: brushHelper.makeRectIsTargetByCursor(rect, api, coordSysModel),\n      getLinearBrushOtherExtent: brushHelper.makeLinearBrushOtherExtent(rect, 0)\n    }]).enableBrush({\n      brushType: 'lineX',\n      brushStyle: areaSelectStyle,\n      removeOnClick: true\n    }).updateCovers(getCoverInfoList(axisModel));\n  };\n  ParallelAxisView.prototype._onBrush = function (eventParam) {\n    var coverInfoList = eventParam.areas;\n    // Do not cache these object, because the mey be changed.\n    var axisModel = this.axisModel;\n    var axis = axisModel.axis;\n    var intervals = zrUtil.map(coverInfoList, function (coverInfo) {\n      return [axis.coordToData(coverInfo.range[0], true), axis.coordToData(coverInfo.range[1], true)];\n    });\n    // If realtime is true, action is not dispatched on drag end, because\n    // the drag end emits the same params with the last drag move event,\n    // and may have some delay when using touch pad.\n    if (!axisModel.option.realtime === eventParam.isEnd || eventParam.removeOnClick) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'axisAreaSelect',\n        parallelAxisId: axisModel.id,\n        intervals: intervals\n      });\n    }\n  };\n  ParallelAxisView.prototype.dispose = function () {\n    this._brushController.dispose();\n  };\n  ParallelAxisView.type = 'parallelAxis';\n  return ParallelAxisView;\n}(ComponentView);\nfunction fromAxisAreaSelect(axisModel, ecModel, payload) {\n  return payload && payload.type === 'axisAreaSelect' && ecModel.findComponents({\n    mainType: 'parallelAxis',\n    query: payload\n  })[0] === axisModel;\n}\nfunction getCoverInfoList(axisModel) {\n  var axis = axisModel.axis;\n  return zrUtil.map(axisModel.activeIntervals, function (interval) {\n    return {\n      brushType: 'lineX',\n      panelId: 'pl',\n      range: [axis.dataToCoord(interval[0], true), axis.dataToCoord(interval[1], true)]\n    };\n  });\n}\nfunction getCoordSysModel(axisModel, ecModel) {\n  return ecModel.getComponent('parallel', axisModel.get('parallelIndex'));\n}\nexport default ParallelAxisView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "AxisBuilder", "BrushController", "brushHelper", "graphic", "ComponentView", "elementList", "ParallelAxisView", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "ecModel", "api", "_brushController", "getZr", "on", "bind", "_onBrush", "render", "axisModel", "payload", "fromAxisAreaSelect", "group", "removeAll", "oldAxisGroup", "_axisGroup", "Group", "add", "get", "coordSysModel", "getCoordSysModel", "coordSys", "coordinateSystem", "areaSelectStyle", "getAreaSelectStyle", "areaWidth", "width", "dim", "axis", "axisLayout", "getAxisLayout", "builderOpt", "extend", "strokeContainThreshold", "axisBuilder", "each", "getGroup", "_refreshBrushController", "groupTransition", "extent", "getExtent", "extentLen", "extra", "Math", "min", "abs", "rect", "BoundingRect", "create", "x", "y", "height", "mount", "enableGlobalPan", "rotation", "position", "set<PERSON><PERSON><PERSON>", "panelId", "clipPath", "makeRectPanelClipPath", "isTargetByCursor", "makeRectIsTargetByCursor", "getLinearBrushOtherExtent", "makeLinearBrushOtherExtent", "enableBrush", "brushType", "brushStyle", "removeOnClick", "updateCovers", "getCoverInfoList", "eventParam", "coverInfoList", "areas", "intervals", "map", "coverInfo", "coordToData", "range", "option", "realtime", "isEnd", "dispatchAction", "parallelAxisId", "id", "dispose", "findComponents", "mainType", "query", "activeIntervals", "interval", "dataToCoord", "getComponent"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/axis/ParallelAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport BrushController from '../helper/BrushController.js';\nimport * as brushHelper from '../helper/brushHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport ComponentView from '../../view/Component.js';\nvar elementList = ['axisLine', 'axisTickLabel', 'axisName'];\nvar ParallelAxisView = /** @class */function (_super) {\n  __extends(ParallelAxisView, _super);\n  function ParallelAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelAxisView.type;\n    return _this;\n  }\n  ParallelAxisView.prototype.init = function (ecModel, api) {\n    _super.prototype.init.apply(this, arguments);\n    (this._brushController = new BrushController(api.getZr())).on('brush', zrUtil.bind(this._onBrush, this));\n  };\n  ParallelAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    if (fromAxisAreaSelect(axisModel, ecModel, payload)) {\n      return;\n    }\n    this.axisModel = axisModel;\n    this.api = api;\n    this.group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    this.group.add(this._axisGroup);\n    if (!axisModel.get('show')) {\n      return;\n    }\n    var coordSysModel = getCoordSysModel(axisModel, ecModel);\n    var coordSys = coordSysModel.coordinateSystem;\n    var areaSelectStyle = axisModel.getAreaSelectStyle();\n    var areaWidth = areaSelectStyle.width;\n    var dim = axisModel.axis.dim;\n    var axisLayout = coordSys.getAxisLayout(dim);\n    var builderOpt = zrUtil.extend({\n      strokeContainThreshold: areaWidth\n    }, axisLayout);\n    var axisBuilder = new AxisBuilder(axisModel, builderOpt);\n    zrUtil.each(elementList, axisBuilder.add, axisBuilder);\n    this._axisGroup.add(axisBuilder.getGroup());\n    this._refreshBrushController(builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n  };\n  // /**\n  //  * @override\n  //  */\n  // updateVisual(axisModel, ecModel, api, payload) {\n  //     this._brushController && this._brushController\n  //         .updateCovers(getCoverInfoList(axisModel));\n  // }\n  ParallelAxisView.prototype._refreshBrushController = function (builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api) {\n    // After filtering, axis may change, select area needs to be update.\n    var extent = axisModel.axis.getExtent();\n    var extentLen = extent[1] - extent[0];\n    var extra = Math.min(30, Math.abs(extentLen) * 0.1); // Arbitrary value.\n    // width/height might be negative, which will be\n    // normalized in BoundingRect.\n    var rect = graphic.BoundingRect.create({\n      x: extent[0],\n      y: -areaWidth / 2,\n      width: extentLen,\n      height: areaWidth\n    });\n    rect.x -= extra;\n    rect.width += 2 * extra;\n    this._brushController.mount({\n      enableGlobalPan: true,\n      rotation: builderOpt.rotation,\n      x: builderOpt.position[0],\n      y: builderOpt.position[1]\n    }).setPanels([{\n      panelId: 'pl',\n      clipPath: brushHelper.makeRectPanelClipPath(rect),\n      isTargetByCursor: brushHelper.makeRectIsTargetByCursor(rect, api, coordSysModel),\n      getLinearBrushOtherExtent: brushHelper.makeLinearBrushOtherExtent(rect, 0)\n    }]).enableBrush({\n      brushType: 'lineX',\n      brushStyle: areaSelectStyle,\n      removeOnClick: true\n    }).updateCovers(getCoverInfoList(axisModel));\n  };\n  ParallelAxisView.prototype._onBrush = function (eventParam) {\n    var coverInfoList = eventParam.areas;\n    // Do not cache these object, because the mey be changed.\n    var axisModel = this.axisModel;\n    var axis = axisModel.axis;\n    var intervals = zrUtil.map(coverInfoList, function (coverInfo) {\n      return [axis.coordToData(coverInfo.range[0], true), axis.coordToData(coverInfo.range[1], true)];\n    });\n    // If realtime is true, action is not dispatched on drag end, because\n    // the drag end emits the same params with the last drag move event,\n    // and may have some delay when using touch pad.\n    if (!axisModel.option.realtime === eventParam.isEnd || eventParam.removeOnClick) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'axisAreaSelect',\n        parallelAxisId: axisModel.id,\n        intervals: intervals\n      });\n    }\n  };\n  ParallelAxisView.prototype.dispose = function () {\n    this._brushController.dispose();\n  };\n  ParallelAxisView.type = 'parallelAxis';\n  return ParallelAxisView;\n}(ComponentView);\nfunction fromAxisAreaSelect(axisModel, ecModel, payload) {\n  return payload && payload.type === 'axisAreaSelect' && ecModel.findComponents({\n    mainType: 'parallelAxis',\n    query: payload\n  })[0] === axisModel;\n}\nfunction getCoverInfoList(axisModel) {\n  var axis = axisModel.axis;\n  return zrUtil.map(axisModel.activeIntervals, function (interval) {\n    return {\n      brushType: 'lineX',\n      panelId: 'pl',\n      range: [axis.dataToCoord(interval[0], true), axis.dataToCoord(interval[1], true)]\n    };\n  });\n}\nfunction getCoordSysModel(axisModel, ecModel) {\n  return ecModel.getComponent('parallel', axisModel.get('parallelIndex'));\n}\nexport default ParallelAxisView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAO,KAAKC,WAAW,MAAM,0BAA0B;AACvD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,IAAIC,WAAW,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;AAC3D,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACpDT,SAAS,CAACQ,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClC,OAAOH,KAAK;EACd;EACAF,gBAAgB,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACxDR,MAAM,CAACK,SAAS,CAACC,IAAI,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5C,CAAC,IAAI,CAACM,gBAAgB,GAAG,IAAIf,eAAe,CAACc,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,OAAO,EAAEnB,MAAM,CAACoB,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC,CAAC;EAC1G,CAAC;EACDd,gBAAgB,CAACM,SAAS,CAACS,MAAM,GAAG,UAAUC,SAAS,EAAER,OAAO,EAAEC,GAAG,EAAEQ,OAAO,EAAE;IAC9E,IAAIC,kBAAkB,CAACF,SAAS,EAAER,OAAO,EAAES,OAAO,CAAC,EAAE;MACnD;IACF;IACA,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACP,GAAG,GAAGA,GAAG;IACd,IAAI,CAACU,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIC,YAAY,GAAG,IAAI,CAACC,UAAU;IAClC,IAAI,CAACA,UAAU,GAAG,IAAIzB,OAAO,CAAC0B,KAAK,CAAC,CAAC;IACrC,IAAI,CAACJ,KAAK,CAACK,GAAG,CAAC,IAAI,CAACF,UAAU,CAAC;IAC/B,IAAI,CAACN,SAAS,CAACS,GAAG,CAAC,MAAM,CAAC,EAAE;MAC1B;IACF;IACA,IAAIC,aAAa,GAAGC,gBAAgB,CAACX,SAAS,EAAER,OAAO,CAAC;IACxD,IAAIoB,QAAQ,GAAGF,aAAa,CAACG,gBAAgB;IAC7C,IAAIC,eAAe,GAAGd,SAAS,CAACe,kBAAkB,CAAC,CAAC;IACpD,IAAIC,SAAS,GAAGF,eAAe,CAACG,KAAK;IACrC,IAAIC,GAAG,GAAGlB,SAAS,CAACmB,IAAI,CAACD,GAAG;IAC5B,IAAIE,UAAU,GAAGR,QAAQ,CAACS,aAAa,CAACH,GAAG,CAAC;IAC5C,IAAII,UAAU,GAAG7C,MAAM,CAAC8C,MAAM,CAAC;MAC7BC,sBAAsB,EAAER;IAC1B,CAAC,EAAEI,UAAU,CAAC;IACd,IAAIK,WAAW,GAAG,IAAI/C,WAAW,CAACsB,SAAS,EAAEsB,UAAU,CAAC;IACxD7C,MAAM,CAACiD,IAAI,CAAC3C,WAAW,EAAE0C,WAAW,CAACjB,GAAG,EAAEiB,WAAW,CAAC;IACtD,IAAI,CAACnB,UAAU,CAACE,GAAG,CAACiB,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACC,uBAAuB,CAACN,UAAU,EAAER,eAAe,EAAEd,SAAS,EAAEU,aAAa,EAAEM,SAAS,EAAEvB,GAAG,CAAC;IACnGZ,OAAO,CAACgD,eAAe,CAACxB,YAAY,EAAE,IAAI,CAACC,UAAU,EAAEN,SAAS,CAAC;EACnE,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACAhB,gBAAgB,CAACM,SAAS,CAACsC,uBAAuB,GAAG,UAAUN,UAAU,EAAER,eAAe,EAAEd,SAAS,EAAEU,aAAa,EAAEM,SAAS,EAAEvB,GAAG,EAAE;IACpI;IACA,IAAIqC,MAAM,GAAG9B,SAAS,CAACmB,IAAI,CAACY,SAAS,CAAC,CAAC;IACvC,IAAIC,SAAS,GAAGF,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IACrC,IAAIG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAACJ,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACrD;IACA;IACA,IAAIK,IAAI,GAAGxD,OAAO,CAACyD,YAAY,CAACC,MAAM,CAAC;MACrCC,CAAC,EAAEV,MAAM,CAAC,CAAC,CAAC;MACZW,CAAC,EAAE,CAACzB,SAAS,GAAG,CAAC;MACjBC,KAAK,EAAEe,SAAS;MAChBU,MAAM,EAAE1B;IACV,CAAC,CAAC;IACFqB,IAAI,CAACG,CAAC,IAAIP,KAAK;IACfI,IAAI,CAACpB,KAAK,IAAI,CAAC,GAAGgB,KAAK;IACvB,IAAI,CAACvC,gBAAgB,CAACiD,KAAK,CAAC;MAC1BC,eAAe,EAAE,IAAI;MACrBC,QAAQ,EAAEvB,UAAU,CAACuB,QAAQ;MAC7BL,CAAC,EAAElB,UAAU,CAACwB,QAAQ,CAAC,CAAC,CAAC;MACzBL,CAAC,EAAEnB,UAAU,CAACwB,QAAQ,CAAC,CAAC;IAC1B,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;MACZC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAErE,WAAW,CAACsE,qBAAqB,CAACb,IAAI,CAAC;MACjDc,gBAAgB,EAAEvE,WAAW,CAACwE,wBAAwB,CAACf,IAAI,EAAE5C,GAAG,EAAEiB,aAAa,CAAC;MAChF2C,yBAAyB,EAAEzE,WAAW,CAAC0E,0BAA0B,CAACjB,IAAI,EAAE,CAAC;IAC3E,CAAC,CAAC,CAAC,CAACkB,WAAW,CAAC;MACdC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE3C,eAAe;MAC3B4C,aAAa,EAAE;IACjB,CAAC,CAAC,CAACC,YAAY,CAACC,gBAAgB,CAAC5D,SAAS,CAAC,CAAC;EAC9C,CAAC;EACDhB,gBAAgB,CAACM,SAAS,CAACQ,QAAQ,GAAG,UAAU+D,UAAU,EAAE;IAC1D,IAAIC,aAAa,GAAGD,UAAU,CAACE,KAAK;IACpC;IACA,IAAI/D,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAImB,IAAI,GAAGnB,SAAS,CAACmB,IAAI;IACzB,IAAI6C,SAAS,GAAGvF,MAAM,CAACwF,GAAG,CAACH,aAAa,EAAE,UAAUI,SAAS,EAAE;MAC7D,OAAO,CAAC/C,IAAI,CAACgD,WAAW,CAACD,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAEjD,IAAI,CAACgD,WAAW,CAACD,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACjG,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACpE,SAAS,CAACqE,MAAM,CAACC,QAAQ,KAAKT,UAAU,CAACU,KAAK,IAAIV,UAAU,CAACH,aAAa,EAAE;MAC/E;MACA,IAAI,CAACjE,GAAG,CAAC+E,cAAc,CAAC;QACtBnF,IAAI,EAAE,gBAAgB;QACtBoF,cAAc,EAAEzE,SAAS,CAAC0E,EAAE;QAC5BV,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EACDhF,gBAAgB,CAACM,SAAS,CAACqF,OAAO,GAAG,YAAY;IAC/C,IAAI,CAACjF,gBAAgB,CAACiF,OAAO,CAAC,CAAC;EACjC,CAAC;EACD3F,gBAAgB,CAACK,IAAI,GAAG,cAAc;EACtC,OAAOL,gBAAgB;AACzB,CAAC,CAACF,aAAa,CAAC;AAChB,SAASoB,kBAAkBA,CAACF,SAAS,EAAER,OAAO,EAAES,OAAO,EAAE;EACvD,OAAOA,OAAO,IAAIA,OAAO,CAACZ,IAAI,KAAK,gBAAgB,IAAIG,OAAO,CAACoF,cAAc,CAAC;IAC5EC,QAAQ,EAAE,cAAc;IACxBC,KAAK,EAAE7E;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKD,SAAS;AACrB;AACA,SAAS4D,gBAAgBA,CAAC5D,SAAS,EAAE;EACnC,IAAImB,IAAI,GAAGnB,SAAS,CAACmB,IAAI;EACzB,OAAO1C,MAAM,CAACwF,GAAG,CAACjE,SAAS,CAAC+E,eAAe,EAAE,UAAUC,QAAQ,EAAE;IAC/D,OAAO;MACLxB,SAAS,EAAE,OAAO;MAClBR,OAAO,EAAE,IAAI;MACboB,KAAK,EAAE,CAACjD,IAAI,CAAC8D,WAAW,CAACD,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE7D,IAAI,CAAC8D,WAAW,CAACD,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;IAClF,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASrE,gBAAgBA,CAACX,SAAS,EAAER,OAAO,EAAE;EAC5C,OAAOA,OAAO,CAAC0F,YAAY,CAAC,UAAU,EAAElF,SAAS,CAACS,GAAG,CAAC,eAAe,CAAC,CAAC;AACzE;AACA,eAAezB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}