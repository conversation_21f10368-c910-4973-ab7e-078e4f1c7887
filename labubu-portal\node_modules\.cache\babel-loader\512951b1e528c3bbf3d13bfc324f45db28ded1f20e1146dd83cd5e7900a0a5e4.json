{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { e as elementChildren, b as elementParents, d as elementOffset, k as getTranslate } from '../shared/utils.mjs';\nfunction Zoom(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  extendParams({\n    zoom: {\n      enabled: false,\n      limitToOriginalSize: false,\n      maxRatio: 3,\n      minRatio: 1,\n      panOnMouseMove: false,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed'\n    }\n  });\n  swiper.zoom = {\n    enabled: false\n  };\n  let currentScale = 1;\n  let isScaling = false;\n  let isPanningWithMouse = false;\n  let mousePanStart = {\n    x: 0,\n    y: 0\n  };\n  const mousePanSensitivity = -3; // Negative to invert pan direction\n  let fakeGestureTouched;\n  let fakeGestureMoved;\n  const evCache = [];\n  const gesture = {\n    originX: 0,\n    originY: 0,\n    slideEl: undefined,\n    slideWidth: undefined,\n    slideHeight: undefined,\n    imageEl: undefined,\n    imageWrapEl: undefined,\n    maxRatio: 3\n  };\n  const image = {\n    isTouched: undefined,\n    isMoved: undefined,\n    currentX: undefined,\n    currentY: undefined,\n    minX: undefined,\n    minY: undefined,\n    maxX: undefined,\n    maxY: undefined,\n    width: undefined,\n    height: undefined,\n    startX: undefined,\n    startY: undefined,\n    touchesStart: {},\n    touchesCurrent: {}\n  };\n  const velocity = {\n    x: undefined,\n    y: undefined,\n    prevPositionX: undefined,\n    prevPositionY: undefined,\n    prevTime: undefined\n  };\n  let scale = 1;\n  Object.defineProperty(swiper.zoom, 'scale', {\n    get() {\n      return scale;\n    },\n    set(value) {\n      if (scale !== value) {\n        const imageEl = gesture.imageEl;\n        const slideEl = gesture.slideEl;\n        emit('zoomChange', value, imageEl, slideEl);\n      }\n      scale = value;\n    }\n  });\n  function getDistanceBetweenTouches() {\n    if (evCache.length < 2) return 1;\n    const x1 = evCache[0].pageX;\n    const y1 = evCache[0].pageY;\n    const x2 = evCache[1].pageX;\n    const y2 = evCache[1].pageY;\n    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);\n    return distance;\n  }\n  function getMaxRatio() {\n    const params = swiper.params.zoom;\n    const maxRatio = gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    if (params.limitToOriginalSize && gesture.imageEl && gesture.imageEl.naturalWidth) {\n      const imageMaxRatio = gesture.imageEl.naturalWidth / gesture.imageEl.offsetWidth;\n      return Math.min(imageMaxRatio, maxRatio);\n    }\n    return maxRatio;\n  }\n  function getScaleOrigin() {\n    if (evCache.length < 2) return {\n      x: null,\n      y: null\n    };\n    const box = gesture.imageEl.getBoundingClientRect();\n    return [(evCache[0].pageX + (evCache[1].pageX - evCache[0].pageX) / 2 - box.x - window.scrollX) / currentScale, (evCache[0].pageY + (evCache[1].pageY - evCache[0].pageY) / 2 - box.y - window.scrollY) / currentScale];\n  }\n  function getSlideSelector() {\n    return swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  }\n  function eventWithinSlide(e) {\n    const slideSelector = getSlideSelector();\n    if (e.target.matches(slideSelector)) return true;\n    if (swiper.slides.filter(slideEl => slideEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n  function eventWithinZoomContainer(e) {\n    const selector = `.${swiper.params.zoom.containerClass}`;\n    if (e.target.matches(selector)) return true;\n    if ([...swiper.hostEl.querySelectorAll(selector)].filter(containerEl => containerEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n\n  // Events\n  function onGestureStart(e) {\n    if (e.pointerType === 'mouse') {\n      evCache.splice(0, evCache.length);\n    }\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    evCache.push(e);\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureTouched = true;\n    gesture.scaleStart = getDistanceBetweenTouches();\n    if (!gesture.slideEl) {\n      gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      if (!gesture.slideEl) gesture.slideEl = swiper.slides[swiper.activeIndex];\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n      if (!gesture.imageWrapEl) {\n        gesture.imageEl = undefined;\n        return;\n      }\n      gesture.maxRatio = getMaxRatio();\n    }\n    if (gesture.imageEl) {\n      const [originX, originY] = getScaleOrigin();\n      gesture.originX = originX;\n      gesture.originY = originY;\n      gesture.imageEl.style.transitionDuration = '0ms';\n    }\n    isScaling = true;\n  }\n  function onGestureChange(e) {\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache[pointerIndex] = e;\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureMoved = true;\n    gesture.scaleMove = getDistanceBetweenTouches();\n    if (!gesture.imageEl) {\n      return;\n    }\n    zoom.scale = gesture.scaleMove / gesture.scaleStart * currentScale;\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = gesture.maxRatio - 1 + (zoom.scale - gesture.maxRatio + 1) ** 0.5;\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = params.minRatio + 1 - (params.minRatio - zoom.scale + 1) ** 0.5;\n    }\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function onGestureEnd(e) {\n    if (!eventWithinSlide(e)) return;\n    if (e.pointerType === 'mouse' && e.type === 'pointerout') return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache.splice(pointerIndex, 1);\n    if (!fakeGestureTouched || !fakeGestureMoved) {\n      return;\n    }\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    if (!gesture.imageEl) return;\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.imageEl.style.transitionDuration = `${swiper.params.speed}ms`;\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n    currentScale = zoom.scale;\n    isScaling = false;\n    if (zoom.scale > 1 && gesture.slideEl) {\n      gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    } else if (zoom.scale <= 1 && gesture.slideEl) {\n      gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    }\n    if (zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n      gesture.slideEl = undefined;\n    }\n  }\n  let allowTouchMoveTimeout;\n  function allowTouchMove() {\n    swiper.touchEventsData.preventTouchMoveFromPointerMove = false;\n  }\n  function preventTouchMove() {\n    clearTimeout(allowTouchMoveTimeout);\n    swiper.touchEventsData.preventTouchMoveFromPointerMove = true;\n    allowTouchMoveTimeout = setTimeout(() => {\n      if (swiper.destroyed) return;\n      allowTouchMove();\n    });\n  }\n  function onTouchStart(e) {\n    const device = swiper.device;\n    if (!gesture.imageEl) return;\n    if (image.isTouched) return;\n    if (device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    const event = evCache.length > 0 ? evCache[0] : e;\n    image.touchesStart.x = event.pageX;\n    image.touchesStart.y = event.pageY;\n  }\n  function onTouchMove(e) {\n    const isMouseEvent = e.pointerType === 'mouse';\n    const isMousePan = isMouseEvent && swiper.params.zoom.panOnMouseMove;\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) {\n      return;\n    }\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) {\n      return;\n    }\n    if (!image.isTouched || !gesture.slideEl) {\n      if (isMousePan) onMouseMove(e);\n      return;\n    }\n    if (isMousePan) {\n      onMouseMove(e);\n      return;\n    }\n    if (!image.isMoved) {\n      image.width = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      image.height = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      image.startX = getTranslate(gesture.imageWrapEl, 'x') || 0;\n      image.startY = getTranslate(gesture.imageWrapEl, 'y') || 0;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      gesture.imageWrapEl.style.transitionDuration = '0ms';\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.touchesCurrent.x = evCache.length > 0 ? evCache[0].pageX : e.pageX;\n    image.touchesCurrent.y = evCache.length > 0 ? evCache[0].pageY : e.pageY;\n    const touchesDiff = Math.max(Math.abs(image.touchesCurrent.x - image.touchesStart.x), Math.abs(image.touchesCurrent.y - image.touchesStart.y));\n    if (touchesDiff > 5) {\n      swiper.allowClick = false;\n    }\n    if (!image.isMoved && !isScaling) {\n      if (swiper.isHorizontal() && (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x || Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)) {\n        image.isTouched = false;\n        allowTouchMove();\n        return;\n      }\n      if (!swiper.isHorizontal() && (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y || Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)) {\n        image.isTouched = false;\n        allowTouchMove();\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n    preventTouchMove();\n    image.isMoved = true;\n    const scaleRatio = (zoom.scale - currentScale) / (gesture.maxRatio - swiper.params.zoom.minRatio);\n    const {\n      originX,\n      originY\n    } = gesture;\n    image.currentX = image.touchesCurrent.x - image.touchesStart.x + image.startX + scaleRatio * (image.width - originX * 2);\n    image.currentY = image.touchesCurrent.y - image.touchesStart.y + image.startY + scaleRatio * (image.height - originY * 2);\n    if (image.currentX < image.minX) {\n      image.currentX = image.minX + 1 - (image.minX - image.currentX + 1) ** 0.8;\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = image.maxX - 1 + (image.currentX - image.maxX + 1) ** 0.8;\n    }\n    if (image.currentY < image.minY) {\n      image.currentY = image.minY + 1 - (image.minY - image.currentY + 1) ** 0.8;\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = image.maxY - 1 + (image.currentY - image.maxY + 1) ** 0.8;\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTouchEnd() {\n    const zoom = swiper.zoom;\n    evCache.length = 0;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n    gesture.imageWrapEl.style.transitionDuration = `${momentumDuration}ms`;\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTransitionEnd() {\n    const zoom = swiper.zoom;\n    if (gesture.slideEl && swiper.activeIndex !== swiper.slides.indexOf(gesture.slideEl)) {\n      if (gesture.imageEl) {\n        gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n      }\n      if (gesture.imageWrapEl) {\n        gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n      }\n      gesture.slideEl.classList.remove(`${swiper.params.zoom.zoomedSlideClass}`);\n      zoom.scale = 1;\n      currentScale = 1;\n      gesture.slideEl = undefined;\n      gesture.imageEl = undefined;\n      gesture.imageWrapEl = undefined;\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n  }\n  function onMouseMove(e) {\n    // Only pan if zoomed in and mouse panning is enabled\n    if (currentScale <= 1 || !gesture.imageWrapEl) return;\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) return;\n    const currentTransform = window.getComputedStyle(gesture.imageWrapEl).transform;\n    const matrix = new window.DOMMatrix(currentTransform);\n    if (!isPanningWithMouse) {\n      isPanningWithMouse = true;\n      mousePanStart.x = e.clientX;\n      mousePanStart.y = e.clientY;\n      image.startX = matrix.e;\n      image.startY = matrix.f;\n      image.width = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      image.height = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      return;\n    }\n    const deltaX = (e.clientX - mousePanStart.x) * mousePanSensitivity;\n    const deltaY = (e.clientY - mousePanStart.y) * mousePanSensitivity;\n    const scaledWidth = image.width * currentScale;\n    const scaledHeight = image.height * currentScale;\n    const slideWidth = gesture.slideWidth;\n    const slideHeight = gesture.slideHeight;\n    const minX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n    const maxX = -minX;\n    const minY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n    const maxY = -minY;\n    const newX = Math.max(Math.min(image.startX + deltaX, maxX), minX);\n    const newY = Math.max(Math.min(image.startY + deltaY, maxY), minY);\n    gesture.imageWrapEl.style.transitionDuration = '0ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${newX}px, ${newY}px, 0)`;\n    mousePanStart.x = e.clientX;\n    mousePanStart.y = e.clientY;\n    image.startX = newX;\n    image.startY = newY;\n    image.currentX = newX;\n    image.currentY = newY;\n  }\n  function zoomIn(e) {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (e && e.target) {\n        gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      }\n      if (!gesture.slideEl) {\n        if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n          gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n        } else {\n          gesture.slideEl = swiper.slides[swiper.activeIndex];\n        }\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.touchAction = 'none';\n    }\n    gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.pageX;\n      touchY = e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n    const prevScale = currentScale;\n    const forceZoomRatio = typeof e === 'number' ? e : null;\n    if (currentScale === 1 && forceZoomRatio) {\n      touchX = undefined;\n      touchY = undefined;\n      image.touchesStart.x = undefined;\n      image.touchesStart.y = undefined;\n    }\n    const maxRatio = getMaxRatio();\n    zoom.scale = forceZoomRatio || maxRatio;\n    currentScale = forceZoomRatio || maxRatio;\n    if (e && !(currentScale === 1 && forceZoomRatio)) {\n      slideWidth = gesture.slideEl.offsetWidth;\n      slideHeight = gesture.slideEl.offsetHeight;\n      offsetX = elementOffset(gesture.slideEl).left + window.scrollX;\n      offsetY = elementOffset(gesture.slideEl).top + window.scrollY;\n      diffX = offsetX + slideWidth / 2 - touchX;\n      diffY = offsetY + slideHeight / 2 - touchY;\n      imageWidth = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      imageHeight = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n      translateMinX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n      translateMinY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n      if (prevScale > 0 && forceZoomRatio && typeof image.currentX === 'number' && typeof image.currentY === 'number') {\n        translateX = image.currentX * zoom.scale / prevScale;\n        translateY = image.currentY * zoom.scale / prevScale;\n      } else {\n        translateX = diffX * zoom.scale;\n        translateY = diffY * zoom.scale;\n      }\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    if (forceZoomRatio && zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n    image.currentX = translateX;\n    image.currentY = translateY;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${translateX}px, ${translateY}px,0)`;\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function zoomOut() {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n      } else {\n        gesture.slideEl = swiper.slides[swiper.activeIndex];\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = '';\n      swiper.wrapperEl.style.touchAction = '';\n    }\n    zoom.scale = 1;\n    currentScale = 1;\n    image.currentX = undefined;\n    image.currentY = undefined;\n    image.touchesStart.x = undefined;\n    image.touchesStart.y = undefined;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n    gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    gesture.slideEl = undefined;\n    gesture.originX = 0;\n    gesture.originY = 0;\n    if (swiper.params.zoom.panOnMouseMove) {\n      mousePanStart = {\n        x: 0,\n        y: 0\n      };\n      if (isPanningWithMouse) {\n        isPanningWithMouse = false;\n        image.startX = 0;\n        image.startY = 0;\n      }\n    }\n  }\n\n  // Toggle Zoom\n  function zoomToggle(e) {\n    const zoom = swiper.zoom;\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoomOut();\n    } else {\n      // Zoom In\n      zoomIn(e);\n    }\n  }\n  function getListeners() {\n    const passiveListener = swiper.params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    const activeListenerWithCapture = swiper.params.passiveListeners ? {\n      passive: false,\n      capture: true\n    } : true;\n    return {\n      passiveListener,\n      activeListenerWithCapture\n    };\n  }\n\n  // Attach/Detach Events\n  function enable() {\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.addEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.addEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.addEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.addEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  function disable() {\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n    zoom.enabled = false;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.removeEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.removeEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.removeEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.removeEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  on('init', () => {\n    if (swiper.params.zoom.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    disable();\n  });\n  on('touchStart', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchStart(e);\n  });\n  on('touchEnd', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchEnd();\n  });\n  on('doubleTap', (_s, e) => {\n    if (!swiper.animating && swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n      zoomToggle(e);\n    }\n  });\n  on('transitionEnd', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n      onTransitionEnd();\n    }\n  });\n  on('slideChange', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n      onTransitionEnd();\n    }\n  });\n  Object.assign(swiper.zoom, {\n    enable,\n    disable,\n    in: zoomIn,\n    out: zoomOut,\n    toggle: zoomToggle\n  });\n}\nexport { Zoom as default };", "map": {"version": 3, "names": ["a", "getWindow", "e", "elementChildren", "b", "elementParents", "d", "elementOffset", "k", "getTranslate", "Zoom", "_ref", "swiper", "extendParams", "on", "emit", "window", "zoom", "enabled", "limitToOriginalSize", "maxRatio", "minRatio", "panOnMouseMove", "toggle", "containerClass", "zoomedSlideClass", "currentScale", "isScaling", "isPanningWithMouse", "mousePanStart", "x", "y", "mousePanSensitivity", "fakeGestureTouched", "fakeGestureMoved", "ev<PERSON><PERSON>", "gesture", "originX", "originY", "slideEl", "undefined", "slideWidth", "slideHeight", "imageEl", "imageWrapEl", "image", "isTouched", "isMoved", "currentX", "currentY", "minX", "minY", "maxX", "maxY", "width", "height", "startX", "startY", "touchesStart", "touchesCurrent", "velocity", "prevPositionX", "prevPositionY", "prevTime", "scale", "Object", "defineProperty", "get", "set", "value", "getDistanceBetweenTouches", "length", "x1", "pageX", "y1", "pageY", "x2", "y2", "distance", "Math", "sqrt", "getMaxRatio", "params", "getAttribute", "naturalWidth", "imageMaxRatio", "offsetWidth", "min", "getScaleOrigin", "box", "getBoundingClientRect", "scrollX", "scrollY", "getSlideSelector", "isElement", "slideClass", "eventWithinSlide", "slideSelector", "target", "matches", "slides", "filter", "contains", "eventWithinZoomContainer", "selector", "hostEl", "querySelectorAll", "containerEl", "onGestureStart", "pointerType", "splice", "push", "scaleStart", "closest", "activeIndex", "querySelector", "style", "transitionDuration", "onGestureChange", "pointerIndex", "findIndex", "cachedEv", "pointerId", "scaleMove", "transform", "onGestureEnd", "type", "max", "speed", "classList", "add", "remove", "allowTouchMoveTimeout", "allowTouchMove", "touchEventsData", "preventTouchMoveFromPointerMove", "preventTouchMove", "clearTimeout", "setTimeout", "destroyed", "onTouchStart", "device", "android", "cancelable", "preventDefault", "event", "onTouchMove", "isMouseEvent", "isMousePan", "onMouseMove", "clientWidth", "offsetHeight", "clientHeight", "scaledWidth", "scaledHeight", "touchesDiff", "abs", "allowClick", "isHorizontal", "floor", "stopPropagation", "scaleRatio", "Date", "now", "onTouchEnd", "momentumDurationX", "momentumDurationY", "momentumDistanceX", "newPositionX", "momentumDistanceY", "newPositionY", "momentumDuration", "onTransitionEnd", "indexOf", "currentTransform", "getComputedStyle", "matrix", "DOMMatrix", "clientX", "clientY", "f", "deltaX", "deltaY", "newX", "newY", "zoomIn", "virtual", "slidesEl", "slideActiveClass", "cssMode", "wrapperEl", "overflow", "touchAction", "touchX", "touchY", "offsetX", "offsetY", "diffX", "diffY", "translateX", "translateY", "imageWidth", "imageHeight", "translateMinX", "translateMinY", "translateMaxX", "translateMaxY", "prevScale", "forceZoomRatio", "left", "top", "zoomOut", "zoomToggle", "getListeners", "passiveListener", "passiveListeners", "passive", "capture", "activeListenerWithCapture", "enable", "addEventListener", "for<PERSON>ach", "eventName", "disable", "removeEventListener", "_s", "animating", "assign", "in", "out", "default"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/swiper/modules/zoom.mjs"], "sourcesContent": ["import { a as getWindow } from '../shared/ssr-window.esm.mjs';\nimport { e as elementChildren, b as elementParents, d as elementOffset, k as getTranslate } from '../shared/utils.mjs';\n\nfunction Zoom(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  extendParams({\n    zoom: {\n      enabled: false,\n      limitToOriginalSize: false,\n      maxRatio: 3,\n      minRatio: 1,\n      panOnMouseMove: false,\n      toggle: true,\n      containerClass: 'swiper-zoom-container',\n      zoomedSlideClass: 'swiper-slide-zoomed'\n    }\n  });\n  swiper.zoom = {\n    enabled: false\n  };\n  let currentScale = 1;\n  let isScaling = false;\n  let isPanningWithMouse = false;\n  let mousePanStart = {\n    x: 0,\n    y: 0\n  };\n  const mousePanSensitivity = -3; // Negative to invert pan direction\n  let fakeGestureTouched;\n  let fakeGestureMoved;\n  const evCache = [];\n  const gesture = {\n    originX: 0,\n    originY: 0,\n    slideEl: undefined,\n    slideWidth: undefined,\n    slideHeight: undefined,\n    imageEl: undefined,\n    imageWrapEl: undefined,\n    maxRatio: 3\n  };\n  const image = {\n    isTouched: undefined,\n    isMoved: undefined,\n    currentX: undefined,\n    currentY: undefined,\n    minX: undefined,\n    minY: undefined,\n    maxX: undefined,\n    maxY: undefined,\n    width: undefined,\n    height: undefined,\n    startX: undefined,\n    startY: undefined,\n    touchesStart: {},\n    touchesCurrent: {}\n  };\n  const velocity = {\n    x: undefined,\n    y: undefined,\n    prevPositionX: undefined,\n    prevPositionY: undefined,\n    prevTime: undefined\n  };\n  let scale = 1;\n  Object.defineProperty(swiper.zoom, 'scale', {\n    get() {\n      return scale;\n    },\n    set(value) {\n      if (scale !== value) {\n        const imageEl = gesture.imageEl;\n        const slideEl = gesture.slideEl;\n        emit('zoomChange', value, imageEl, slideEl);\n      }\n      scale = value;\n    }\n  });\n  function getDistanceBetweenTouches() {\n    if (evCache.length < 2) return 1;\n    const x1 = evCache[0].pageX;\n    const y1 = evCache[0].pageY;\n    const x2 = evCache[1].pageX;\n    const y2 = evCache[1].pageY;\n    const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);\n    return distance;\n  }\n  function getMaxRatio() {\n    const params = swiper.params.zoom;\n    const maxRatio = gesture.imageWrapEl.getAttribute('data-swiper-zoom') || params.maxRatio;\n    if (params.limitToOriginalSize && gesture.imageEl && gesture.imageEl.naturalWidth) {\n      const imageMaxRatio = gesture.imageEl.naturalWidth / gesture.imageEl.offsetWidth;\n      return Math.min(imageMaxRatio, maxRatio);\n    }\n    return maxRatio;\n  }\n  function getScaleOrigin() {\n    if (evCache.length < 2) return {\n      x: null,\n      y: null\n    };\n    const box = gesture.imageEl.getBoundingClientRect();\n    return [(evCache[0].pageX + (evCache[1].pageX - evCache[0].pageX) / 2 - box.x - window.scrollX) / currentScale, (evCache[0].pageY + (evCache[1].pageY - evCache[0].pageY) / 2 - box.y - window.scrollY) / currentScale];\n  }\n  function getSlideSelector() {\n    return swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  }\n  function eventWithinSlide(e) {\n    const slideSelector = getSlideSelector();\n    if (e.target.matches(slideSelector)) return true;\n    if (swiper.slides.filter(slideEl => slideEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n  function eventWithinZoomContainer(e) {\n    const selector = `.${swiper.params.zoom.containerClass}`;\n    if (e.target.matches(selector)) return true;\n    if ([...swiper.hostEl.querySelectorAll(selector)].filter(containerEl => containerEl.contains(e.target)).length > 0) return true;\n    return false;\n  }\n\n  // Events\n  function onGestureStart(e) {\n    if (e.pointerType === 'mouse') {\n      evCache.splice(0, evCache.length);\n    }\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    evCache.push(e);\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureTouched = true;\n    gesture.scaleStart = getDistanceBetweenTouches();\n    if (!gesture.slideEl) {\n      gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      if (!gesture.slideEl) gesture.slideEl = swiper.slides[swiper.activeIndex];\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n      if (!gesture.imageWrapEl) {\n        gesture.imageEl = undefined;\n        return;\n      }\n      gesture.maxRatio = getMaxRatio();\n    }\n    if (gesture.imageEl) {\n      const [originX, originY] = getScaleOrigin();\n      gesture.originX = originX;\n      gesture.originY = originY;\n      gesture.imageEl.style.transitionDuration = '0ms';\n    }\n    isScaling = true;\n  }\n  function onGestureChange(e) {\n    if (!eventWithinSlide(e)) return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache[pointerIndex] = e;\n    if (evCache.length < 2) {\n      return;\n    }\n    fakeGestureMoved = true;\n    gesture.scaleMove = getDistanceBetweenTouches();\n    if (!gesture.imageEl) {\n      return;\n    }\n    zoom.scale = gesture.scaleMove / gesture.scaleStart * currentScale;\n    if (zoom.scale > gesture.maxRatio) {\n      zoom.scale = gesture.maxRatio - 1 + (zoom.scale - gesture.maxRatio + 1) ** 0.5;\n    }\n    if (zoom.scale < params.minRatio) {\n      zoom.scale = params.minRatio + 1 - (params.minRatio - zoom.scale + 1) ** 0.5;\n    }\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function onGestureEnd(e) {\n    if (!eventWithinSlide(e)) return;\n    if (e.pointerType === 'mouse' && e.type === 'pointerout') return;\n    const params = swiper.params.zoom;\n    const zoom = swiper.zoom;\n    const pointerIndex = evCache.findIndex(cachedEv => cachedEv.pointerId === e.pointerId);\n    if (pointerIndex >= 0) evCache.splice(pointerIndex, 1);\n    if (!fakeGestureTouched || !fakeGestureMoved) {\n      return;\n    }\n    fakeGestureTouched = false;\n    fakeGestureMoved = false;\n    if (!gesture.imageEl) return;\n    zoom.scale = Math.max(Math.min(zoom.scale, gesture.maxRatio), params.minRatio);\n    gesture.imageEl.style.transitionDuration = `${swiper.params.speed}ms`;\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n    currentScale = zoom.scale;\n    isScaling = false;\n    if (zoom.scale > 1 && gesture.slideEl) {\n      gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    } else if (zoom.scale <= 1 && gesture.slideEl) {\n      gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    }\n    if (zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n      gesture.slideEl = undefined;\n    }\n  }\n  let allowTouchMoveTimeout;\n  function allowTouchMove() {\n    swiper.touchEventsData.preventTouchMoveFromPointerMove = false;\n  }\n  function preventTouchMove() {\n    clearTimeout(allowTouchMoveTimeout);\n    swiper.touchEventsData.preventTouchMoveFromPointerMove = true;\n    allowTouchMoveTimeout = setTimeout(() => {\n      if (swiper.destroyed) return;\n      allowTouchMove();\n    });\n  }\n  function onTouchStart(e) {\n    const device = swiper.device;\n    if (!gesture.imageEl) return;\n    if (image.isTouched) return;\n    if (device.android && e.cancelable) e.preventDefault();\n    image.isTouched = true;\n    const event = evCache.length > 0 ? evCache[0] : e;\n    image.touchesStart.x = event.pageX;\n    image.touchesStart.y = event.pageY;\n  }\n  function onTouchMove(e) {\n    const isMouseEvent = e.pointerType === 'mouse';\n    const isMousePan = isMouseEvent && swiper.params.zoom.panOnMouseMove;\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) {\n      return;\n    }\n    const zoom = swiper.zoom;\n    if (!gesture.imageEl) {\n      return;\n    }\n    if (!image.isTouched || !gesture.slideEl) {\n      if (isMousePan) onMouseMove(e);\n      return;\n    }\n    if (isMousePan) {\n      onMouseMove(e);\n      return;\n    }\n    if (!image.isMoved) {\n      image.width = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      image.height = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      image.startX = getTranslate(gesture.imageWrapEl, 'x') || 0;\n      image.startY = getTranslate(gesture.imageWrapEl, 'y') || 0;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      gesture.imageWrapEl.style.transitionDuration = '0ms';\n    }\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.touchesCurrent.x = evCache.length > 0 ? evCache[0].pageX : e.pageX;\n    image.touchesCurrent.y = evCache.length > 0 ? evCache[0].pageY : e.pageY;\n    const touchesDiff = Math.max(Math.abs(image.touchesCurrent.x - image.touchesStart.x), Math.abs(image.touchesCurrent.y - image.touchesStart.y));\n    if (touchesDiff > 5) {\n      swiper.allowClick = false;\n    }\n    if (!image.isMoved && !isScaling) {\n      if (swiper.isHorizontal() && (Math.floor(image.minX) === Math.floor(image.startX) && image.touchesCurrent.x < image.touchesStart.x || Math.floor(image.maxX) === Math.floor(image.startX) && image.touchesCurrent.x > image.touchesStart.x)) {\n        image.isTouched = false;\n        allowTouchMove();\n        return;\n      }\n      if (!swiper.isHorizontal() && (Math.floor(image.minY) === Math.floor(image.startY) && image.touchesCurrent.y < image.touchesStart.y || Math.floor(image.maxY) === Math.floor(image.startY) && image.touchesCurrent.y > image.touchesStart.y)) {\n        image.isTouched = false;\n        allowTouchMove();\n        return;\n      }\n    }\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n    e.stopPropagation();\n    preventTouchMove();\n    image.isMoved = true;\n    const scaleRatio = (zoom.scale - currentScale) / (gesture.maxRatio - swiper.params.zoom.minRatio);\n    const {\n      originX,\n      originY\n    } = gesture;\n    image.currentX = image.touchesCurrent.x - image.touchesStart.x + image.startX + scaleRatio * (image.width - originX * 2);\n    image.currentY = image.touchesCurrent.y - image.touchesStart.y + image.startY + scaleRatio * (image.height - originY * 2);\n    if (image.currentX < image.minX) {\n      image.currentX = image.minX + 1 - (image.minX - image.currentX + 1) ** 0.8;\n    }\n    if (image.currentX > image.maxX) {\n      image.currentX = image.maxX - 1 + (image.currentX - image.maxX + 1) ** 0.8;\n    }\n    if (image.currentY < image.minY) {\n      image.currentY = image.minY + 1 - (image.minY - image.currentY + 1) ** 0.8;\n    }\n    if (image.currentY > image.maxY) {\n      image.currentY = image.maxY - 1 + (image.currentY - image.maxY + 1) ** 0.8;\n    }\n\n    // Velocity\n    if (!velocity.prevPositionX) velocity.prevPositionX = image.touchesCurrent.x;\n    if (!velocity.prevPositionY) velocity.prevPositionY = image.touchesCurrent.y;\n    if (!velocity.prevTime) velocity.prevTime = Date.now();\n    velocity.x = (image.touchesCurrent.x - velocity.prevPositionX) / (Date.now() - velocity.prevTime) / 2;\n    velocity.y = (image.touchesCurrent.y - velocity.prevPositionY) / (Date.now() - velocity.prevTime) / 2;\n    if (Math.abs(image.touchesCurrent.x - velocity.prevPositionX) < 2) velocity.x = 0;\n    if (Math.abs(image.touchesCurrent.y - velocity.prevPositionY) < 2) velocity.y = 0;\n    velocity.prevPositionX = image.touchesCurrent.x;\n    velocity.prevPositionY = image.touchesCurrent.y;\n    velocity.prevTime = Date.now();\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTouchEnd() {\n    const zoom = swiper.zoom;\n    evCache.length = 0;\n    if (!gesture.imageEl) return;\n    if (!image.isTouched || !image.isMoved) {\n      image.isTouched = false;\n      image.isMoved = false;\n      return;\n    }\n    image.isTouched = false;\n    image.isMoved = false;\n    let momentumDurationX = 300;\n    let momentumDurationY = 300;\n    const momentumDistanceX = velocity.x * momentumDurationX;\n    const newPositionX = image.currentX + momentumDistanceX;\n    const momentumDistanceY = velocity.y * momentumDurationY;\n    const newPositionY = image.currentY + momentumDistanceY;\n\n    // Fix duration\n    if (velocity.x !== 0) momentumDurationX = Math.abs((newPositionX - image.currentX) / velocity.x);\n    if (velocity.y !== 0) momentumDurationY = Math.abs((newPositionY - image.currentY) / velocity.y);\n    const momentumDuration = Math.max(momentumDurationX, momentumDurationY);\n    image.currentX = newPositionX;\n    image.currentY = newPositionY;\n    // Define if we need image drag\n    const scaledWidth = image.width * zoom.scale;\n    const scaledHeight = image.height * zoom.scale;\n    image.minX = Math.min(gesture.slideWidth / 2 - scaledWidth / 2, 0);\n    image.maxX = -image.minX;\n    image.minY = Math.min(gesture.slideHeight / 2 - scaledHeight / 2, 0);\n    image.maxY = -image.minY;\n    image.currentX = Math.max(Math.min(image.currentX, image.maxX), image.minX);\n    image.currentY = Math.max(Math.min(image.currentY, image.maxY), image.minY);\n    gesture.imageWrapEl.style.transitionDuration = `${momentumDuration}ms`;\n    gesture.imageWrapEl.style.transform = `translate3d(${image.currentX}px, ${image.currentY}px,0)`;\n  }\n  function onTransitionEnd() {\n    const zoom = swiper.zoom;\n    if (gesture.slideEl && swiper.activeIndex !== swiper.slides.indexOf(gesture.slideEl)) {\n      if (gesture.imageEl) {\n        gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n      }\n      if (gesture.imageWrapEl) {\n        gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n      }\n      gesture.slideEl.classList.remove(`${swiper.params.zoom.zoomedSlideClass}`);\n      zoom.scale = 1;\n      currentScale = 1;\n      gesture.slideEl = undefined;\n      gesture.imageEl = undefined;\n      gesture.imageWrapEl = undefined;\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n  }\n  function onMouseMove(e) {\n    // Only pan if zoomed in and mouse panning is enabled\n    if (currentScale <= 1 || !gesture.imageWrapEl) return;\n    if (!eventWithinSlide(e) || !eventWithinZoomContainer(e)) return;\n    const currentTransform = window.getComputedStyle(gesture.imageWrapEl).transform;\n    const matrix = new window.DOMMatrix(currentTransform);\n    if (!isPanningWithMouse) {\n      isPanningWithMouse = true;\n      mousePanStart.x = e.clientX;\n      mousePanStart.y = e.clientY;\n      image.startX = matrix.e;\n      image.startY = matrix.f;\n      image.width = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      image.height = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      gesture.slideWidth = gesture.slideEl.offsetWidth;\n      gesture.slideHeight = gesture.slideEl.offsetHeight;\n      return;\n    }\n    const deltaX = (e.clientX - mousePanStart.x) * mousePanSensitivity;\n    const deltaY = (e.clientY - mousePanStart.y) * mousePanSensitivity;\n    const scaledWidth = image.width * currentScale;\n    const scaledHeight = image.height * currentScale;\n    const slideWidth = gesture.slideWidth;\n    const slideHeight = gesture.slideHeight;\n    const minX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n    const maxX = -minX;\n    const minY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n    const maxY = -minY;\n    const newX = Math.max(Math.min(image.startX + deltaX, maxX), minX);\n    const newY = Math.max(Math.min(image.startY + deltaY, maxY), minY);\n    gesture.imageWrapEl.style.transitionDuration = '0ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${newX}px, ${newY}px, 0)`;\n    mousePanStart.x = e.clientX;\n    mousePanStart.y = e.clientY;\n    image.startX = newX;\n    image.startY = newY;\n    image.currentX = newX;\n    image.currentY = newY;\n  }\n  function zoomIn(e) {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (e && e.target) {\n        gesture.slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n      }\n      if (!gesture.slideEl) {\n        if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n          gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n        } else {\n          gesture.slideEl = swiper.slides[swiper.activeIndex];\n        }\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.touchAction = 'none';\n    }\n    gesture.slideEl.classList.add(`${params.zoomedSlideClass}`);\n    let touchX;\n    let touchY;\n    let offsetX;\n    let offsetY;\n    let diffX;\n    let diffY;\n    let translateX;\n    let translateY;\n    let imageWidth;\n    let imageHeight;\n    let scaledWidth;\n    let scaledHeight;\n    let translateMinX;\n    let translateMinY;\n    let translateMaxX;\n    let translateMaxY;\n    let slideWidth;\n    let slideHeight;\n    if (typeof image.touchesStart.x === 'undefined' && e) {\n      touchX = e.pageX;\n      touchY = e.pageY;\n    } else {\n      touchX = image.touchesStart.x;\n      touchY = image.touchesStart.y;\n    }\n    const prevScale = currentScale;\n    const forceZoomRatio = typeof e === 'number' ? e : null;\n    if (currentScale === 1 && forceZoomRatio) {\n      touchX = undefined;\n      touchY = undefined;\n      image.touchesStart.x = undefined;\n      image.touchesStart.y = undefined;\n    }\n    const maxRatio = getMaxRatio();\n    zoom.scale = forceZoomRatio || maxRatio;\n    currentScale = forceZoomRatio || maxRatio;\n    if (e && !(currentScale === 1 && forceZoomRatio)) {\n      slideWidth = gesture.slideEl.offsetWidth;\n      slideHeight = gesture.slideEl.offsetHeight;\n      offsetX = elementOffset(gesture.slideEl).left + window.scrollX;\n      offsetY = elementOffset(gesture.slideEl).top + window.scrollY;\n      diffX = offsetX + slideWidth / 2 - touchX;\n      diffY = offsetY + slideHeight / 2 - touchY;\n      imageWidth = gesture.imageEl.offsetWidth || gesture.imageEl.clientWidth;\n      imageHeight = gesture.imageEl.offsetHeight || gesture.imageEl.clientHeight;\n      scaledWidth = imageWidth * zoom.scale;\n      scaledHeight = imageHeight * zoom.scale;\n      translateMinX = Math.min(slideWidth / 2 - scaledWidth / 2, 0);\n      translateMinY = Math.min(slideHeight / 2 - scaledHeight / 2, 0);\n      translateMaxX = -translateMinX;\n      translateMaxY = -translateMinY;\n      if (prevScale > 0 && forceZoomRatio && typeof image.currentX === 'number' && typeof image.currentY === 'number') {\n        translateX = image.currentX * zoom.scale / prevScale;\n        translateY = image.currentY * zoom.scale / prevScale;\n      } else {\n        translateX = diffX * zoom.scale;\n        translateY = diffY * zoom.scale;\n      }\n      if (translateX < translateMinX) {\n        translateX = translateMinX;\n      }\n      if (translateX > translateMaxX) {\n        translateX = translateMaxX;\n      }\n      if (translateY < translateMinY) {\n        translateY = translateMinY;\n      }\n      if (translateY > translateMaxY) {\n        translateY = translateMaxY;\n      }\n    } else {\n      translateX = 0;\n      translateY = 0;\n    }\n    if (forceZoomRatio && zoom.scale === 1) {\n      gesture.originX = 0;\n      gesture.originY = 0;\n    }\n    image.currentX = translateX;\n    image.currentY = translateY;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = `translate3d(${translateX}px, ${translateY}px,0)`;\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = `translate3d(0,0,0) scale(${zoom.scale})`;\n  }\n  function zoomOut() {\n    const zoom = swiper.zoom;\n    const params = swiper.params.zoom;\n    if (!gesture.slideEl) {\n      if (swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual) {\n        gesture.slideEl = elementChildren(swiper.slidesEl, `.${swiper.params.slideActiveClass}`)[0];\n      } else {\n        gesture.slideEl = swiper.slides[swiper.activeIndex];\n      }\n      let imageEl = gesture.slideEl.querySelector(`.${params.containerClass}`);\n      if (imageEl) {\n        imageEl = imageEl.querySelectorAll('picture, img, svg, canvas, .swiper-zoom-target')[0];\n      }\n      gesture.imageEl = imageEl;\n      if (imageEl) {\n        gesture.imageWrapEl = elementParents(gesture.imageEl, `.${params.containerClass}`)[0];\n      } else {\n        gesture.imageWrapEl = undefined;\n      }\n    }\n    if (!gesture.imageEl || !gesture.imageWrapEl) return;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style.overflow = '';\n      swiper.wrapperEl.style.touchAction = '';\n    }\n    zoom.scale = 1;\n    currentScale = 1;\n    image.currentX = undefined;\n    image.currentY = undefined;\n    image.touchesStart.x = undefined;\n    image.touchesStart.y = undefined;\n    gesture.imageWrapEl.style.transitionDuration = '300ms';\n    gesture.imageWrapEl.style.transform = 'translate3d(0,0,0)';\n    gesture.imageEl.style.transitionDuration = '300ms';\n    gesture.imageEl.style.transform = 'translate3d(0,0,0) scale(1)';\n    gesture.slideEl.classList.remove(`${params.zoomedSlideClass}`);\n    gesture.slideEl = undefined;\n    gesture.originX = 0;\n    gesture.originY = 0;\n    if (swiper.params.zoom.panOnMouseMove) {\n      mousePanStart = {\n        x: 0,\n        y: 0\n      };\n      if (isPanningWithMouse) {\n        isPanningWithMouse = false;\n        image.startX = 0;\n        image.startY = 0;\n      }\n    }\n  }\n\n  // Toggle Zoom\n  function zoomToggle(e) {\n    const zoom = swiper.zoom;\n    if (zoom.scale && zoom.scale !== 1) {\n      // Zoom Out\n      zoomOut();\n    } else {\n      // Zoom In\n      zoomIn(e);\n    }\n  }\n  function getListeners() {\n    const passiveListener = swiper.params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    const activeListenerWithCapture = swiper.params.passiveListeners ? {\n      passive: false,\n      capture: true\n    } : true;\n    return {\n      passiveListener,\n      activeListenerWithCapture\n    };\n  }\n\n  // Attach/Detach Events\n  function enable() {\n    const zoom = swiper.zoom;\n    if (zoom.enabled) return;\n    zoom.enabled = true;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.addEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.addEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.addEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.addEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  function disable() {\n    const zoom = swiper.zoom;\n    if (!zoom.enabled) return;\n    zoom.enabled = false;\n    const {\n      passiveListener,\n      activeListenerWithCapture\n    } = getListeners();\n\n    // Scale image\n    swiper.wrapperEl.removeEventListener('pointerdown', onGestureStart, passiveListener);\n    swiper.wrapperEl.removeEventListener('pointermove', onGestureChange, activeListenerWithCapture);\n    ['pointerup', 'pointercancel', 'pointerout'].forEach(eventName => {\n      swiper.wrapperEl.removeEventListener(eventName, onGestureEnd, passiveListener);\n    });\n\n    // Move image\n    swiper.wrapperEl.removeEventListener('pointermove', onTouchMove, activeListenerWithCapture);\n  }\n  on('init', () => {\n    if (swiper.params.zoom.enabled) {\n      enable();\n    }\n  });\n  on('destroy', () => {\n    disable();\n  });\n  on('touchStart', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchStart(e);\n  });\n  on('touchEnd', (_s, e) => {\n    if (!swiper.zoom.enabled) return;\n    onTouchEnd();\n  });\n  on('doubleTap', (_s, e) => {\n    if (!swiper.animating && swiper.params.zoom.enabled && swiper.zoom.enabled && swiper.params.zoom.toggle) {\n      zoomToggle(e);\n    }\n  });\n  on('transitionEnd', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled) {\n      onTransitionEnd();\n    }\n  });\n  on('slideChange', () => {\n    if (swiper.zoom.enabled && swiper.params.zoom.enabled && swiper.params.cssMode) {\n      onTransitionEnd();\n    }\n  });\n  Object.assign(swiper.zoom, {\n    enable,\n    disable,\n    in: zoomIn,\n    out: zoomOut,\n    toggle: zoomToggle\n  });\n}\n\nexport { Zoom as default };\n"], "mappings": ";;;AAAA,SAASA,CAAC,IAAIC,SAAS,QAAQ,8BAA8B;AAC7D,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,YAAY,QAAQ,qBAAqB;AAEtH,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,MAAM,GAAGf,SAAS,CAAC,CAAC;EAC1BY,YAAY,CAAC;IACXI,IAAI,EAAE;MACJC,OAAO,EAAE,KAAK;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,cAAc,EAAE,KAAK;MACrBC,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE,uBAAuB;MACvCC,gBAAgB,EAAE;IACpB;EACF,CAAC,CAAC;EACFb,MAAM,CAACK,IAAI,GAAG;IACZC,OAAO,EAAE;EACX,CAAC;EACD,IAAIQ,YAAY,GAAG,CAAC;EACpB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,IAAIC,aAAa,GAAG;IAClBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;EACD,MAAMC,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC,IAAIC,kBAAkB;EACtB,IAAIC,gBAAgB;EACpB,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAMC,OAAO,GAAG;IACdC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAEC,SAAS;IAClBC,UAAU,EAAED,SAAS;IACrBE,WAAW,EAAEF,SAAS;IACtBG,OAAO,EAAEH,SAAS;IAClBI,WAAW,EAAEJ,SAAS;IACtBpB,QAAQ,EAAE;EACZ,CAAC;EACD,MAAMyB,KAAK,GAAG;IACZC,SAAS,EAAEN,SAAS;IACpBO,OAAO,EAAEP,SAAS;IAClBQ,QAAQ,EAAER,SAAS;IACnBS,QAAQ,EAAET,SAAS;IACnBU,IAAI,EAAEV,SAAS;IACfW,IAAI,EAAEX,SAAS;IACfY,IAAI,EAAEZ,SAAS;IACfa,IAAI,EAAEb,SAAS;IACfc,KAAK,EAAEd,SAAS;IAChBe,MAAM,EAAEf,SAAS;IACjBgB,MAAM,EAAEhB,SAAS;IACjBiB,MAAM,EAAEjB,SAAS;IACjBkB,YAAY,EAAE,CAAC,CAAC;IAChBC,cAAc,EAAE,CAAC;EACnB,CAAC;EACD,MAAMC,QAAQ,GAAG;IACf9B,CAAC,EAAEU,SAAS;IACZT,CAAC,EAAES,SAAS;IACZqB,aAAa,EAAErB,SAAS;IACxBsB,aAAa,EAAEtB,SAAS;IACxBuB,QAAQ,EAAEvB;EACZ,CAAC;EACD,IAAIwB,KAAK,GAAG,CAAC;EACbC,MAAM,CAACC,cAAc,CAACtD,MAAM,CAACK,IAAI,EAAE,OAAO,EAAE;IAC1CkD,GAAGA,CAAA,EAAG;MACJ,OAAOH,KAAK;IACd,CAAC;IACDI,GAAGA,CAACC,KAAK,EAAE;MACT,IAAIL,KAAK,KAAKK,KAAK,EAAE;QACnB,MAAM1B,OAAO,GAAGP,OAAO,CAACO,OAAO;QAC/B,MAAMJ,OAAO,GAAGH,OAAO,CAACG,OAAO;QAC/BxB,IAAI,CAAC,YAAY,EAAEsD,KAAK,EAAE1B,OAAO,EAAEJ,OAAO,CAAC;MAC7C;MACAyB,KAAK,GAAGK,KAAK;IACf;EACF,CAAC,CAAC;EACF,SAASC,yBAAyBA,CAAA,EAAG;IACnC,IAAInC,OAAO,CAACoC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;IAChC,MAAMC,EAAE,GAAGrC,OAAO,CAAC,CAAC,CAAC,CAACsC,KAAK;IAC3B,MAAMC,EAAE,GAAGvC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK;IAC3B,MAAMC,EAAE,GAAGzC,OAAO,CAAC,CAAC,CAAC,CAACsC,KAAK;IAC3B,MAAMI,EAAE,GAAG1C,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK;IAC3B,MAAMG,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,EAAE,GAAGJ,EAAE,KAAK,CAAC,GAAG,CAACK,EAAE,GAAGH,EAAE,KAAK,CAAC,CAAC;IAC3D,OAAOI,QAAQ;EACjB;EACA,SAASG,WAAWA,CAAA,EAAG;IACrB,MAAMC,MAAM,GAAGtE,MAAM,CAACsE,MAAM,CAACjE,IAAI;IACjC,MAAMG,QAAQ,GAAGgB,OAAO,CAACQ,WAAW,CAACuC,YAAY,CAAC,kBAAkB,CAAC,IAAID,MAAM,CAAC9D,QAAQ;IACxF,IAAI8D,MAAM,CAAC/D,mBAAmB,IAAIiB,OAAO,CAACO,OAAO,IAAIP,OAAO,CAACO,OAAO,CAACyC,YAAY,EAAE;MACjF,MAAMC,aAAa,GAAGjD,OAAO,CAACO,OAAO,CAACyC,YAAY,GAAGhD,OAAO,CAACO,OAAO,CAAC2C,WAAW;MAChF,OAAOP,IAAI,CAACQ,GAAG,CAACF,aAAa,EAAEjE,QAAQ,CAAC;IAC1C;IACA,OAAOA,QAAQ;EACjB;EACA,SAASoE,cAAcA,CAAA,EAAG;IACxB,IAAIrD,OAAO,CAACoC,MAAM,GAAG,CAAC,EAAE,OAAO;MAC7BzC,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL,CAAC;IACD,MAAM0D,GAAG,GAAGrD,OAAO,CAACO,OAAO,CAAC+C,qBAAqB,CAAC,CAAC;IACnD,OAAO,CAAC,CAACvD,OAAO,CAAC,CAAC,CAAC,CAACsC,KAAK,GAAG,CAACtC,OAAO,CAAC,CAAC,CAAC,CAACsC,KAAK,GAAGtC,OAAO,CAAC,CAAC,CAAC,CAACsC,KAAK,IAAI,CAAC,GAAGgB,GAAG,CAAC3D,CAAC,GAAGd,MAAM,CAAC2E,OAAO,IAAIjE,YAAY,EAAE,CAACS,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,GAAG,CAACxC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,GAAGxC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,IAAI,CAAC,GAAGc,GAAG,CAAC1D,CAAC,GAAGf,MAAM,CAAC4E,OAAO,IAAIlE,YAAY,CAAC;EACzN;EACA,SAASmE,gBAAgBA,CAAA,EAAG;IAC1B,OAAOjF,MAAM,CAACkF,SAAS,GAAG,cAAc,GAAG,IAAIlF,MAAM,CAACsE,MAAM,CAACa,UAAU,EAAE;EAC3E;EACA,SAASC,gBAAgBA,CAAC9F,CAAC,EAAE;IAC3B,MAAM+F,aAAa,GAAGJ,gBAAgB,CAAC,CAAC;IACxC,IAAI3F,CAAC,CAACgG,MAAM,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE,OAAO,IAAI;IAChD,IAAIrF,MAAM,CAACwF,MAAM,CAACC,MAAM,CAAC9D,OAAO,IAAIA,OAAO,CAAC+D,QAAQ,CAACpG,CAAC,CAACgG,MAAM,CAAC,CAAC,CAAC3B,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IACvF,OAAO,KAAK;EACd;EACA,SAASgC,wBAAwBA,CAACrG,CAAC,EAAE;IACnC,MAAMsG,QAAQ,GAAG,IAAI5F,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACO,cAAc,EAAE;IACxD,IAAItB,CAAC,CAACgG,MAAM,CAACC,OAAO,CAACK,QAAQ,CAAC,EAAE,OAAO,IAAI;IAC3C,IAAI,CAAC,GAAG5F,MAAM,CAAC6F,MAAM,CAACC,gBAAgB,CAACF,QAAQ,CAAC,CAAC,CAACH,MAAM,CAACM,WAAW,IAAIA,WAAW,CAACL,QAAQ,CAACpG,CAAC,CAACgG,MAAM,CAAC,CAAC,CAAC3B,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;IAC/H,OAAO,KAAK;EACd;;EAEA;EACA,SAASqC,cAAcA,CAAC1G,CAAC,EAAE;IACzB,IAAIA,CAAC,CAAC2G,WAAW,KAAK,OAAO,EAAE;MAC7B1E,OAAO,CAAC2E,MAAM,CAAC,CAAC,EAAE3E,OAAO,CAACoC,MAAM,CAAC;IACnC;IACA,IAAI,CAACyB,gBAAgB,CAAC9F,CAAC,CAAC,EAAE;IAC1B,MAAMgF,MAAM,GAAGtE,MAAM,CAACsE,MAAM,CAACjE,IAAI;IACjCgB,kBAAkB,GAAG,KAAK;IAC1BC,gBAAgB,GAAG,KAAK;IACxBC,OAAO,CAAC4E,IAAI,CAAC7G,CAAC,CAAC;IACf,IAAIiC,OAAO,CAACoC,MAAM,GAAG,CAAC,EAAE;MACtB;IACF;IACAtC,kBAAkB,GAAG,IAAI;IACzBG,OAAO,CAAC4E,UAAU,GAAG1C,yBAAyB,CAAC,CAAC;IAChD,IAAI,CAAClC,OAAO,CAACG,OAAO,EAAE;MACpBH,OAAO,CAACG,OAAO,GAAGrC,CAAC,CAACgG,MAAM,CAACe,OAAO,CAAC,IAAIrG,MAAM,CAACsE,MAAM,CAACa,UAAU,gBAAgB,CAAC;MAChF,IAAI,CAAC3D,OAAO,CAACG,OAAO,EAAEH,OAAO,CAACG,OAAO,GAAG3B,MAAM,CAACwF,MAAM,CAACxF,MAAM,CAACsG,WAAW,CAAC;MACzE,IAAIvE,OAAO,GAAGP,OAAO,CAACG,OAAO,CAAC4E,aAAa,CAAC,IAAIjC,MAAM,CAAC1D,cAAc,EAAE,CAAC;MACxE,IAAImB,OAAO,EAAE;QACXA,OAAO,GAAGA,OAAO,CAAC+D,gBAAgB,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC;MACzF;MACAtE,OAAO,CAACO,OAAO,GAAGA,OAAO;MACzB,IAAIA,OAAO,EAAE;QACXP,OAAO,CAACQ,WAAW,GAAGvC,cAAc,CAAC+B,OAAO,CAACO,OAAO,EAAE,IAAIuC,MAAM,CAAC1D,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,MAAM;QACLY,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MACjC;MACA,IAAI,CAACJ,OAAO,CAACQ,WAAW,EAAE;QACxBR,OAAO,CAACO,OAAO,GAAGH,SAAS;QAC3B;MACF;MACAJ,OAAO,CAAChB,QAAQ,GAAG6D,WAAW,CAAC,CAAC;IAClC;IACA,IAAI7C,OAAO,CAACO,OAAO,EAAE;MACnB,MAAM,CAACN,OAAO,EAAEC,OAAO,CAAC,GAAGkD,cAAc,CAAC,CAAC;MAC3CpD,OAAO,CAACC,OAAO,GAAGA,OAAO;MACzBD,OAAO,CAACE,OAAO,GAAGA,OAAO;MACzBF,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACC,kBAAkB,GAAG,KAAK;IAClD;IACA1F,SAAS,GAAG,IAAI;EAClB;EACA,SAAS2F,eAAeA,CAACpH,CAAC,EAAE;IAC1B,IAAI,CAAC8F,gBAAgB,CAAC9F,CAAC,CAAC,EAAE;IAC1B,MAAMgF,MAAM,GAAGtE,MAAM,CAACsE,MAAM,CAACjE,IAAI;IACjC,MAAMA,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAMsG,YAAY,GAAGpF,OAAO,CAACqF,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,KAAKxH,CAAC,CAACwH,SAAS,CAAC;IACtF,IAAIH,YAAY,IAAI,CAAC,EAAEpF,OAAO,CAACoF,YAAY,CAAC,GAAGrH,CAAC;IAChD,IAAIiC,OAAO,CAACoC,MAAM,GAAG,CAAC,EAAE;MACtB;IACF;IACArC,gBAAgB,GAAG,IAAI;IACvBE,OAAO,CAACuF,SAAS,GAAGrD,yBAAyB,CAAC,CAAC;IAC/C,IAAI,CAAClC,OAAO,CAACO,OAAO,EAAE;MACpB;IACF;IACA1B,IAAI,CAAC+C,KAAK,GAAG5B,OAAO,CAACuF,SAAS,GAAGvF,OAAO,CAAC4E,UAAU,GAAGtF,YAAY;IAClE,IAAIT,IAAI,CAAC+C,KAAK,GAAG5B,OAAO,CAAChB,QAAQ,EAAE;MACjCH,IAAI,CAAC+C,KAAK,GAAG5B,OAAO,CAAChB,QAAQ,GAAG,CAAC,GAAG,CAACH,IAAI,CAAC+C,KAAK,GAAG5B,OAAO,CAAChB,QAAQ,GAAG,CAAC,KAAK,GAAG;IAChF;IACA,IAAIH,IAAI,CAAC+C,KAAK,GAAGkB,MAAM,CAAC7D,QAAQ,EAAE;MAChCJ,IAAI,CAAC+C,KAAK,GAAGkB,MAAM,CAAC7D,QAAQ,GAAG,CAAC,GAAG,CAAC6D,MAAM,CAAC7D,QAAQ,GAAGJ,IAAI,CAAC+C,KAAK,GAAG,CAAC,KAAK,GAAG;IAC9E;IACA5B,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACQ,SAAS,GAAG,4BAA4B3G,IAAI,CAAC+C,KAAK,GAAG;EAC7E;EACA,SAAS6D,YAAYA,CAAC3H,CAAC,EAAE;IACvB,IAAI,CAAC8F,gBAAgB,CAAC9F,CAAC,CAAC,EAAE;IAC1B,IAAIA,CAAC,CAAC2G,WAAW,KAAK,OAAO,IAAI3G,CAAC,CAAC4H,IAAI,KAAK,YAAY,EAAE;IAC1D,MAAM5C,MAAM,GAAGtE,MAAM,CAACsE,MAAM,CAACjE,IAAI;IACjC,MAAMA,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAMsG,YAAY,GAAGpF,OAAO,CAACqF,SAAS,CAACC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,KAAKxH,CAAC,CAACwH,SAAS,CAAC;IACtF,IAAIH,YAAY,IAAI,CAAC,EAAEpF,OAAO,CAAC2E,MAAM,CAACS,YAAY,EAAE,CAAC,CAAC;IACtD,IAAI,CAACtF,kBAAkB,IAAI,CAACC,gBAAgB,EAAE;MAC5C;IACF;IACAD,kBAAkB,GAAG,KAAK;IAC1BC,gBAAgB,GAAG,KAAK;IACxB,IAAI,CAACE,OAAO,CAACO,OAAO,EAAE;IACtB1B,IAAI,CAAC+C,KAAK,GAAGe,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACQ,GAAG,CAACtE,IAAI,CAAC+C,KAAK,EAAE5B,OAAO,CAAChB,QAAQ,CAAC,EAAE8D,MAAM,CAAC7D,QAAQ,CAAC;IAC9Ee,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACC,kBAAkB,GAAG,GAAGzG,MAAM,CAACsE,MAAM,CAAC8C,KAAK,IAAI;IACrE5F,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACQ,SAAS,GAAG,4BAA4B3G,IAAI,CAAC+C,KAAK,GAAG;IAC3EtC,YAAY,GAAGT,IAAI,CAAC+C,KAAK;IACzBrC,SAAS,GAAG,KAAK;IACjB,IAAIV,IAAI,CAAC+C,KAAK,GAAG,CAAC,IAAI5B,OAAO,CAACG,OAAO,EAAE;MACrCH,OAAO,CAACG,OAAO,CAAC0F,SAAS,CAACC,GAAG,CAAC,GAAGhD,MAAM,CAACzD,gBAAgB,EAAE,CAAC;IAC7D,CAAC,MAAM,IAAIR,IAAI,CAAC+C,KAAK,IAAI,CAAC,IAAI5B,OAAO,CAACG,OAAO,EAAE;MAC7CH,OAAO,CAACG,OAAO,CAAC0F,SAAS,CAACE,MAAM,CAAC,GAAGjD,MAAM,CAACzD,gBAAgB,EAAE,CAAC;IAChE;IACA,IAAIR,IAAI,CAAC+C,KAAK,KAAK,CAAC,EAAE;MACpB5B,OAAO,CAACC,OAAO,GAAG,CAAC;MACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;MACnBF,OAAO,CAACG,OAAO,GAAGC,SAAS;IAC7B;EACF;EACA,IAAI4F,qBAAqB;EACzB,SAASC,cAAcA,CAAA,EAAG;IACxBzH,MAAM,CAAC0H,eAAe,CAACC,+BAA+B,GAAG,KAAK;EAChE;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1BC,YAAY,CAACL,qBAAqB,CAAC;IACnCxH,MAAM,CAAC0H,eAAe,CAACC,+BAA+B,GAAG,IAAI;IAC7DH,qBAAqB,GAAGM,UAAU,CAAC,MAAM;MACvC,IAAI9H,MAAM,CAAC+H,SAAS,EAAE;MACtBN,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;EACJ;EACA,SAASO,YAAYA,CAAC1I,CAAC,EAAE;IACvB,MAAM2I,MAAM,GAAGjI,MAAM,CAACiI,MAAM;IAC5B,IAAI,CAACzG,OAAO,CAACO,OAAO,EAAE;IACtB,IAAIE,KAAK,CAACC,SAAS,EAAE;IACrB,IAAI+F,MAAM,CAACC,OAAO,IAAI5I,CAAC,CAAC6I,UAAU,EAAE7I,CAAC,CAAC8I,cAAc,CAAC,CAAC;IACtDnG,KAAK,CAACC,SAAS,GAAG,IAAI;IACtB,MAAMmG,KAAK,GAAG9G,OAAO,CAACoC,MAAM,GAAG,CAAC,GAAGpC,OAAO,CAAC,CAAC,CAAC,GAAGjC,CAAC;IACjD2C,KAAK,CAACa,YAAY,CAAC5B,CAAC,GAAGmH,KAAK,CAACxE,KAAK;IAClC5B,KAAK,CAACa,YAAY,CAAC3B,CAAC,GAAGkH,KAAK,CAACtE,KAAK;EACpC;EACA,SAASuE,WAAWA,CAAChJ,CAAC,EAAE;IACtB,MAAMiJ,YAAY,GAAGjJ,CAAC,CAAC2G,WAAW,KAAK,OAAO;IAC9C,MAAMuC,UAAU,GAAGD,YAAY,IAAIvI,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACK,cAAc;IACpE,IAAI,CAAC0E,gBAAgB,CAAC9F,CAAC,CAAC,IAAI,CAACqG,wBAAwB,CAACrG,CAAC,CAAC,EAAE;MACxD;IACF;IACA,MAAMe,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAI,CAACmB,OAAO,CAACO,OAAO,EAAE;MACpB;IACF;IACA,IAAI,CAACE,KAAK,CAACC,SAAS,IAAI,CAACV,OAAO,CAACG,OAAO,EAAE;MACxC,IAAI6G,UAAU,EAAEC,WAAW,CAACnJ,CAAC,CAAC;MAC9B;IACF;IACA,IAAIkJ,UAAU,EAAE;MACdC,WAAW,CAACnJ,CAAC,CAAC;MACd;IACF;IACA,IAAI,CAAC2C,KAAK,CAACE,OAAO,EAAE;MAClBF,KAAK,CAACS,KAAK,GAAGlB,OAAO,CAACO,OAAO,CAAC2C,WAAW,IAAIlD,OAAO,CAACO,OAAO,CAAC2G,WAAW;MACxEzG,KAAK,CAACU,MAAM,GAAGnB,OAAO,CAACO,OAAO,CAAC4G,YAAY,IAAInH,OAAO,CAACO,OAAO,CAAC6G,YAAY;MAC3E3G,KAAK,CAACW,MAAM,GAAG/C,YAAY,CAAC2B,OAAO,CAACQ,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC;MAC1DC,KAAK,CAACY,MAAM,GAAGhD,YAAY,CAAC2B,OAAO,CAACQ,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC;MAC1DR,OAAO,CAACK,UAAU,GAAGL,OAAO,CAACG,OAAO,CAAC+C,WAAW;MAChDlD,OAAO,CAACM,WAAW,GAAGN,OAAO,CAACG,OAAO,CAACgH,YAAY;MAClDnH,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACC,kBAAkB,GAAG,KAAK;IACtD;IACA;IACA,MAAMoC,WAAW,GAAG5G,KAAK,CAACS,KAAK,GAAGrC,IAAI,CAAC+C,KAAK;IAC5C,MAAM0F,YAAY,GAAG7G,KAAK,CAACU,MAAM,GAAGtC,IAAI,CAAC+C,KAAK;IAC9CnB,KAAK,CAACK,IAAI,GAAG6B,IAAI,CAACQ,GAAG,CAACnD,OAAO,CAACK,UAAU,GAAG,CAAC,GAAGgH,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;IAClE5G,KAAK,CAACO,IAAI,GAAG,CAACP,KAAK,CAACK,IAAI;IACxBL,KAAK,CAACM,IAAI,GAAG4B,IAAI,CAACQ,GAAG,CAACnD,OAAO,CAACM,WAAW,GAAG,CAAC,GAAGgH,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;IACpE7G,KAAK,CAACQ,IAAI,GAAG,CAACR,KAAK,CAACM,IAAI;IACxBN,KAAK,CAACc,cAAc,CAAC7B,CAAC,GAAGK,OAAO,CAACoC,MAAM,GAAG,CAAC,GAAGpC,OAAO,CAAC,CAAC,CAAC,CAACsC,KAAK,GAAGvE,CAAC,CAACuE,KAAK;IACxE5B,KAAK,CAACc,cAAc,CAAC5B,CAAC,GAAGI,OAAO,CAACoC,MAAM,GAAG,CAAC,GAAGpC,OAAO,CAAC,CAAC,CAAC,CAACwC,KAAK,GAAGzE,CAAC,CAACyE,KAAK;IACxE,MAAMgF,WAAW,GAAG5E,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAAC6E,GAAG,CAAC/G,KAAK,CAACc,cAAc,CAAC7B,CAAC,GAAGe,KAAK,CAACa,YAAY,CAAC5B,CAAC,CAAC,EAAEiD,IAAI,CAAC6E,GAAG,CAAC/G,KAAK,CAACc,cAAc,CAAC5B,CAAC,GAAGc,KAAK,CAACa,YAAY,CAAC3B,CAAC,CAAC,CAAC;IAC9I,IAAI4H,WAAW,GAAG,CAAC,EAAE;MACnB/I,MAAM,CAACiJ,UAAU,GAAG,KAAK;IAC3B;IACA,IAAI,CAAChH,KAAK,CAACE,OAAO,IAAI,CAACpB,SAAS,EAAE;MAChC,IAAIf,MAAM,CAACkJ,YAAY,CAAC,CAAC,KAAK/E,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACK,IAAI,CAAC,KAAK6B,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACW,MAAM,CAAC,IAAIX,KAAK,CAACc,cAAc,CAAC7B,CAAC,GAAGe,KAAK,CAACa,YAAY,CAAC5B,CAAC,IAAIiD,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACO,IAAI,CAAC,KAAK2B,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACW,MAAM,CAAC,IAAIX,KAAK,CAACc,cAAc,CAAC7B,CAAC,GAAGe,KAAK,CAACa,YAAY,CAAC5B,CAAC,CAAC,EAAE;QAC3Oe,KAAK,CAACC,SAAS,GAAG,KAAK;QACvBuF,cAAc,CAAC,CAAC;QAChB;MACF;MACA,IAAI,CAACzH,MAAM,CAACkJ,YAAY,CAAC,CAAC,KAAK/E,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACM,IAAI,CAAC,KAAK4B,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACY,MAAM,CAAC,IAAIZ,KAAK,CAACc,cAAc,CAAC5B,CAAC,GAAGc,KAAK,CAACa,YAAY,CAAC3B,CAAC,IAAIgD,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACQ,IAAI,CAAC,KAAK0B,IAAI,CAACgF,KAAK,CAAClH,KAAK,CAACY,MAAM,CAAC,IAAIZ,KAAK,CAACc,cAAc,CAAC5B,CAAC,GAAGc,KAAK,CAACa,YAAY,CAAC3B,CAAC,CAAC,EAAE;QAC5Oc,KAAK,CAACC,SAAS,GAAG,KAAK;QACvBuF,cAAc,CAAC,CAAC;QAChB;MACF;IACF;IACA,IAAInI,CAAC,CAAC6I,UAAU,EAAE;MAChB7I,CAAC,CAAC8I,cAAc,CAAC,CAAC;IACpB;IACA9I,CAAC,CAAC8J,eAAe,CAAC,CAAC;IACnBxB,gBAAgB,CAAC,CAAC;IAClB3F,KAAK,CAACE,OAAO,GAAG,IAAI;IACpB,MAAMkH,UAAU,GAAG,CAAChJ,IAAI,CAAC+C,KAAK,GAAGtC,YAAY,KAAKU,OAAO,CAAChB,QAAQ,GAAGR,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACI,QAAQ,CAAC;IACjG,MAAM;MACJgB,OAAO;MACPC;IACF,CAAC,GAAGF,OAAO;IACXS,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACc,cAAc,CAAC7B,CAAC,GAAGe,KAAK,CAACa,YAAY,CAAC5B,CAAC,GAAGe,KAAK,CAACW,MAAM,GAAGyG,UAAU,IAAIpH,KAAK,CAACS,KAAK,GAAGjB,OAAO,GAAG,CAAC,CAAC;IACxHQ,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACc,cAAc,CAAC5B,CAAC,GAAGc,KAAK,CAACa,YAAY,CAAC3B,CAAC,GAAGc,KAAK,CAACY,MAAM,GAAGwG,UAAU,IAAIpH,KAAK,CAACU,MAAM,GAAGjB,OAAO,GAAG,CAAC,CAAC;IACzH,IAAIO,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACK,IAAI,EAAE;MAC/BL,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACK,IAAI,GAAG,CAAC,GAAG,CAACL,KAAK,CAACK,IAAI,GAAGL,KAAK,CAACG,QAAQ,GAAG,CAAC,KAAK,GAAG;IAC5E;IACA,IAAIH,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACO,IAAI,EAAE;MAC/BP,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACO,IAAI,GAAG,CAAC,GAAG,CAACP,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACO,IAAI,GAAG,CAAC,KAAK,GAAG;IAC5E;IACA,IAAIP,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACM,IAAI,EAAE;MAC/BN,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACM,IAAI,GAAG,CAAC,GAAG,CAACN,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACI,QAAQ,GAAG,CAAC,KAAK,GAAG;IAC5E;IACA,IAAIJ,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACQ,IAAI,EAAE;MAC/BR,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACQ,IAAI,GAAG,CAAC,GAAG,CAACR,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACQ,IAAI,GAAG,CAAC,KAAK,GAAG;IAC5E;;IAEA;IACA,IAAI,CAACO,QAAQ,CAACC,aAAa,EAAED,QAAQ,CAACC,aAAa,GAAGhB,KAAK,CAACc,cAAc,CAAC7B,CAAC;IAC5E,IAAI,CAAC8B,QAAQ,CAACE,aAAa,EAAEF,QAAQ,CAACE,aAAa,GAAGjB,KAAK,CAACc,cAAc,CAAC5B,CAAC;IAC5E,IAAI,CAAC6B,QAAQ,CAACG,QAAQ,EAAEH,QAAQ,CAACG,QAAQ,GAAGmG,IAAI,CAACC,GAAG,CAAC,CAAC;IACtDvG,QAAQ,CAAC9B,CAAC,GAAG,CAACe,KAAK,CAACc,cAAc,CAAC7B,CAAC,GAAG8B,QAAQ,CAACC,aAAa,KAAKqG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGvG,QAAQ,CAACG,QAAQ,CAAC,GAAG,CAAC;IACrGH,QAAQ,CAAC7B,CAAC,GAAG,CAACc,KAAK,CAACc,cAAc,CAAC5B,CAAC,GAAG6B,QAAQ,CAACE,aAAa,KAAKoG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGvG,QAAQ,CAACG,QAAQ,CAAC,GAAG,CAAC;IACrG,IAAIgB,IAAI,CAAC6E,GAAG,CAAC/G,KAAK,CAACc,cAAc,CAAC7B,CAAC,GAAG8B,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,EAAED,QAAQ,CAAC9B,CAAC,GAAG,CAAC;IACjF,IAAIiD,IAAI,CAAC6E,GAAG,CAAC/G,KAAK,CAACc,cAAc,CAAC5B,CAAC,GAAG6B,QAAQ,CAACE,aAAa,CAAC,GAAG,CAAC,EAAEF,QAAQ,CAAC7B,CAAC,GAAG,CAAC;IACjF6B,QAAQ,CAACC,aAAa,GAAGhB,KAAK,CAACc,cAAc,CAAC7B,CAAC;IAC/C8B,QAAQ,CAACE,aAAa,GAAGjB,KAAK,CAACc,cAAc,CAAC5B,CAAC;IAC/C6B,QAAQ,CAACG,QAAQ,GAAGmG,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B/H,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACQ,SAAS,GAAG,eAAe/E,KAAK,CAACG,QAAQ,OAAOH,KAAK,CAACI,QAAQ,OAAO;EACjG;EACA,SAASmH,UAAUA,CAAA,EAAG;IACpB,MAAMnJ,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxBkB,OAAO,CAACoC,MAAM,GAAG,CAAC;IAClB,IAAI,CAACnC,OAAO,CAACO,OAAO,EAAE;IACtB,IAAI,CAACE,KAAK,CAACC,SAAS,IAAI,CAACD,KAAK,CAACE,OAAO,EAAE;MACtCF,KAAK,CAACC,SAAS,GAAG,KAAK;MACvBD,KAAK,CAACE,OAAO,GAAG,KAAK;MACrB;IACF;IACAF,KAAK,CAACC,SAAS,GAAG,KAAK;IACvBD,KAAK,CAACE,OAAO,GAAG,KAAK;IACrB,IAAIsH,iBAAiB,GAAG,GAAG;IAC3B,IAAIC,iBAAiB,GAAG,GAAG;IAC3B,MAAMC,iBAAiB,GAAG3G,QAAQ,CAAC9B,CAAC,GAAGuI,iBAAiB;IACxD,MAAMG,YAAY,GAAG3H,KAAK,CAACG,QAAQ,GAAGuH,iBAAiB;IACvD,MAAME,iBAAiB,GAAG7G,QAAQ,CAAC7B,CAAC,GAAGuI,iBAAiB;IACxD,MAAMI,YAAY,GAAG7H,KAAK,CAACI,QAAQ,GAAGwH,iBAAiB;;IAEvD;IACA,IAAI7G,QAAQ,CAAC9B,CAAC,KAAK,CAAC,EAAEuI,iBAAiB,GAAGtF,IAAI,CAAC6E,GAAG,CAAC,CAACY,YAAY,GAAG3H,KAAK,CAACG,QAAQ,IAAIY,QAAQ,CAAC9B,CAAC,CAAC;IAChG,IAAI8B,QAAQ,CAAC7B,CAAC,KAAK,CAAC,EAAEuI,iBAAiB,GAAGvF,IAAI,CAAC6E,GAAG,CAAC,CAACc,YAAY,GAAG7H,KAAK,CAACI,QAAQ,IAAIW,QAAQ,CAAC7B,CAAC,CAAC;IAChG,MAAM4I,gBAAgB,GAAG5F,IAAI,CAACgD,GAAG,CAACsC,iBAAiB,EAAEC,iBAAiB,CAAC;IACvEzH,KAAK,CAACG,QAAQ,GAAGwH,YAAY;IAC7B3H,KAAK,CAACI,QAAQ,GAAGyH,YAAY;IAC7B;IACA,MAAMjB,WAAW,GAAG5G,KAAK,CAACS,KAAK,GAAGrC,IAAI,CAAC+C,KAAK;IAC5C,MAAM0F,YAAY,GAAG7G,KAAK,CAACU,MAAM,GAAGtC,IAAI,CAAC+C,KAAK;IAC9CnB,KAAK,CAACK,IAAI,GAAG6B,IAAI,CAACQ,GAAG,CAACnD,OAAO,CAACK,UAAU,GAAG,CAAC,GAAGgH,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;IAClE5G,KAAK,CAACO,IAAI,GAAG,CAACP,KAAK,CAACK,IAAI;IACxBL,KAAK,CAACM,IAAI,GAAG4B,IAAI,CAACQ,GAAG,CAACnD,OAAO,CAACM,WAAW,GAAG,CAAC,GAAGgH,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;IACpE7G,KAAK,CAACQ,IAAI,GAAG,CAACR,KAAK,CAACM,IAAI;IACxBN,KAAK,CAACG,QAAQ,GAAG+B,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACQ,GAAG,CAAC1C,KAAK,CAACG,QAAQ,EAAEH,KAAK,CAACO,IAAI,CAAC,EAAEP,KAAK,CAACK,IAAI,CAAC;IAC3EL,KAAK,CAACI,QAAQ,GAAG8B,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACQ,GAAG,CAAC1C,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAACQ,IAAI,CAAC,EAAER,KAAK,CAACM,IAAI,CAAC;IAC3Ef,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACC,kBAAkB,GAAG,GAAGsD,gBAAgB,IAAI;IACtEvI,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACQ,SAAS,GAAG,eAAe/E,KAAK,CAACG,QAAQ,OAAOH,KAAK,CAACI,QAAQ,OAAO;EACjG;EACA,SAAS2H,eAAeA,CAAA,EAAG;IACzB,MAAM3J,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAImB,OAAO,CAACG,OAAO,IAAI3B,MAAM,CAACsG,WAAW,KAAKtG,MAAM,CAACwF,MAAM,CAACyE,OAAO,CAACzI,OAAO,CAACG,OAAO,CAAC,EAAE;MACpF,IAAIH,OAAO,CAACO,OAAO,EAAE;QACnBP,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACQ,SAAS,GAAG,6BAA6B;MACjE;MACA,IAAIxF,OAAO,CAACQ,WAAW,EAAE;QACvBR,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACQ,SAAS,GAAG,oBAAoB;MAC5D;MACAxF,OAAO,CAACG,OAAO,CAAC0F,SAAS,CAACE,MAAM,CAAC,GAAGvH,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACQ,gBAAgB,EAAE,CAAC;MAC1ER,IAAI,CAAC+C,KAAK,GAAG,CAAC;MACdtC,YAAY,GAAG,CAAC;MAChBU,OAAO,CAACG,OAAO,GAAGC,SAAS;MAC3BJ,OAAO,CAACO,OAAO,GAAGH,SAAS;MAC3BJ,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MAC/BJ,OAAO,CAACC,OAAO,GAAG,CAAC;MACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;IACrB;EACF;EACA,SAAS+G,WAAWA,CAACnJ,CAAC,EAAE;IACtB;IACA,IAAIwB,YAAY,IAAI,CAAC,IAAI,CAACU,OAAO,CAACQ,WAAW,EAAE;IAC/C,IAAI,CAACoD,gBAAgB,CAAC9F,CAAC,CAAC,IAAI,CAACqG,wBAAwB,CAACrG,CAAC,CAAC,EAAE;IAC1D,MAAM4K,gBAAgB,GAAG9J,MAAM,CAAC+J,gBAAgB,CAAC3I,OAAO,CAACQ,WAAW,CAAC,CAACgF,SAAS;IAC/E,MAAMoD,MAAM,GAAG,IAAIhK,MAAM,CAACiK,SAAS,CAACH,gBAAgB,CAAC;IACrD,IAAI,CAAClJ,kBAAkB,EAAE;MACvBA,kBAAkB,GAAG,IAAI;MACzBC,aAAa,CAACC,CAAC,GAAG5B,CAAC,CAACgL,OAAO;MAC3BrJ,aAAa,CAACE,CAAC,GAAG7B,CAAC,CAACiL,OAAO;MAC3BtI,KAAK,CAACW,MAAM,GAAGwH,MAAM,CAAC9K,CAAC;MACvB2C,KAAK,CAACY,MAAM,GAAGuH,MAAM,CAACI,CAAC;MACvBvI,KAAK,CAACS,KAAK,GAAGlB,OAAO,CAACO,OAAO,CAAC2C,WAAW,IAAIlD,OAAO,CAACO,OAAO,CAAC2G,WAAW;MACxEzG,KAAK,CAACU,MAAM,GAAGnB,OAAO,CAACO,OAAO,CAAC4G,YAAY,IAAInH,OAAO,CAACO,OAAO,CAAC6G,YAAY;MAC3EpH,OAAO,CAACK,UAAU,GAAGL,OAAO,CAACG,OAAO,CAAC+C,WAAW;MAChDlD,OAAO,CAACM,WAAW,GAAGN,OAAO,CAACG,OAAO,CAACgH,YAAY;MAClD;IACF;IACA,MAAM8B,MAAM,GAAG,CAACnL,CAAC,CAACgL,OAAO,GAAGrJ,aAAa,CAACC,CAAC,IAAIE,mBAAmB;IAClE,MAAMsJ,MAAM,GAAG,CAACpL,CAAC,CAACiL,OAAO,GAAGtJ,aAAa,CAACE,CAAC,IAAIC,mBAAmB;IAClE,MAAMyH,WAAW,GAAG5G,KAAK,CAACS,KAAK,GAAG5B,YAAY;IAC9C,MAAMgI,YAAY,GAAG7G,KAAK,CAACU,MAAM,GAAG7B,YAAY;IAChD,MAAMe,UAAU,GAAGL,OAAO,CAACK,UAAU;IACrC,MAAMC,WAAW,GAAGN,OAAO,CAACM,WAAW;IACvC,MAAMQ,IAAI,GAAG6B,IAAI,CAACQ,GAAG,CAAC9C,UAAU,GAAG,CAAC,GAAGgH,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;IAC1D,MAAMrG,IAAI,GAAG,CAACF,IAAI;IAClB,MAAMC,IAAI,GAAG4B,IAAI,CAACQ,GAAG,CAAC7C,WAAW,GAAG,CAAC,GAAGgH,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;IAC5D,MAAMrG,IAAI,GAAG,CAACF,IAAI;IAClB,MAAMoI,IAAI,GAAGxG,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACQ,GAAG,CAAC1C,KAAK,CAACW,MAAM,GAAG6H,MAAM,EAAEjI,IAAI,CAAC,EAAEF,IAAI,CAAC;IAClE,MAAMsI,IAAI,GAAGzG,IAAI,CAACgD,GAAG,CAAChD,IAAI,CAACQ,GAAG,CAAC1C,KAAK,CAACY,MAAM,GAAG6H,MAAM,EAAEjI,IAAI,CAAC,EAAEF,IAAI,CAAC;IAClEf,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACC,kBAAkB,GAAG,KAAK;IACpDjF,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACQ,SAAS,GAAG,eAAe2D,IAAI,OAAOC,IAAI,QAAQ;IAC5E3J,aAAa,CAACC,CAAC,GAAG5B,CAAC,CAACgL,OAAO;IAC3BrJ,aAAa,CAACE,CAAC,GAAG7B,CAAC,CAACiL,OAAO;IAC3BtI,KAAK,CAACW,MAAM,GAAG+H,IAAI;IACnB1I,KAAK,CAACY,MAAM,GAAG+H,IAAI;IACnB3I,KAAK,CAACG,QAAQ,GAAGuI,IAAI;IACrB1I,KAAK,CAACI,QAAQ,GAAGuI,IAAI;EACvB;EACA,SAASC,MAAMA,CAACvL,CAAC,EAAE;IACjB,MAAMe,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAMiE,MAAM,GAAGtE,MAAM,CAACsE,MAAM,CAACjE,IAAI;IACjC,IAAI,CAACmB,OAAO,CAACG,OAAO,EAAE;MACpB,IAAIrC,CAAC,IAAIA,CAAC,CAACgG,MAAM,EAAE;QACjB9D,OAAO,CAACG,OAAO,GAAGrC,CAAC,CAACgG,MAAM,CAACe,OAAO,CAAC,IAAIrG,MAAM,CAACsE,MAAM,CAACa,UAAU,gBAAgB,CAAC;MAClF;MACA,IAAI,CAAC3D,OAAO,CAACG,OAAO,EAAE;QACpB,IAAI3B,MAAM,CAACsE,MAAM,CAACwG,OAAO,IAAI9K,MAAM,CAACsE,MAAM,CAACwG,OAAO,CAACxK,OAAO,IAAIN,MAAM,CAAC8K,OAAO,EAAE;UAC5EtJ,OAAO,CAACG,OAAO,GAAGpC,eAAe,CAACS,MAAM,CAAC+K,QAAQ,EAAE,IAAI/K,MAAM,CAACsE,MAAM,CAAC0G,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7F,CAAC,MAAM;UACLxJ,OAAO,CAACG,OAAO,GAAG3B,MAAM,CAACwF,MAAM,CAACxF,MAAM,CAACsG,WAAW,CAAC;QACrD;MACF;MACA,IAAIvE,OAAO,GAAGP,OAAO,CAACG,OAAO,CAAC4E,aAAa,CAAC,IAAIjC,MAAM,CAAC1D,cAAc,EAAE,CAAC;MACxE,IAAImB,OAAO,EAAE;QACXA,OAAO,GAAGA,OAAO,CAAC+D,gBAAgB,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC;MACzF;MACAtE,OAAO,CAACO,OAAO,GAAGA,OAAO;MACzB,IAAIA,OAAO,EAAE;QACXP,OAAO,CAACQ,WAAW,GAAGvC,cAAc,CAAC+B,OAAO,CAACO,OAAO,EAAE,IAAIuC,MAAM,CAAC1D,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,MAAM;QACLY,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MACjC;IACF;IACA,IAAI,CAACJ,OAAO,CAACO,OAAO,IAAI,CAACP,OAAO,CAACQ,WAAW,EAAE;IAC9C,IAAIhC,MAAM,CAACsE,MAAM,CAAC2G,OAAO,EAAE;MACzBjL,MAAM,CAACkL,SAAS,CAAC1E,KAAK,CAAC2E,QAAQ,GAAG,QAAQ;MAC1CnL,MAAM,CAACkL,SAAS,CAAC1E,KAAK,CAAC4E,WAAW,GAAG,MAAM;IAC7C;IACA5J,OAAO,CAACG,OAAO,CAAC0F,SAAS,CAACC,GAAG,CAAC,GAAGhD,MAAM,CAACzD,gBAAgB,EAAE,CAAC;IAC3D,IAAIwK,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,OAAO;IACX,IAAIC,OAAO;IACX,IAAIC,KAAK;IACT,IAAIC,KAAK;IACT,IAAIC,UAAU;IACd,IAAIC,UAAU;IACd,IAAIC,UAAU;IACd,IAAIC,WAAW;IACf,IAAIjD,WAAW;IACf,IAAIC,YAAY;IAChB,IAAIiD,aAAa;IACjB,IAAIC,aAAa;IACjB,IAAIC,aAAa;IACjB,IAAIC,aAAa;IACjB,IAAIrK,UAAU;IACd,IAAIC,WAAW;IACf,IAAI,OAAOG,KAAK,CAACa,YAAY,CAAC5B,CAAC,KAAK,WAAW,IAAI5B,CAAC,EAAE;MACpD+L,MAAM,GAAG/L,CAAC,CAACuE,KAAK;MAChByH,MAAM,GAAGhM,CAAC,CAACyE,KAAK;IAClB,CAAC,MAAM;MACLsH,MAAM,GAAGpJ,KAAK,CAACa,YAAY,CAAC5B,CAAC;MAC7BoK,MAAM,GAAGrJ,KAAK,CAACa,YAAY,CAAC3B,CAAC;IAC/B;IACA,MAAMgL,SAAS,GAAGrL,YAAY;IAC9B,MAAMsL,cAAc,GAAG,OAAO9M,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAG,IAAI;IACvD,IAAIwB,YAAY,KAAK,CAAC,IAAIsL,cAAc,EAAE;MACxCf,MAAM,GAAGzJ,SAAS;MAClB0J,MAAM,GAAG1J,SAAS;MAClBK,KAAK,CAACa,YAAY,CAAC5B,CAAC,GAAGU,SAAS;MAChCK,KAAK,CAACa,YAAY,CAAC3B,CAAC,GAAGS,SAAS;IAClC;IACA,MAAMpB,QAAQ,GAAG6D,WAAW,CAAC,CAAC;IAC9BhE,IAAI,CAAC+C,KAAK,GAAGgJ,cAAc,IAAI5L,QAAQ;IACvCM,YAAY,GAAGsL,cAAc,IAAI5L,QAAQ;IACzC,IAAIlB,CAAC,IAAI,EAAEwB,YAAY,KAAK,CAAC,IAAIsL,cAAc,CAAC,EAAE;MAChDvK,UAAU,GAAGL,OAAO,CAACG,OAAO,CAAC+C,WAAW;MACxC5C,WAAW,GAAGN,OAAO,CAACG,OAAO,CAACgH,YAAY;MAC1C4C,OAAO,GAAG5L,aAAa,CAAC6B,OAAO,CAACG,OAAO,CAAC,CAAC0K,IAAI,GAAGjM,MAAM,CAAC2E,OAAO;MAC9DyG,OAAO,GAAG7L,aAAa,CAAC6B,OAAO,CAACG,OAAO,CAAC,CAAC2K,GAAG,GAAGlM,MAAM,CAAC4E,OAAO;MAC7DyG,KAAK,GAAGF,OAAO,GAAG1J,UAAU,GAAG,CAAC,GAAGwJ,MAAM;MACzCK,KAAK,GAAGF,OAAO,GAAG1J,WAAW,GAAG,CAAC,GAAGwJ,MAAM;MAC1CO,UAAU,GAAGrK,OAAO,CAACO,OAAO,CAAC2C,WAAW,IAAIlD,OAAO,CAACO,OAAO,CAAC2G,WAAW;MACvEoD,WAAW,GAAGtK,OAAO,CAACO,OAAO,CAAC4G,YAAY,IAAInH,OAAO,CAACO,OAAO,CAAC6G,YAAY;MAC1EC,WAAW,GAAGgD,UAAU,GAAGxL,IAAI,CAAC+C,KAAK;MACrC0F,YAAY,GAAGgD,WAAW,GAAGzL,IAAI,CAAC+C,KAAK;MACvC2I,aAAa,GAAG5H,IAAI,CAACQ,GAAG,CAAC9C,UAAU,GAAG,CAAC,GAAGgH,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;MAC7DmD,aAAa,GAAG7H,IAAI,CAACQ,GAAG,CAAC7C,WAAW,GAAG,CAAC,GAAGgH,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;MAC/DmD,aAAa,GAAG,CAACF,aAAa;MAC9BG,aAAa,GAAG,CAACF,aAAa;MAC9B,IAAIG,SAAS,GAAG,CAAC,IAAIC,cAAc,IAAI,OAAOnK,KAAK,CAACG,QAAQ,KAAK,QAAQ,IAAI,OAAOH,KAAK,CAACI,QAAQ,KAAK,QAAQ,EAAE;QAC/GsJ,UAAU,GAAG1J,KAAK,CAACG,QAAQ,GAAG/B,IAAI,CAAC+C,KAAK,GAAG+I,SAAS;QACpDP,UAAU,GAAG3J,KAAK,CAACI,QAAQ,GAAGhC,IAAI,CAAC+C,KAAK,GAAG+I,SAAS;MACtD,CAAC,MAAM;QACLR,UAAU,GAAGF,KAAK,GAAGpL,IAAI,CAAC+C,KAAK;QAC/BwI,UAAU,GAAGF,KAAK,GAAGrL,IAAI,CAAC+C,KAAK;MACjC;MACA,IAAIuI,UAAU,GAAGI,aAAa,EAAE;QAC9BJ,UAAU,GAAGI,aAAa;MAC5B;MACA,IAAIJ,UAAU,GAAGM,aAAa,EAAE;QAC9BN,UAAU,GAAGM,aAAa;MAC5B;MACA,IAAIL,UAAU,GAAGI,aAAa,EAAE;QAC9BJ,UAAU,GAAGI,aAAa;MAC5B;MACA,IAAIJ,UAAU,GAAGM,aAAa,EAAE;QAC9BN,UAAU,GAAGM,aAAa;MAC5B;IACF,CAAC,MAAM;MACLP,UAAU,GAAG,CAAC;MACdC,UAAU,GAAG,CAAC;IAChB;IACA,IAAIQ,cAAc,IAAI/L,IAAI,CAAC+C,KAAK,KAAK,CAAC,EAAE;MACtC5B,OAAO,CAACC,OAAO,GAAG,CAAC;MACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;IACrB;IACAO,KAAK,CAACG,QAAQ,GAAGuJ,UAAU;IAC3B1J,KAAK,CAACI,QAAQ,GAAGuJ,UAAU;IAC3BpK,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IACtDjF,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACQ,SAAS,GAAG,eAAe2E,UAAU,OAAOC,UAAU,OAAO;IACvFpK,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IAClDjF,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACQ,SAAS,GAAG,4BAA4B3G,IAAI,CAAC+C,KAAK,GAAG;EAC7E;EACA,SAASmJ,OAAOA,CAAA,EAAG;IACjB,MAAMlM,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,MAAMiE,MAAM,GAAGtE,MAAM,CAACsE,MAAM,CAACjE,IAAI;IACjC,IAAI,CAACmB,OAAO,CAACG,OAAO,EAAE;MACpB,IAAI3B,MAAM,CAACsE,MAAM,CAACwG,OAAO,IAAI9K,MAAM,CAACsE,MAAM,CAACwG,OAAO,CAACxK,OAAO,IAAIN,MAAM,CAAC8K,OAAO,EAAE;QAC5EtJ,OAAO,CAACG,OAAO,GAAGpC,eAAe,CAACS,MAAM,CAAC+K,QAAQ,EAAE,IAAI/K,MAAM,CAACsE,MAAM,CAAC0G,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;MAC7F,CAAC,MAAM;QACLxJ,OAAO,CAACG,OAAO,GAAG3B,MAAM,CAACwF,MAAM,CAACxF,MAAM,CAACsG,WAAW,CAAC;MACrD;MACA,IAAIvE,OAAO,GAAGP,OAAO,CAACG,OAAO,CAAC4E,aAAa,CAAC,IAAIjC,MAAM,CAAC1D,cAAc,EAAE,CAAC;MACxE,IAAImB,OAAO,EAAE;QACXA,OAAO,GAAGA,OAAO,CAAC+D,gBAAgB,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC;MACzF;MACAtE,OAAO,CAACO,OAAO,GAAGA,OAAO;MACzB,IAAIA,OAAO,EAAE;QACXP,OAAO,CAACQ,WAAW,GAAGvC,cAAc,CAAC+B,OAAO,CAACO,OAAO,EAAE,IAAIuC,MAAM,CAAC1D,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,MAAM;QACLY,OAAO,CAACQ,WAAW,GAAGJ,SAAS;MACjC;IACF;IACA,IAAI,CAACJ,OAAO,CAACO,OAAO,IAAI,CAACP,OAAO,CAACQ,WAAW,EAAE;IAC9C,IAAIhC,MAAM,CAACsE,MAAM,CAAC2G,OAAO,EAAE;MACzBjL,MAAM,CAACkL,SAAS,CAAC1E,KAAK,CAAC2E,QAAQ,GAAG,EAAE;MACpCnL,MAAM,CAACkL,SAAS,CAAC1E,KAAK,CAAC4E,WAAW,GAAG,EAAE;IACzC;IACA/K,IAAI,CAAC+C,KAAK,GAAG,CAAC;IACdtC,YAAY,GAAG,CAAC;IAChBmB,KAAK,CAACG,QAAQ,GAAGR,SAAS;IAC1BK,KAAK,CAACI,QAAQ,GAAGT,SAAS;IAC1BK,KAAK,CAACa,YAAY,CAAC5B,CAAC,GAAGU,SAAS;IAChCK,KAAK,CAACa,YAAY,CAAC3B,CAAC,GAAGS,SAAS;IAChCJ,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IACtDjF,OAAO,CAACQ,WAAW,CAACwE,KAAK,CAACQ,SAAS,GAAG,oBAAoB;IAC1DxF,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACC,kBAAkB,GAAG,OAAO;IAClDjF,OAAO,CAACO,OAAO,CAACyE,KAAK,CAACQ,SAAS,GAAG,6BAA6B;IAC/DxF,OAAO,CAACG,OAAO,CAAC0F,SAAS,CAACE,MAAM,CAAC,GAAGjD,MAAM,CAACzD,gBAAgB,EAAE,CAAC;IAC9DW,OAAO,CAACG,OAAO,GAAGC,SAAS;IAC3BJ,OAAO,CAACC,OAAO,GAAG,CAAC;IACnBD,OAAO,CAACE,OAAO,GAAG,CAAC;IACnB,IAAI1B,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACK,cAAc,EAAE;MACrCO,aAAa,GAAG;QACdC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;MACD,IAAIH,kBAAkB,EAAE;QACtBA,kBAAkB,GAAG,KAAK;QAC1BiB,KAAK,CAACW,MAAM,GAAG,CAAC;QAChBX,KAAK,CAACY,MAAM,GAAG,CAAC;MAClB;IACF;EACF;;EAEA;EACA,SAAS2J,UAAUA,CAAClN,CAAC,EAAE;IACrB,MAAMe,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAIA,IAAI,CAAC+C,KAAK,IAAI/C,IAAI,CAAC+C,KAAK,KAAK,CAAC,EAAE;MAClC;MACAmJ,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACL;MACA1B,MAAM,CAACvL,CAAC,CAAC;IACX;EACF;EACA,SAASmN,YAAYA,CAAA,EAAG;IACtB,MAAMC,eAAe,GAAG1M,MAAM,CAACsE,MAAM,CAACqI,gBAAgB,GAAG;MACvDC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC,GAAG,KAAK;IACT,MAAMC,yBAAyB,GAAG9M,MAAM,CAACsE,MAAM,CAACqI,gBAAgB,GAAG;MACjEC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,GAAG,IAAI;IACR,OAAO;MACLH,eAAe;MACfI;IACF,CAAC;EACH;;EAEA;EACA,SAASC,MAAMA,CAAA,EAAG;IAChB,MAAM1M,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAIA,IAAI,CAACC,OAAO,EAAE;IAClBD,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,MAAM;MACJoM,eAAe;MACfI;IACF,CAAC,GAAGL,YAAY,CAAC,CAAC;;IAElB;IACAzM,MAAM,CAACkL,SAAS,CAAC8B,gBAAgB,CAAC,aAAa,EAAEhH,cAAc,EAAE0G,eAAe,CAAC;IACjF1M,MAAM,CAACkL,SAAS,CAAC8B,gBAAgB,CAAC,aAAa,EAAEtG,eAAe,EAAEoG,yBAAyB,CAAC;IAC5F,CAAC,WAAW,EAAE,eAAe,EAAE,YAAY,CAAC,CAACG,OAAO,CAACC,SAAS,IAAI;MAChElN,MAAM,CAACkL,SAAS,CAAC8B,gBAAgB,CAACE,SAAS,EAAEjG,YAAY,EAAEyF,eAAe,CAAC;IAC7E,CAAC,CAAC;;IAEF;IACA1M,MAAM,CAACkL,SAAS,CAAC8B,gBAAgB,CAAC,aAAa,EAAE1E,WAAW,EAAEwE,yBAAyB,CAAC;EAC1F;EACA,SAASK,OAAOA,CAAA,EAAG;IACjB,MAAM9M,IAAI,GAAGL,MAAM,CAACK,IAAI;IACxB,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;IACnBD,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,MAAM;MACJoM,eAAe;MACfI;IACF,CAAC,GAAGL,YAAY,CAAC,CAAC;;IAElB;IACAzM,MAAM,CAACkL,SAAS,CAACkC,mBAAmB,CAAC,aAAa,EAAEpH,cAAc,EAAE0G,eAAe,CAAC;IACpF1M,MAAM,CAACkL,SAAS,CAACkC,mBAAmB,CAAC,aAAa,EAAE1G,eAAe,EAAEoG,yBAAyB,CAAC;IAC/F,CAAC,WAAW,EAAE,eAAe,EAAE,YAAY,CAAC,CAACG,OAAO,CAACC,SAAS,IAAI;MAChElN,MAAM,CAACkL,SAAS,CAACkC,mBAAmB,CAACF,SAAS,EAAEjG,YAAY,EAAEyF,eAAe,CAAC;IAChF,CAAC,CAAC;;IAEF;IACA1M,MAAM,CAACkL,SAAS,CAACkC,mBAAmB,CAAC,aAAa,EAAE9E,WAAW,EAAEwE,yBAAyB,CAAC;EAC7F;EACA5M,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACC,OAAO,EAAE;MAC9ByM,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACF7M,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBiN,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;EACFjN,EAAE,CAAC,YAAY,EAAE,CAACmN,EAAE,EAAE/N,CAAC,KAAK;IAC1B,IAAI,CAACU,MAAM,CAACK,IAAI,CAACC,OAAO,EAAE;IAC1B0H,YAAY,CAAC1I,CAAC,CAAC;EACjB,CAAC,CAAC;EACFY,EAAE,CAAC,UAAU,EAAE,CAACmN,EAAE,EAAE/N,CAAC,KAAK;IACxB,IAAI,CAACU,MAAM,CAACK,IAAI,CAACC,OAAO,EAAE;IAC1BkJ,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACFtJ,EAAE,CAAC,WAAW,EAAE,CAACmN,EAAE,EAAE/N,CAAC,KAAK;IACzB,IAAI,CAACU,MAAM,CAACsN,SAAS,IAAItN,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACK,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACM,MAAM,EAAE;MACvG6L,UAAU,CAAClN,CAAC,CAAC;IACf;EACF,CAAC,CAAC;EACFY,EAAE,CAAC,eAAe,EAAE,MAAM;IACxB,IAAIF,MAAM,CAACK,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACC,OAAO,EAAE;MACrD0J,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,CAAC;EACF9J,EAAE,CAAC,aAAa,EAAE,MAAM;IACtB,IAAIF,MAAM,CAACK,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACsE,MAAM,CAACjE,IAAI,CAACC,OAAO,IAAIN,MAAM,CAACsE,MAAM,CAAC2G,OAAO,EAAE;MAC9EjB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,CAAC;EACF3G,MAAM,CAACkK,MAAM,CAACvN,MAAM,CAACK,IAAI,EAAE;IACzB0M,MAAM;IACNI,OAAO;IACPK,EAAE,EAAE3C,MAAM;IACV4C,GAAG,EAAElB,OAAO;IACZ5L,MAAM,EAAE6L;EACV,CAAC,CAAC;AACJ;AAEA,SAAS1M,IAAI,IAAI4N,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}