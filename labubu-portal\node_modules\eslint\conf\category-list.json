{"categories": [{"name": "Possible Errors", "description": "These rules relate to possible syntax or logic errors in JavaScript code:"}, {"name": "Best Practices", "description": "These rules relate to better ways of doing things to help you avoid problems:"}, {"name": "Strict Mode", "description": "These rules relate to strict mode directives:"}, {"name": "Variables", "description": "These rules relate to variable declarations:"}, {"name": "Stylistic Issues", "description": "These rules relate to style guidelines, and are therefore quite subjective:"}, {"name": "ECMAScript 6", "description": "These rules relate to ES6, also known as ES2015:"}], "deprecated": {"name": "Deprecated", "description": "These rules have been deprecated in accordance with the <a href=\"/docs/user-guide/rule-deprecation\">deprecation policy</a>, and replaced by newer rules:", "rules": []}, "removed": {"name": "Removed", "description": "These rules from older versions of ESLint (before the <a href=\"/docs/user-guide/rule-deprecation\">deprecation policy</a> existed) have been replaced by newer rules:", "rules": [{"removed": "generator-star", "replacedBy": ["generator-star-spacing"]}, {"removed": "global-strict", "replacedBy": ["strict"]}, {"removed": "no-arrow-condition", "replacedBy": ["no-confusing-arrow", "no-constant-condition"]}, {"removed": "no-comma-dangle", "replacedBy": ["comma-dangle"]}, {"removed": "no-empty-class", "replacedBy": ["no-empty-character-class"]}, {"removed": "no-empty-label", "replacedBy": ["no-labels"]}, {"removed": "no-extra-strict", "replacedBy": ["strict"]}, {"removed": "no-reserved-keys", "replacedBy": ["quote-props"]}, {"removed": "no-space-before-semi", "replacedBy": ["semi-spacing"]}, {"removed": "no-wrap-func", "replacedBy": ["no-extra-parens"]}, {"removed": "space-after-function-name", "replacedBy": ["space-before-function-paren"]}, {"removed": "space-after-keywords", "replacedBy": ["keyword-spacing"]}, {"removed": "space-before-function-parentheses", "replacedBy": ["space-before-function-paren"]}, {"removed": "space-before-keywords", "replacedBy": ["keyword-spacing"]}, {"removed": "space-in-brackets", "replacedBy": ["object-curly-spacing", "array-bracket-spacing"]}, {"removed": "space-return-throw-case", "replacedBy": ["keyword-spacing"]}, {"removed": "space-unary-word-ops", "replacedBy": ["space-unary-ops"]}, {"removed": "spaced-line-comment", "replacedBy": ["spaced-comment"]}]}}