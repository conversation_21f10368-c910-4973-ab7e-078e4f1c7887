{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport LegendModel from './LegendModel.js';\nimport { mergeLayoutParam, getLayoutParams } from '../../util/layout.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar ScrollableLegendModel = /** @class */function (_super) {\n  __extends(ScrollableLegendModel, _super);\n  function ScrollableLegendModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScrollableLegendModel.type;\n    return _this;\n  }\n  /**\r\n   * @param {number} scrollDataIndex\r\n   */\n  ScrollableLegendModel.prototype.setScrollDataIndex = function (scrollDataIndex) {\n    this.option.scrollDataIndex = scrollDataIndex;\n  };\n  ScrollableLegendModel.prototype.init = function (option, parentModel, ecModel) {\n    var inputPositionParams = getLayoutParams(option);\n    _super.prototype.init.call(this, option, parentModel, ecModel);\n    mergeAndNormalizeLayoutParams(this, option, inputPositionParams);\n  };\n  /**\r\n   * @override\r\n   */\n  ScrollableLegendModel.prototype.mergeOption = function (option, ecModel) {\n    _super.prototype.mergeOption.call(this, option, ecModel);\n    mergeAndNormalizeLayoutParams(this, this.option, option);\n  };\n  ScrollableLegendModel.type = 'legend.scroll';\n  ScrollableLegendModel.defaultOption = inheritDefaultOption(LegendModel.defaultOption, {\n    scrollDataIndex: 0,\n    pageButtonItemGap: 5,\n    pageButtonGap: null,\n    pageButtonPosition: 'end',\n    pageFormatter: '{current}/{total}',\n    pageIcons: {\n      horizontal: ['M0,0L12,-10L12,10z', 'M0,0L-12,-10L-12,10z'],\n      vertical: ['M0,0L20,0L10,-20z', 'M0,0L20,0L10,20z']\n    },\n    pageIconColor: '#2f4554',\n    pageIconInactiveColor: '#aaa',\n    pageIconSize: 15,\n    pageTextStyle: {\n      color: '#333'\n    },\n    animationDurationUpdate: 800\n  });\n  return ScrollableLegendModel;\n}(LegendModel);\n;\n// Do not `ignoreSize` to enable setting {left: 10, right: 10}.\nfunction mergeAndNormalizeLayoutParams(legendModel, target, raw) {\n  var orient = legendModel.getOrient();\n  var ignoreSize = [1, 1];\n  ignoreSize[orient.index] = 0;\n  mergeLayoutParam(target, raw, {\n    type: 'box',\n    ignoreSize: !!ignoreSize\n  });\n}\nexport default ScrollableLegendModel;", "map": {"version": 3, "names": ["__extends", "LegendModel", "mergeLayoutParam", "getLayoutParams", "inheritDefaultOption", "ScrollableLegendModel", "_super", "_this", "apply", "arguments", "type", "prototype", "setScrollDataIndex", "scrollDataIndex", "option", "init", "parentModel", "ecModel", "inputPositionParams", "call", "mergeAndNormalizeLayoutParams", "mergeOption", "defaultOption", "pageButtonItemGap", "pageButtonGap", "pageButtonPosition", "pageFormatter", "pageIcons", "horizontal", "vertical", "pageIconColor", "pageIconInactiveColor", "pageIconSize", "pageTextStyle", "color", "animationDurationUpdate", "legend<PERSON><PERSON><PERSON>", "target", "raw", "orient", "getOrient", "ignoreSize", "index"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/legend/ScrollableLegendModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport LegendModel from './LegendModel.js';\nimport { mergeLayoutParam, getLayoutParams } from '../../util/layout.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar ScrollableLegendModel = /** @class */function (_super) {\n  __extends(ScrollableLegendModel, _super);\n  function ScrollableLegendModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ScrollableLegendModel.type;\n    return _this;\n  }\n  /**\r\n   * @param {number} scrollDataIndex\r\n   */\n  ScrollableLegendModel.prototype.setScrollDataIndex = function (scrollDataIndex) {\n    this.option.scrollDataIndex = scrollDataIndex;\n  };\n  ScrollableLegendModel.prototype.init = function (option, parentModel, ecModel) {\n    var inputPositionParams = getLayoutParams(option);\n    _super.prototype.init.call(this, option, parentModel, ecModel);\n    mergeAndNormalizeLayoutParams(this, option, inputPositionParams);\n  };\n  /**\r\n   * @override\r\n   */\n  ScrollableLegendModel.prototype.mergeOption = function (option, ecModel) {\n    _super.prototype.mergeOption.call(this, option, ecModel);\n    mergeAndNormalizeLayoutParams(this, this.option, option);\n  };\n  ScrollableLegendModel.type = 'legend.scroll';\n  ScrollableLegendModel.defaultOption = inheritDefaultOption(LegendModel.defaultOption, {\n    scrollDataIndex: 0,\n    pageButtonItemGap: 5,\n    pageButtonGap: null,\n    pageButtonPosition: 'end',\n    pageFormatter: '{current}/{total}',\n    pageIcons: {\n      horizontal: ['M0,0L12,-10L12,10z', 'M0,0L-12,-10L-12,10z'],\n      vertical: ['M0,0L20,0L10,-20z', 'M0,0L20,0L10,20z']\n    },\n    pageIconColor: '#2f4554',\n    pageIconInactiveColor: '#aaa',\n    pageIconSize: 15,\n    pageTextStyle: {\n      color: '#333'\n    },\n    animationDurationUpdate: 800\n  });\n  return ScrollableLegendModel;\n}(LegendModel);\n;\n// Do not `ignoreSize` to enable setting {left: 10, right: 10}.\nfunction mergeAndNormalizeLayoutParams(legendModel, target, raw) {\n  var orient = legendModel.getOrient();\n  var ignoreSize = [1, 1];\n  ignoreSize[orient.index] = 0;\n  mergeLayoutParam(target, raw, {\n    type: 'box',\n    ignoreSize: !!ignoreSize\n  });\n}\nexport default ScrollableLegendModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,gBAAgB,EAAEC,eAAe,QAAQ,sBAAsB;AACxE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,qBAAqB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACzDN,SAAS,CAACK,qBAAqB,EAAEC,MAAM,CAAC;EACxC,SAASD,qBAAqBA,CAAA,EAAG;IAC/B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,qBAAqB,CAACK,IAAI;IACvC,OAAOH,KAAK;EACd;EACA;AACF;AACA;EACEF,qBAAqB,CAACM,SAAS,CAACC,kBAAkB,GAAG,UAAUC,eAAe,EAAE;IAC9E,IAAI,CAACC,MAAM,CAACD,eAAe,GAAGA,eAAe;EAC/C,CAAC;EACDR,qBAAqB,CAACM,SAAS,CAACI,IAAI,GAAG,UAAUD,MAAM,EAAEE,WAAW,EAAEC,OAAO,EAAE;IAC7E,IAAIC,mBAAmB,GAAGf,eAAe,CAACW,MAAM,CAAC;IACjDR,MAAM,CAACK,SAAS,CAACI,IAAI,CAACI,IAAI,CAAC,IAAI,EAAEL,MAAM,EAAEE,WAAW,EAAEC,OAAO,CAAC;IAC9DG,6BAA6B,CAAC,IAAI,EAAEN,MAAM,EAAEI,mBAAmB,CAAC;EAClE,CAAC;EACD;AACF;AACA;EACEb,qBAAqB,CAACM,SAAS,CAACU,WAAW,GAAG,UAAUP,MAAM,EAAEG,OAAO,EAAE;IACvEX,MAAM,CAACK,SAAS,CAACU,WAAW,CAACF,IAAI,CAAC,IAAI,EAAEL,MAAM,EAAEG,OAAO,CAAC;IACxDG,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAACN,MAAM,EAAEA,MAAM,CAAC;EAC1D,CAAC;EACDT,qBAAqB,CAACK,IAAI,GAAG,eAAe;EAC5CL,qBAAqB,CAACiB,aAAa,GAAGlB,oBAAoB,CAACH,WAAW,CAACqB,aAAa,EAAE;IACpFT,eAAe,EAAE,CAAC;IAClBU,iBAAiB,EAAE,CAAC;IACpBC,aAAa,EAAE,IAAI;IACnBC,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,mBAAmB;IAClCC,SAAS,EAAE;MACTC,UAAU,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;MAC1DC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,kBAAkB;IACpD,CAAC;IACDC,aAAa,EAAE,SAAS;IACxBC,qBAAqB,EAAE,MAAM;IAC7BC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;MACbC,KAAK,EAAE;IACT,CAAC;IACDC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,OAAO9B,qBAAqB;AAC9B,CAAC,CAACJ,WAAW,CAAC;AACd;AACA;AACA,SAASmB,6BAA6BA,CAACgB,WAAW,EAAEC,MAAM,EAAEC,GAAG,EAAE;EAC/D,IAAIC,MAAM,GAAGH,WAAW,CAACI,SAAS,CAAC,CAAC;EACpC,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACvBA,UAAU,CAACF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;EAC5BxC,gBAAgB,CAACmC,MAAM,EAAEC,GAAG,EAAE;IAC5B5B,IAAI,EAAE,KAAK;IACX+B,UAAU,EAAE,CAAC,CAACA;EAChB,CAAC,CAAC;AACJ;AACA,eAAepC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}