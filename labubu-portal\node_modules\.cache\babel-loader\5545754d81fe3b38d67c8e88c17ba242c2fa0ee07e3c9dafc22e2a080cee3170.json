{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar platform = '';\n// Navigator not exists in node\nif (typeof navigator !== 'undefined') {\n  /* global navigator */\n  platform = navigator.platform || '';\n}\nvar decalColor = 'rgba(0, 0, 0, 0.2)';\nexport default {\n  darkMode: 'auto',\n  // backgroundColor: 'rgba(0,0,0,0)',\n  colorBy: 'series',\n  color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],\n  gradientColor: ['#f6efa6', '#d88273', '#bf444c'],\n  aria: {\n    decal: {\n      decals: [{\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [2, 5],\n        symbolSize: 1,\n        rotation: Math.PI / 6\n      }, {\n        color: decalColor,\n        symbol: 'circle',\n        dashArrayX: [[8, 8], [0, 8, 8, 0]],\n        dashArrayY: [6, 0],\n        symbolSize: 0.8\n      }, {\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [4, 3],\n        rotation: -Math.PI / 4\n      }, {\n        color: decalColor,\n        dashArrayX: [[6, 6], [0, 6, 6, 0]],\n        dashArrayY: [6, 0]\n      }, {\n        color: decalColor,\n        dashArrayX: [[1, 0], [1, 6]],\n        dashArrayY: [1, 0, 6, 0],\n        rotation: Math.PI / 4\n      }, {\n        color: decalColor,\n        symbol: 'triangle',\n        dashArrayX: [[9, 9], [0, 9, 9, 0]],\n        dashArrayY: [7, 2],\n        symbolSize: 0.75\n      }]\n    }\n  },\n  // If xAxis and yAxis declared, grid is created by default.\n  // grid: {},\n  textStyle: {\n    // color: '#000',\n    // decoration: 'none',\n    // PENDING\n    fontFamily: platform.match(/^Win/) ? 'Microsoft YaHei' : 'sans-serif',\n    // fontFamily: 'Arial, Verdana, sans-serif',\n    fontSize: 12,\n    fontStyle: 'normal',\n    fontWeight: 'normal'\n  },\n  // http://blogs.adobe.com/webplatform/2014/02/24/using-blend-modes-in-html-canvas/\n  // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\n  // Default is source-over\n  blendMode: null,\n  stateAnimation: {\n    duration: 300,\n    easing: 'cubicOut'\n  },\n  animation: 'auto',\n  animationDuration: 1000,\n  animationDurationUpdate: 500,\n  animationEasing: 'cubicInOut',\n  animationEasingUpdate: 'cubicInOut',\n  animationThreshold: 2000,\n  // Configuration for progressive/incremental rendering\n  progressiveThreshold: 3000,\n  progressive: 400,\n  // Threshold of if use single hover layer to optimize.\n  // It is recommended that `hoverLayerThreshold` is equivalent to or less than\n  // `progressiveThreshold`, otherwise hover will cause restart of progressive,\n  // which is unexpected.\n  // see example <echarts/test/heatmap-large.html>.\n  hoverLayerThreshold: 3000,\n  // See: module:echarts/scale/Time\n  useUTC: false\n};", "map": {"version": 3, "names": ["platform", "navigator", "decalColor", "darkMode", "colorBy", "color", "gradientColor", "aria", "decal", "decals", "dashArrayX", "dashArrayY", "symbolSize", "rotation", "Math", "PI", "symbol", "textStyle", "fontFamily", "match", "fontSize", "fontStyle", "fontWeight", "blendMode", "stateAnimation", "duration", "easing", "animation", "animationDuration", "animationDurationUpdate", "animationEasing", "animationEasingUpdate", "animationThreshold", "progressiveThreshold", "progressive", "hoverLayerThreshold", "useUTC"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/model/globalDefault.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar platform = '';\n// Navigator not exists in node\nif (typeof navigator !== 'undefined') {\n  /* global navigator */\n  platform = navigator.platform || '';\n}\nvar decalColor = 'rgba(0, 0, 0, 0.2)';\nexport default {\n  darkMode: 'auto',\n  // backgroundColor: 'rgba(0,0,0,0)',\n  colorBy: 'series',\n  color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],\n  gradientColor: ['#f6efa6', '#d88273', '#bf444c'],\n  aria: {\n    decal: {\n      decals: [{\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [2, 5],\n        symbolSize: 1,\n        rotation: Math.PI / 6\n      }, {\n        color: decalColor,\n        symbol: 'circle',\n        dashArrayX: [[8, 8], [0, 8, 8, 0]],\n        dashArrayY: [6, 0],\n        symbolSize: 0.8\n      }, {\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [4, 3],\n        rotation: -Math.PI / 4\n      }, {\n        color: decalColor,\n        dashArrayX: [[6, 6], [0, 6, 6, 0]],\n        dashArrayY: [6, 0]\n      }, {\n        color: decalColor,\n        dashArrayX: [[1, 0], [1, 6]],\n        dashArrayY: [1, 0, 6, 0],\n        rotation: Math.PI / 4\n      }, {\n        color: decalColor,\n        symbol: 'triangle',\n        dashArrayX: [[9, 9], [0, 9, 9, 0]],\n        dashArrayY: [7, 2],\n        symbolSize: 0.75\n      }]\n    }\n  },\n  // If xAxis and yAxis declared, grid is created by default.\n  // grid: {},\n  textStyle: {\n    // color: '#000',\n    // decoration: 'none',\n    // PENDING\n    fontFamily: platform.match(/^Win/) ? 'Microsoft YaHei' : 'sans-serif',\n    // fontFamily: 'Arial, Verdana, sans-serif',\n    fontSize: 12,\n    fontStyle: 'normal',\n    fontWeight: 'normal'\n  },\n  // http://blogs.adobe.com/webplatform/2014/02/24/using-blend-modes-in-html-canvas/\n  // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\n  // Default is source-over\n  blendMode: null,\n  stateAnimation: {\n    duration: 300,\n    easing: 'cubicOut'\n  },\n  animation: 'auto',\n  animationDuration: 1000,\n  animationDurationUpdate: 500,\n  animationEasing: 'cubicInOut',\n  animationEasingUpdate: 'cubicInOut',\n  animationThreshold: 2000,\n  // Configuration for progressive/incremental rendering\n  progressiveThreshold: 3000,\n  progressive: 400,\n  // Threshold of if use single hover layer to optimize.\n  // It is recommended that `hoverLayerThreshold` is equivalent to or less than\n  // `progressiveThreshold`, otherwise hover will cause restart of progressive,\n  // which is unexpected.\n  // see example <echarts/test/heatmap-large.html>.\n  hoverLayerThreshold: 3000,\n  // See: module:echarts/scale/Time\n  useUTC: false\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAG,EAAE;AACjB;AACA,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;EACpC;EACAD,QAAQ,GAAGC,SAAS,CAACD,QAAQ,IAAI,EAAE;AACrC;AACA,IAAIE,UAAU,GAAG,oBAAoB;AACrC,eAAe;EACbC,QAAQ,EAAE,MAAM;EAChB;EACAC,OAAO,EAAE,QAAQ;EACjBC,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAC1GC,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EAChDC,IAAI,EAAE;IACJC,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;QACPJ,KAAK,EAAEH,UAAU;QACjBQ,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAEC,IAAI,CAACC,EAAE,GAAG;MACtB,CAAC,EAAE;QACDV,KAAK,EAAEH,UAAU;QACjBc,MAAM,EAAE,QAAQ;QAChBN,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,UAAU,EAAE;MACd,CAAC,EAAE;QACDP,KAAK,EAAEH,UAAU;QACjBQ,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBE,QAAQ,EAAE,CAACC,IAAI,CAACC,EAAE,GAAG;MACvB,CAAC,EAAE;QACDV,KAAK,EAAEH,UAAU;QACjBQ,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;MACnB,CAAC,EAAE;QACDN,KAAK,EAAEH,UAAU;QACjBQ,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBE,QAAQ,EAAEC,IAAI,CAACC,EAAE,GAAG;MACtB,CAAC,EAAE;QACDV,KAAK,EAAEH,UAAU;QACjBc,MAAM,EAAE,UAAU;QAClBN,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,UAAU,EAAE;MACd,CAAC;IACH;EACF,CAAC;EACD;EACA;EACAK,SAAS,EAAE;IACT;IACA;IACA;IACAC,UAAU,EAAElB,QAAQ,CAACmB,KAAK,CAAC,MAAM,CAAC,GAAG,iBAAiB,GAAG,YAAY;IACrE;IACAC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd,CAAC;EACD;EACA;EACA;EACAC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE;IACdC,QAAQ,EAAE,GAAG;IACbC,MAAM,EAAE;EACV,CAAC;EACDC,SAAS,EAAE,MAAM;EACjBC,iBAAiB,EAAE,IAAI;EACvBC,uBAAuB,EAAE,GAAG;EAC5BC,eAAe,EAAE,YAAY;EAC7BC,qBAAqB,EAAE,YAAY;EACnCC,kBAAkB,EAAE,IAAI;EACxB;EACAC,oBAAoB,EAAE,IAAI;EAC1BC,WAAW,EAAE,GAAG;EAChB;EACA;EACA;EACA;EACA;EACAC,mBAAmB,EAAE,IAAI;EACzB;EACAC,MAAM,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}