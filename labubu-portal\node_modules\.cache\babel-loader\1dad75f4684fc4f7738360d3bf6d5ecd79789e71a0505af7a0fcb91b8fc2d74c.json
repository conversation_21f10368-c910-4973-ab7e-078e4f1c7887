{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The method \"quantile\" was copied from \"d3.js\".\r\n* (See more details in the comment of the method below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar RADIAN_EPSILON = 1e-4;\n// Although chrome already enlarge this number to 100 for `toFixed`, but\n// we sill follow the spec for compatibility.\nvar ROUND_SUPPORTED_PRECISION_MAX = 20;\nfunction _trim(str) {\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n/**\r\n * Linear mapping a value from domain to range\r\n * @param  val\r\n * @param  domain Domain extent domain[0] can be bigger than domain[1]\r\n * @param  range  Range extent range[0] can be bigger than range[1]\r\n * @param  clamp Default to be false\r\n */\nexport function linearMap(val, domain, range, clamp) {\n  var d0 = domain[0];\n  var d1 = domain[1];\n  var r0 = range[0];\n  var r1 = range[1];\n  var subDomain = d1 - d0;\n  var subRange = r1 - r0;\n  if (subDomain === 0) {\n    return subRange === 0 ? r0 : (r0 + r1) / 2;\n  }\n  // Avoid accuracy problem in edge, such as\n  // 146.39 - 62.83 === 83.55999999999999.\n  // See echarts/test/ut/spec/util/number.js#linearMap#accuracyError\n  // It is a little verbose for efficiency considering this method\n  // is a hotspot.\n  if (clamp) {\n    if (subDomain > 0) {\n      if (val <= d0) {\n        return r0;\n      } else if (val >= d1) {\n        return r1;\n      }\n    } else {\n      if (val >= d0) {\n        return r0;\n      } else if (val <= d1) {\n        return r1;\n      }\n    }\n  } else {\n    if (val === d0) {\n      return r0;\n    }\n    if (val === d1) {\n      return r1;\n    }\n  }\n  return (val - d0) / subDomain * subRange + r0;\n}\n/**\r\n * Convert a percent string to absolute number.\r\n * Returns NaN if percent is not a valid string or number\r\n */\nexport function parsePercent(percent, all) {\n  switch (percent) {\n    case 'center':\n    case 'middle':\n      percent = '50%';\n      break;\n    case 'left':\n    case 'top':\n      percent = '0%';\n      break;\n    case 'right':\n    case 'bottom':\n      percent = '100%';\n      break;\n  }\n  if (zrUtil.isString(percent)) {\n    if (_trim(percent).match(/%$/)) {\n      return parseFloat(percent) / 100 * all;\n    }\n    return parseFloat(percent);\n  }\n  return percent == null ? NaN : +percent;\n}\nexport function round(x, precision, returnStr) {\n  if (precision == null) {\n    precision = 10;\n  }\n  // Avoid range error\n  precision = Math.min(Math.max(0, precision), ROUND_SUPPORTED_PRECISION_MAX);\n  // PENDING: 1.005.toFixed(2) is '1.00' rather than '1.01'\n  x = (+x).toFixed(precision);\n  return returnStr ? x : +x;\n}\n/**\r\n * Inplacd asc sort arr.\r\n * The input arr will be modified.\r\n */\nexport function asc(arr) {\n  arr.sort(function (a, b) {\n    return a - b;\n  });\n  return arr;\n}\n/**\r\n * Get precision.\r\n */\nexport function getPrecision(val) {\n  val = +val;\n  if (isNaN(val)) {\n    return 0;\n  }\n  // It is much faster than methods converting number to string as follows\n  //      let tmp = val.toString();\n  //      return tmp.length - 1 - tmp.indexOf('.');\n  // especially when precision is low\n  // Notice:\n  // (1) If the loop count is over about 20, it is slower than `getPrecisionSafe`.\n  //     (see https://jsbench.me/2vkpcekkvw/1)\n  // (2) If the val is less than for example 1e-15, the result may be incorrect.\n  //     (see test/ut/spec/util/number.test.ts `getPrecision_equal_random`)\n  if (val > 1e-14) {\n    var e = 1;\n    for (var i = 0; i < 15; i++, e *= 10) {\n      if (Math.round(val * e) / e === val) {\n        return i;\n      }\n    }\n  }\n  return getPrecisionSafe(val);\n}\n/**\r\n * Get precision with slow but safe method\r\n */\nexport function getPrecisionSafe(val) {\n  // toLowerCase for: '3.4E-12'\n  var str = val.toString().toLowerCase();\n  // Consider scientific notation: '3.4e-12' '3.4e+12'\n  var eIndex = str.indexOf('e');\n  var exp = eIndex > 0 ? +str.slice(eIndex + 1) : 0;\n  var significandPartLen = eIndex > 0 ? eIndex : str.length;\n  var dotIndex = str.indexOf('.');\n  var decimalPartLen = dotIndex < 0 ? 0 : significandPartLen - 1 - dotIndex;\n  return Math.max(0, decimalPartLen - exp);\n}\n/**\r\n * Minimal dicernible data precisioin according to a single pixel.\r\n */\nexport function getPixelPrecision(dataExtent, pixelExtent) {\n  var log = Math.log;\n  var LN10 = Math.LN10;\n  var dataQuantity = Math.floor(log(dataExtent[1] - dataExtent[0]) / LN10);\n  var sizeQuantity = Math.round(log(Math.abs(pixelExtent[1] - pixelExtent[0])) / LN10);\n  // toFixed() digits argument must be between 0 and 20.\n  var precision = Math.min(Math.max(-dataQuantity + sizeQuantity, 0), 20);\n  return !isFinite(precision) ? 20 : precision;\n}\n/**\r\n * Get a data of given precision, assuring the sum of percentages\r\n * in valueList is 1.\r\n * The largest remainder method is used.\r\n * https://en.wikipedia.org/wiki/Largest_remainder_method\r\n *\r\n * @param valueList a list of all data\r\n * @param idx index of the data to be processed in valueList\r\n * @param precision integer number showing digits of precision\r\n * @return percent ranging from 0 to 100\r\n */\nexport function getPercentWithPrecision(valueList, idx, precision) {\n  if (!valueList[idx]) {\n    return 0;\n  }\n  var seats = getPercentSeats(valueList, precision);\n  return seats[idx] || 0;\n}\n/**\r\n * Get a data of given precision, assuring the sum of percentages\r\n * in valueList is 1.\r\n * The largest remainder method is used.\r\n * https://en.wikipedia.org/wiki/Largest_remainder_method\r\n *\r\n * @param valueList a list of all data\r\n * @param precision integer number showing digits of precision\r\n * @return {Array<number>}\r\n */\nexport function getPercentSeats(valueList, precision) {\n  var sum = zrUtil.reduce(valueList, function (acc, val) {\n    return acc + (isNaN(val) ? 0 : val);\n  }, 0);\n  if (sum === 0) {\n    return [];\n  }\n  var digits = Math.pow(10, precision);\n  var votesPerQuota = zrUtil.map(valueList, function (val) {\n    return (isNaN(val) ? 0 : val) / sum * digits * 100;\n  });\n  var targetSeats = digits * 100;\n  var seats = zrUtil.map(votesPerQuota, function (votes) {\n    // Assign automatic seats.\n    return Math.floor(votes);\n  });\n  var currentSum = zrUtil.reduce(seats, function (acc, val) {\n    return acc + val;\n  }, 0);\n  var remainder = zrUtil.map(votesPerQuota, function (votes, idx) {\n    return votes - seats[idx];\n  });\n  // Has remainding votes.\n  while (currentSum < targetSeats) {\n    // Find next largest remainder.\n    var max = Number.NEGATIVE_INFINITY;\n    var maxId = null;\n    for (var i = 0, len = remainder.length; i < len; ++i) {\n      if (remainder[i] > max) {\n        max = remainder[i];\n        maxId = i;\n      }\n    }\n    // Add a vote to max remainder.\n    ++seats[maxId];\n    remainder[maxId] = 0;\n    ++currentSum;\n  }\n  return zrUtil.map(seats, function (seat) {\n    return seat / digits;\n  });\n}\n/**\r\n * Solve the floating point adding problem like 0.1 + 0.2 === 0.30000000000000004\r\n * See <http://0.30000000000000004.com/>\r\n */\nexport function addSafe(val0, val1) {\n  var maxPrecision = Math.max(getPrecision(val0), getPrecision(val1));\n  // const multiplier = Math.pow(10, maxPrecision);\n  // return (Math.round(val0 * multiplier) + Math.round(val1 * multiplier)) / multiplier;\n  var sum = val0 + val1;\n  // // PENDING: support more?\n  return maxPrecision > ROUND_SUPPORTED_PRECISION_MAX ? sum : round(sum, maxPrecision);\n}\n// Number.MAX_SAFE_INTEGER, ie do not support.\nexport var MAX_SAFE_INTEGER = 9007199254740991;\n/**\r\n * To 0 - 2 * PI, considering negative radian.\r\n */\nexport function remRadian(radian) {\n  var pi2 = Math.PI * 2;\n  return (radian % pi2 + pi2) % pi2;\n}\n/**\r\n * @param {type} radian\r\n * @return {boolean}\r\n */\nexport function isRadianAroundZero(val) {\n  return val > -RADIAN_EPSILON && val < RADIAN_EPSILON;\n}\n// eslint-disable-next-line\nvar TIME_REG = /^(?:(\\d{4})(?:[-\\/](\\d{1,2})(?:[-\\/](\\d{1,2})(?:[T ](\\d{1,2})(?::(\\d{1,2})(?::(\\d{1,2})(?:[.,](\\d+))?)?)?(Z|[\\+\\-]\\d\\d:?\\d\\d)?)?)?)?)?$/; // jshint ignore:line\n/**\r\n * @param value valid type: number | string | Date, otherwise return `new Date(NaN)`\r\n *   These values can be accepted:\r\n *   + An instance of Date, represent a time in its own time zone.\r\n *   + Or string in a subset of ISO 8601, only including:\r\n *     + only year, month, date: '2012-03', '2012-03-01', '2012-03-01 05', '2012-03-01 05:06',\r\n *     + separated with T or space: '2012-03-01T12:22:33.123', '2012-03-01 12:22:33.123',\r\n *     + time zone: '2012-03-01T12:22:33Z', '2012-03-01T12:22:33+8000', '2012-03-01T12:22:33-05:00',\r\n *     all of which will be treated as local time if time zone is not specified\r\n *     (see <https://momentjs.com/>).\r\n *   + Or other string format, including (all of which will be treated as local time):\r\n *     '2012', '2012-3-1', '2012/3/1', '2012/03/01',\r\n *     '2009/6/12 2:00', '2009/6/12 2:05:08', '2009/6/12 2:05:08.123'\r\n *   + a timestamp, which represent a time in UTC.\r\n * @return date Never be null/undefined. If invalid, return `new Date(NaN)`.\r\n */\nexport function parseDate(value) {\n  if (value instanceof Date) {\n    return value;\n  } else if (zrUtil.isString(value)) {\n    // Different browsers parse date in different way, so we parse it manually.\n    // Some other issues:\n    // new Date('1970-01-01') is UTC,\n    // new Date('1970/01/01') and new Date('1970-1-01') is local.\n    // See issue #3623\n    var match = TIME_REG.exec(value);\n    if (!match) {\n      // return Invalid Date.\n      return new Date(NaN);\n    }\n    // Use local time when no timezone offset is specified.\n    if (!match[8]) {\n      // match[n] can only be string or undefined.\n      // But take care of '12' + 1 => '121'.\n      return new Date(+match[1], +(match[2] || 1) - 1, +match[3] || 1, +match[4] || 0, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0);\n    }\n    // Timezoneoffset of Javascript Date has considered DST (Daylight Saving Time,\n    // https://tc39.github.io/ecma262/#sec-daylight-saving-time-adjustment).\n    // For example, system timezone is set as \"Time Zone: America/Toronto\",\n    // then these code will get different result:\n    // `new Date(1478411999999).getTimezoneOffset();  // get 240`\n    // `new Date(1478412000000).getTimezoneOffset();  // get 300`\n    // So we should not use `new Date`, but use `Date.UTC`.\n    else {\n      var hour = +match[4] || 0;\n      if (match[8].toUpperCase() !== 'Z') {\n        hour -= +match[8].slice(0, 3);\n      }\n      return new Date(Date.UTC(+match[1], +(match[2] || 1) - 1, +match[3] || 1, hour, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0));\n    }\n  } else if (value == null) {\n    return new Date(NaN);\n  }\n  return new Date(Math.round(value));\n}\n/**\r\n * Quantity of a number. e.g. 0.1, 1, 10, 100\r\n *\r\n * @param val\r\n * @return\r\n */\nexport function quantity(val) {\n  return Math.pow(10, quantityExponent(val));\n}\n/**\r\n * Exponent of the quantity of a number\r\n * e.g., 1234 equals to 1.234*10^3, so quantityExponent(1234) is 3\r\n *\r\n * @param val non-negative value\r\n * @return\r\n */\nexport function quantityExponent(val) {\n  if (val === 0) {\n    return 0;\n  }\n  var exp = Math.floor(Math.log(val) / Math.LN10);\n  /**\r\n   * exp is expected to be the rounded-down result of the base-10 log of val.\r\n   * But due to the precision loss with Math.log(val), we need to restore it\r\n   * using 10^exp to make sure we can get val back from exp. #11249\r\n   */\n  if (val / Math.pow(10, exp) >= 10) {\n    exp++;\n  }\n  return exp;\n}\n/**\r\n * find a “nice” number approximately equal to x. Round the number if round = true,\r\n * take ceiling if round = false. The primary observation is that the “nicest”\r\n * numbers in decimal are 1, 2, and 5, and all power-of-ten multiples of these numbers.\r\n *\r\n * See \"Nice Numbers for Graph Labels\" of Graphic Gems.\r\n *\r\n * @param  val Non-negative value.\r\n * @param  round\r\n * @return Niced number\r\n */\nexport function nice(val, round) {\n  var exponent = quantityExponent(val);\n  var exp10 = Math.pow(10, exponent);\n  var f = val / exp10; // 1 <= f < 10\n  var nf;\n  if (round) {\n    if (f < 1.5) {\n      nf = 1;\n    } else if (f < 2.5) {\n      nf = 2;\n    } else if (f < 4) {\n      nf = 3;\n    } else if (f < 7) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  } else {\n    if (f < 1) {\n      nf = 1;\n    } else if (f < 2) {\n      nf = 2;\n    } else if (f < 3) {\n      nf = 3;\n    } else if (f < 5) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  }\n  val = nf * exp10;\n  // Fix 3 * 0.1 === 0.30000000000000004 issue (see IEEE 754).\n  // 20 is the uppper bound of toFixed.\n  return exponent >= -20 ? +val.toFixed(exponent < 0 ? -exponent : 0) : val;\n}\n/**\r\n * This code was copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/9cc9a875e636a1dcf36cc1e07bdf77e1ad6e2c74/src/arrays/quantile.js>.\r\n * See the license statement at the head of this file.\r\n * @param ascArr\r\n */\nexport function quantile(ascArr, p) {\n  var H = (ascArr.length - 1) * p + 1;\n  var h = Math.floor(H);\n  var v = +ascArr[h - 1];\n  var e = H - h;\n  return e ? v + e * (ascArr[h] - v) : v;\n}\n/**\r\n * Order intervals asc, and split them when overlap.\r\n * expect(numberUtil.reformIntervals([\r\n *     {interval: [18, 62], close: [1, 1]},\r\n *     {interval: [-Infinity, -70], close: [0, 0]},\r\n *     {interval: [-70, -26], close: [1, 1]},\r\n *     {interval: [-26, 18], close: [1, 1]},\r\n *     {interval: [62, 150], close: [1, 1]},\r\n *     {interval: [106, 150], close: [1, 1]},\r\n *     {interval: [150, Infinity], close: [0, 0]}\r\n * ])).toEqual([\r\n *     {interval: [-Infinity, -70], close: [0, 0]},\r\n *     {interval: [-70, -26], close: [1, 1]},\r\n *     {interval: [-26, 18], close: [0, 1]},\r\n *     {interval: [18, 62], close: [0, 1]},\r\n *     {interval: [62, 150], close: [0, 1]},\r\n *     {interval: [150, Infinity], close: [0, 0]}\r\n * ]);\r\n * @param list, where `close` mean open or close\r\n *        of the interval, and Infinity can be used.\r\n * @return The origin list, which has been reformed.\r\n */\nexport function reformIntervals(list) {\n  list.sort(function (a, b) {\n    return littleThan(a, b, 0) ? -1 : 1;\n  });\n  var curr = -Infinity;\n  var currClose = 1;\n  for (var i = 0; i < list.length;) {\n    var interval = list[i].interval;\n    var close_1 = list[i].close;\n    for (var lg = 0; lg < 2; lg++) {\n      if (interval[lg] <= curr) {\n        interval[lg] = curr;\n        close_1[lg] = !lg ? 1 - currClose : 1;\n      }\n      curr = interval[lg];\n      currClose = close_1[lg];\n    }\n    if (interval[0] === interval[1] && close_1[0] * close_1[1] !== 1) {\n      list.splice(i, 1);\n    } else {\n      i++;\n    }\n  }\n  return list;\n  function littleThan(a, b, lg) {\n    return a.interval[lg] < b.interval[lg] || a.interval[lg] === b.interval[lg] && (a.close[lg] - b.close[lg] === (!lg ? 1 : -1) || !lg && littleThan(a, b, 1));\n  }\n}\n/**\r\n * [Numeric is defined as]:\r\n *     `parseFloat(val) == val`\r\n * For example:\r\n * numeric:\r\n *     typeof number except NaN, '-123', '123', '2e3', '-2e3', '011', 'Infinity', Infinity,\r\n *     and they rounded by white-spaces or line-terminal like ' -123 \\n ' (see es spec)\r\n * not-numeric:\r\n *     null, undefined, [], {}, true, false, 'NaN', NaN, '123ab',\r\n *     empty string, string with only white-spaces or line-terminal (see es spec),\r\n *     0x12, '0x12', '-0x12', 012, '012', '-012',\r\n *     non-string, ...\r\n *\r\n * @test See full test cases in `test/ut/spec/util/number.js`.\r\n * @return Must be a typeof number. If not numeric, return NaN.\r\n */\nexport function numericToNumber(val) {\n  var valFloat = parseFloat(val);\n  return valFloat == val // eslint-disable-line eqeqeq\n  && (valFloat !== 0 || !zrUtil.isString(val) || val.indexOf('x') <= 0) // For case ' 0x0 '.\n  ? valFloat : NaN;\n}\n/**\r\n * Definition of \"numeric\": see `numericToNumber`.\r\n */\nexport function isNumeric(val) {\n  return !isNaN(numericToNumber(val));\n}\n/**\r\n * Use random base to prevent users hard code depending on\r\n * this auto generated marker id.\r\n * @return An positive integer.\r\n */\nexport function getRandomIdBase() {\n  return Math.round(Math.random() * 9);\n}\n/**\r\n * Get the greatest common divisor.\r\n *\r\n * @param {number} a one number\r\n * @param {number} b the other number\r\n */\nexport function getGreatestCommonDividor(a, b) {\n  if (b === 0) {\n    return a;\n  }\n  return getGreatestCommonDividor(b, a % b);\n}\n/**\r\n * Get the least common multiple.\r\n *\r\n * @param {number} a one number\r\n * @param {number} b the other number\r\n */\nexport function getLeastCommonMultiple(a, b) {\n  if (a == null) {\n    return b;\n  }\n  if (b == null) {\n    return a;\n  }\n  return a * b / getGreatestCommonDividor(a, b);\n}", "map": {"version": 3, "names": ["zrUtil", "RADIAN_EPSILON", "ROUND_SUPPORTED_PRECISION_MAX", "_trim", "str", "replace", "linearMap", "val", "domain", "range", "clamp", "d0", "d1", "r0", "r1", "subDomain", "subRange", "parsePercent", "percent", "all", "isString", "match", "parseFloat", "NaN", "round", "x", "precision", "returnStr", "Math", "min", "max", "toFixed", "asc", "arr", "sort", "a", "b", "getPrecision", "isNaN", "e", "i", "getPrecisionSafe", "toString", "toLowerCase", "eIndex", "indexOf", "exp", "slice", "significandPartLen", "length", "dotIndex", "decimalPartLen", "getPixelPrecision", "dataExtent", "pixelExtent", "log", "LN10", "dataQuantity", "floor", "sizeQuantity", "abs", "isFinite", "getPercentWithPrecision", "valueList", "idx", "seats", "getPercentSeats", "sum", "reduce", "acc", "digits", "pow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "targetSeats", "votes", "currentSum", "remainder", "Number", "NEGATIVE_INFINITY", "maxId", "len", "seat", "addSafe", "val0", "val1", "maxPrecision", "MAX_SAFE_INTEGER", "remRadian", "radian", "pi2", "PI", "isRadianAroundZero", "TIME_REG", "parseDate", "value", "Date", "exec", "substring", "hour", "toUpperCase", "UTC", "quantity", "quantityExponent", "nice", "exponent", "exp10", "f", "nf", "quantile", "ascArr", "p", "H", "h", "v", "reformIntervals", "list", "<PERSON><PERSON><PERSON>", "curr", "Infinity", "currClose", "interval", "close_1", "close", "lg", "splice", "numericToNumber", "valFloat", "isNumeric", "getRandomIdBase", "random", "getGreatestCommonDividor", "getLeastCommonMultiple"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/util/number.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The method \"quantile\" was copied from \"d3.js\".\r\n* (See more details in the comment of the method below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar RADIAN_EPSILON = 1e-4;\n// Although chrome already enlarge this number to 100 for `toFixed`, but\n// we sill follow the spec for compatibility.\nvar ROUND_SUPPORTED_PRECISION_MAX = 20;\nfunction _trim(str) {\n  return str.replace(/^\\s+|\\s+$/g, '');\n}\n/**\r\n * Linear mapping a value from domain to range\r\n * @param  val\r\n * @param  domain Domain extent domain[0] can be bigger than domain[1]\r\n * @param  range  Range extent range[0] can be bigger than range[1]\r\n * @param  clamp Default to be false\r\n */\nexport function linearMap(val, domain, range, clamp) {\n  var d0 = domain[0];\n  var d1 = domain[1];\n  var r0 = range[0];\n  var r1 = range[1];\n  var subDomain = d1 - d0;\n  var subRange = r1 - r0;\n  if (subDomain === 0) {\n    return subRange === 0 ? r0 : (r0 + r1) / 2;\n  }\n  // Avoid accuracy problem in edge, such as\n  // 146.39 - 62.83 === 83.55999999999999.\n  // See echarts/test/ut/spec/util/number.js#linearMap#accuracyError\n  // It is a little verbose for efficiency considering this method\n  // is a hotspot.\n  if (clamp) {\n    if (subDomain > 0) {\n      if (val <= d0) {\n        return r0;\n      } else if (val >= d1) {\n        return r1;\n      }\n    } else {\n      if (val >= d0) {\n        return r0;\n      } else if (val <= d1) {\n        return r1;\n      }\n    }\n  } else {\n    if (val === d0) {\n      return r0;\n    }\n    if (val === d1) {\n      return r1;\n    }\n  }\n  return (val - d0) / subDomain * subRange + r0;\n}\n/**\r\n * Convert a percent string to absolute number.\r\n * Returns NaN if percent is not a valid string or number\r\n */\nexport function parsePercent(percent, all) {\n  switch (percent) {\n    case 'center':\n    case 'middle':\n      percent = '50%';\n      break;\n    case 'left':\n    case 'top':\n      percent = '0%';\n      break;\n    case 'right':\n    case 'bottom':\n      percent = '100%';\n      break;\n  }\n  if (zrUtil.isString(percent)) {\n    if (_trim(percent).match(/%$/)) {\n      return parseFloat(percent) / 100 * all;\n    }\n    return parseFloat(percent);\n  }\n  return percent == null ? NaN : +percent;\n}\nexport function round(x, precision, returnStr) {\n  if (precision == null) {\n    precision = 10;\n  }\n  // Avoid range error\n  precision = Math.min(Math.max(0, precision), ROUND_SUPPORTED_PRECISION_MAX);\n  // PENDING: 1.005.toFixed(2) is '1.00' rather than '1.01'\n  x = (+x).toFixed(precision);\n  return returnStr ? x : +x;\n}\n/**\r\n * Inplacd asc sort arr.\r\n * The input arr will be modified.\r\n */\nexport function asc(arr) {\n  arr.sort(function (a, b) {\n    return a - b;\n  });\n  return arr;\n}\n/**\r\n * Get precision.\r\n */\nexport function getPrecision(val) {\n  val = +val;\n  if (isNaN(val)) {\n    return 0;\n  }\n  // It is much faster than methods converting number to string as follows\n  //      let tmp = val.toString();\n  //      return tmp.length - 1 - tmp.indexOf('.');\n  // especially when precision is low\n  // Notice:\n  // (1) If the loop count is over about 20, it is slower than `getPrecisionSafe`.\n  //     (see https://jsbench.me/2vkpcekkvw/1)\n  // (2) If the val is less than for example 1e-15, the result may be incorrect.\n  //     (see test/ut/spec/util/number.test.ts `getPrecision_equal_random`)\n  if (val > 1e-14) {\n    var e = 1;\n    for (var i = 0; i < 15; i++, e *= 10) {\n      if (Math.round(val * e) / e === val) {\n        return i;\n      }\n    }\n  }\n  return getPrecisionSafe(val);\n}\n/**\r\n * Get precision with slow but safe method\r\n */\nexport function getPrecisionSafe(val) {\n  // toLowerCase for: '3.4E-12'\n  var str = val.toString().toLowerCase();\n  // Consider scientific notation: '3.4e-12' '3.4e+12'\n  var eIndex = str.indexOf('e');\n  var exp = eIndex > 0 ? +str.slice(eIndex + 1) : 0;\n  var significandPartLen = eIndex > 0 ? eIndex : str.length;\n  var dotIndex = str.indexOf('.');\n  var decimalPartLen = dotIndex < 0 ? 0 : significandPartLen - 1 - dotIndex;\n  return Math.max(0, decimalPartLen - exp);\n}\n/**\r\n * Minimal dicernible data precisioin according to a single pixel.\r\n */\nexport function getPixelPrecision(dataExtent, pixelExtent) {\n  var log = Math.log;\n  var LN10 = Math.LN10;\n  var dataQuantity = Math.floor(log(dataExtent[1] - dataExtent[0]) / LN10);\n  var sizeQuantity = Math.round(log(Math.abs(pixelExtent[1] - pixelExtent[0])) / LN10);\n  // toFixed() digits argument must be between 0 and 20.\n  var precision = Math.min(Math.max(-dataQuantity + sizeQuantity, 0), 20);\n  return !isFinite(precision) ? 20 : precision;\n}\n/**\r\n * Get a data of given precision, assuring the sum of percentages\r\n * in valueList is 1.\r\n * The largest remainder method is used.\r\n * https://en.wikipedia.org/wiki/Largest_remainder_method\r\n *\r\n * @param valueList a list of all data\r\n * @param idx index of the data to be processed in valueList\r\n * @param precision integer number showing digits of precision\r\n * @return percent ranging from 0 to 100\r\n */\nexport function getPercentWithPrecision(valueList, idx, precision) {\n  if (!valueList[idx]) {\n    return 0;\n  }\n  var seats = getPercentSeats(valueList, precision);\n  return seats[idx] || 0;\n}\n/**\r\n * Get a data of given precision, assuring the sum of percentages\r\n * in valueList is 1.\r\n * The largest remainder method is used.\r\n * https://en.wikipedia.org/wiki/Largest_remainder_method\r\n *\r\n * @param valueList a list of all data\r\n * @param precision integer number showing digits of precision\r\n * @return {Array<number>}\r\n */\nexport function getPercentSeats(valueList, precision) {\n  var sum = zrUtil.reduce(valueList, function (acc, val) {\n    return acc + (isNaN(val) ? 0 : val);\n  }, 0);\n  if (sum === 0) {\n    return [];\n  }\n  var digits = Math.pow(10, precision);\n  var votesPerQuota = zrUtil.map(valueList, function (val) {\n    return (isNaN(val) ? 0 : val) / sum * digits * 100;\n  });\n  var targetSeats = digits * 100;\n  var seats = zrUtil.map(votesPerQuota, function (votes) {\n    // Assign automatic seats.\n    return Math.floor(votes);\n  });\n  var currentSum = zrUtil.reduce(seats, function (acc, val) {\n    return acc + val;\n  }, 0);\n  var remainder = zrUtil.map(votesPerQuota, function (votes, idx) {\n    return votes - seats[idx];\n  });\n  // Has remainding votes.\n  while (currentSum < targetSeats) {\n    // Find next largest remainder.\n    var max = Number.NEGATIVE_INFINITY;\n    var maxId = null;\n    for (var i = 0, len = remainder.length; i < len; ++i) {\n      if (remainder[i] > max) {\n        max = remainder[i];\n        maxId = i;\n      }\n    }\n    // Add a vote to max remainder.\n    ++seats[maxId];\n    remainder[maxId] = 0;\n    ++currentSum;\n  }\n  return zrUtil.map(seats, function (seat) {\n    return seat / digits;\n  });\n}\n/**\r\n * Solve the floating point adding problem like 0.1 + 0.2 === 0.30000000000000004\r\n * See <http://0.30000000000000004.com/>\r\n */\nexport function addSafe(val0, val1) {\n  var maxPrecision = Math.max(getPrecision(val0), getPrecision(val1));\n  // const multiplier = Math.pow(10, maxPrecision);\n  // return (Math.round(val0 * multiplier) + Math.round(val1 * multiplier)) / multiplier;\n  var sum = val0 + val1;\n  // // PENDING: support more?\n  return maxPrecision > ROUND_SUPPORTED_PRECISION_MAX ? sum : round(sum, maxPrecision);\n}\n// Number.MAX_SAFE_INTEGER, ie do not support.\nexport var MAX_SAFE_INTEGER = 9007199254740991;\n/**\r\n * To 0 - 2 * PI, considering negative radian.\r\n */\nexport function remRadian(radian) {\n  var pi2 = Math.PI * 2;\n  return (radian % pi2 + pi2) % pi2;\n}\n/**\r\n * @param {type} radian\r\n * @return {boolean}\r\n */\nexport function isRadianAroundZero(val) {\n  return val > -RADIAN_EPSILON && val < RADIAN_EPSILON;\n}\n// eslint-disable-next-line\nvar TIME_REG = /^(?:(\\d{4})(?:[-\\/](\\d{1,2})(?:[-\\/](\\d{1,2})(?:[T ](\\d{1,2})(?::(\\d{1,2})(?::(\\d{1,2})(?:[.,](\\d+))?)?)?(Z|[\\+\\-]\\d\\d:?\\d\\d)?)?)?)?)?$/; // jshint ignore:line\n/**\r\n * @param value valid type: number | string | Date, otherwise return `new Date(NaN)`\r\n *   These values can be accepted:\r\n *   + An instance of Date, represent a time in its own time zone.\r\n *   + Or string in a subset of ISO 8601, only including:\r\n *     + only year, month, date: '2012-03', '2012-03-01', '2012-03-01 05', '2012-03-01 05:06',\r\n *     + separated with T or space: '2012-03-01T12:22:33.123', '2012-03-01 12:22:33.123',\r\n *     + time zone: '2012-03-01T12:22:33Z', '2012-03-01T12:22:33+8000', '2012-03-01T12:22:33-05:00',\r\n *     all of which will be treated as local time if time zone is not specified\r\n *     (see <https://momentjs.com/>).\r\n *   + Or other string format, including (all of which will be treated as local time):\r\n *     '2012', '2012-3-1', '2012/3/1', '2012/03/01',\r\n *     '2009/6/12 2:00', '2009/6/12 2:05:08', '2009/6/12 2:05:08.123'\r\n *   + a timestamp, which represent a time in UTC.\r\n * @return date Never be null/undefined. If invalid, return `new Date(NaN)`.\r\n */\nexport function parseDate(value) {\n  if (value instanceof Date) {\n    return value;\n  } else if (zrUtil.isString(value)) {\n    // Different browsers parse date in different way, so we parse it manually.\n    // Some other issues:\n    // new Date('1970-01-01') is UTC,\n    // new Date('1970/01/01') and new Date('1970-1-01') is local.\n    // See issue #3623\n    var match = TIME_REG.exec(value);\n    if (!match) {\n      // return Invalid Date.\n      return new Date(NaN);\n    }\n    // Use local time when no timezone offset is specified.\n    if (!match[8]) {\n      // match[n] can only be string or undefined.\n      // But take care of '12' + 1 => '121'.\n      return new Date(+match[1], +(match[2] || 1) - 1, +match[3] || 1, +match[4] || 0, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0);\n    }\n    // Timezoneoffset of Javascript Date has considered DST (Daylight Saving Time,\n    // https://tc39.github.io/ecma262/#sec-daylight-saving-time-adjustment).\n    // For example, system timezone is set as \"Time Zone: America/Toronto\",\n    // then these code will get different result:\n    // `new Date(1478411999999).getTimezoneOffset();  // get 240`\n    // `new Date(1478412000000).getTimezoneOffset();  // get 300`\n    // So we should not use `new Date`, but use `Date.UTC`.\n    else {\n      var hour = +match[4] || 0;\n      if (match[8].toUpperCase() !== 'Z') {\n        hour -= +match[8].slice(0, 3);\n      }\n      return new Date(Date.UTC(+match[1], +(match[2] || 1) - 1, +match[3] || 1, hour, +(match[5] || 0), +match[6] || 0, match[7] ? +match[7].substring(0, 3) : 0));\n    }\n  } else if (value == null) {\n    return new Date(NaN);\n  }\n  return new Date(Math.round(value));\n}\n/**\r\n * Quantity of a number. e.g. 0.1, 1, 10, 100\r\n *\r\n * @param val\r\n * @return\r\n */\nexport function quantity(val) {\n  return Math.pow(10, quantityExponent(val));\n}\n/**\r\n * Exponent of the quantity of a number\r\n * e.g., 1234 equals to 1.234*10^3, so quantityExponent(1234) is 3\r\n *\r\n * @param val non-negative value\r\n * @return\r\n */\nexport function quantityExponent(val) {\n  if (val === 0) {\n    return 0;\n  }\n  var exp = Math.floor(Math.log(val) / Math.LN10);\n  /**\r\n   * exp is expected to be the rounded-down result of the base-10 log of val.\r\n   * But due to the precision loss with Math.log(val), we need to restore it\r\n   * using 10^exp to make sure we can get val back from exp. #11249\r\n   */\n  if (val / Math.pow(10, exp) >= 10) {\n    exp++;\n  }\n  return exp;\n}\n/**\r\n * find a “nice” number approximately equal to x. Round the number if round = true,\r\n * take ceiling if round = false. The primary observation is that the “nicest”\r\n * numbers in decimal are 1, 2, and 5, and all power-of-ten multiples of these numbers.\r\n *\r\n * See \"Nice Numbers for Graph Labels\" of Graphic Gems.\r\n *\r\n * @param  val Non-negative value.\r\n * @param  round\r\n * @return Niced number\r\n */\nexport function nice(val, round) {\n  var exponent = quantityExponent(val);\n  var exp10 = Math.pow(10, exponent);\n  var f = val / exp10; // 1 <= f < 10\n  var nf;\n  if (round) {\n    if (f < 1.5) {\n      nf = 1;\n    } else if (f < 2.5) {\n      nf = 2;\n    } else if (f < 4) {\n      nf = 3;\n    } else if (f < 7) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  } else {\n    if (f < 1) {\n      nf = 1;\n    } else if (f < 2) {\n      nf = 2;\n    } else if (f < 3) {\n      nf = 3;\n    } else if (f < 5) {\n      nf = 5;\n    } else {\n      nf = 10;\n    }\n  }\n  val = nf * exp10;\n  // Fix 3 * 0.1 === 0.30000000000000004 issue (see IEEE 754).\n  // 20 is the uppper bound of toFixed.\n  return exponent >= -20 ? +val.toFixed(exponent < 0 ? -exponent : 0) : val;\n}\n/**\r\n * This code was copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/9cc9a875e636a1dcf36cc1e07bdf77e1ad6e2c74/src/arrays/quantile.js>.\r\n * See the license statement at the head of this file.\r\n * @param ascArr\r\n */\nexport function quantile(ascArr, p) {\n  var H = (ascArr.length - 1) * p + 1;\n  var h = Math.floor(H);\n  var v = +ascArr[h - 1];\n  var e = H - h;\n  return e ? v + e * (ascArr[h] - v) : v;\n}\n/**\r\n * Order intervals asc, and split them when overlap.\r\n * expect(numberUtil.reformIntervals([\r\n *     {interval: [18, 62], close: [1, 1]},\r\n *     {interval: [-Infinity, -70], close: [0, 0]},\r\n *     {interval: [-70, -26], close: [1, 1]},\r\n *     {interval: [-26, 18], close: [1, 1]},\r\n *     {interval: [62, 150], close: [1, 1]},\r\n *     {interval: [106, 150], close: [1, 1]},\r\n *     {interval: [150, Infinity], close: [0, 0]}\r\n * ])).toEqual([\r\n *     {interval: [-Infinity, -70], close: [0, 0]},\r\n *     {interval: [-70, -26], close: [1, 1]},\r\n *     {interval: [-26, 18], close: [0, 1]},\r\n *     {interval: [18, 62], close: [0, 1]},\r\n *     {interval: [62, 150], close: [0, 1]},\r\n *     {interval: [150, Infinity], close: [0, 0]}\r\n * ]);\r\n * @param list, where `close` mean open or close\r\n *        of the interval, and Infinity can be used.\r\n * @return The origin list, which has been reformed.\r\n */\nexport function reformIntervals(list) {\n  list.sort(function (a, b) {\n    return littleThan(a, b, 0) ? -1 : 1;\n  });\n  var curr = -Infinity;\n  var currClose = 1;\n  for (var i = 0; i < list.length;) {\n    var interval = list[i].interval;\n    var close_1 = list[i].close;\n    for (var lg = 0; lg < 2; lg++) {\n      if (interval[lg] <= curr) {\n        interval[lg] = curr;\n        close_1[lg] = !lg ? 1 - currClose : 1;\n      }\n      curr = interval[lg];\n      currClose = close_1[lg];\n    }\n    if (interval[0] === interval[1] && close_1[0] * close_1[1] !== 1) {\n      list.splice(i, 1);\n    } else {\n      i++;\n    }\n  }\n  return list;\n  function littleThan(a, b, lg) {\n    return a.interval[lg] < b.interval[lg] || a.interval[lg] === b.interval[lg] && (a.close[lg] - b.close[lg] === (!lg ? 1 : -1) || !lg && littleThan(a, b, 1));\n  }\n}\n/**\r\n * [Numeric is defined as]:\r\n *     `parseFloat(val) == val`\r\n * For example:\r\n * numeric:\r\n *     typeof number except NaN, '-123', '123', '2e3', '-2e3', '011', 'Infinity', Infinity,\r\n *     and they rounded by white-spaces or line-terminal like ' -123 \\n ' (see es spec)\r\n * not-numeric:\r\n *     null, undefined, [], {}, true, false, 'NaN', NaN, '123ab',\r\n *     empty string, string with only white-spaces or line-terminal (see es spec),\r\n *     0x12, '0x12', '-0x12', 012, '012', '-012',\r\n *     non-string, ...\r\n *\r\n * @test See full test cases in `test/ut/spec/util/number.js`.\r\n * @return Must be a typeof number. If not numeric, return NaN.\r\n */\nexport function numericToNumber(val) {\n  var valFloat = parseFloat(val);\n  return valFloat == val // eslint-disable-line eqeqeq\n  && (valFloat !== 0 || !zrUtil.isString(val) || val.indexOf('x') <= 0) // For case ' 0x0 '.\n  ? valFloat : NaN;\n}\n/**\r\n * Definition of \"numeric\": see `numericToNumber`.\r\n */\nexport function isNumeric(val) {\n  return !isNaN(numericToNumber(val));\n}\n/**\r\n * Use random base to prevent users hard code depending on\r\n * this auto generated marker id.\r\n * @return An positive integer.\r\n */\nexport function getRandomIdBase() {\n  return Math.round(Math.random() * 9);\n}\n/**\r\n * Get the greatest common divisor.\r\n *\r\n * @param {number} a one number\r\n * @param {number} b the other number\r\n */\nexport function getGreatestCommonDividor(a, b) {\n  if (b === 0) {\n    return a;\n  }\n  return getGreatestCommonDividor(b, a % b);\n}\n/**\r\n * Get the least common multiple.\r\n *\r\n * @param {number} a one number\r\n * @param {number} b the other number\r\n */\nexport function getLeastCommonMultiple(a, b) {\n  if (a == null) {\n    return b;\n  }\n  if (b == null) {\n    return a;\n  }\n  return a * b / getGreatestCommonDividor(a, b);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,cAAc,GAAG,IAAI;AACzB;AACA;AACA,IAAIC,6BAA6B,GAAG,EAAE;AACtC,SAASC,KAAKA,CAACC,GAAG,EAAE;EAClB,OAAOA,GAAG,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACnD,IAAIC,EAAE,GAAGH,MAAM,CAAC,CAAC,CAAC;EAClB,IAAII,EAAE,GAAGJ,MAAM,CAAC,CAAC,CAAC;EAClB,IAAIK,EAAE,GAAGJ,KAAK,CAAC,CAAC,CAAC;EACjB,IAAIK,EAAE,GAAGL,KAAK,CAAC,CAAC,CAAC;EACjB,IAAIM,SAAS,GAAGH,EAAE,GAAGD,EAAE;EACvB,IAAIK,QAAQ,GAAGF,EAAE,GAAGD,EAAE;EACtB,IAAIE,SAAS,KAAK,CAAC,EAAE;IACnB,OAAOC,QAAQ,KAAK,CAAC,GAAGH,EAAE,GAAG,CAACA,EAAE,GAAGC,EAAE,IAAI,CAAC;EAC5C;EACA;EACA;EACA;EACA;EACA;EACA,IAAIJ,KAAK,EAAE;IACT,IAAIK,SAAS,GAAG,CAAC,EAAE;MACjB,IAAIR,GAAG,IAAII,EAAE,EAAE;QACb,OAAOE,EAAE;MACX,CAAC,MAAM,IAAIN,GAAG,IAAIK,EAAE,EAAE;QACpB,OAAOE,EAAE;MACX;IACF,CAAC,MAAM;MACL,IAAIP,GAAG,IAAII,EAAE,EAAE;QACb,OAAOE,EAAE;MACX,CAAC,MAAM,IAAIN,GAAG,IAAIK,EAAE,EAAE;QACpB,OAAOE,EAAE;MACX;IACF;EACF,CAAC,MAAM;IACL,IAAIP,GAAG,KAAKI,EAAE,EAAE;MACd,OAAOE,EAAE;IACX;IACA,IAAIN,GAAG,KAAKK,EAAE,EAAE;MACd,OAAOE,EAAE;IACX;EACF;EACA,OAAO,CAACP,GAAG,GAAGI,EAAE,IAAII,SAAS,GAAGC,QAAQ,GAAGH,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,YAAYA,CAACC,OAAO,EAAEC,GAAG,EAAE;EACzC,QAAQD,OAAO;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;MACXA,OAAO,GAAG,KAAK;MACf;IACF,KAAK,MAAM;IACX,KAAK,KAAK;MACRA,OAAO,GAAG,IAAI;MACd;IACF,KAAK,OAAO;IACZ,KAAK,QAAQ;MACXA,OAAO,GAAG,MAAM;MAChB;EACJ;EACA,IAAIlB,MAAM,CAACoB,QAAQ,CAACF,OAAO,CAAC,EAAE;IAC5B,IAAIf,KAAK,CAACe,OAAO,CAAC,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;MAC9B,OAAOC,UAAU,CAACJ,OAAO,CAAC,GAAG,GAAG,GAAGC,GAAG;IACxC;IACA,OAAOG,UAAU,CAACJ,OAAO,CAAC;EAC5B;EACA,OAAOA,OAAO,IAAI,IAAI,GAAGK,GAAG,GAAG,CAACL,OAAO;AACzC;AACA,OAAO,SAASM,KAAKA,CAACC,CAAC,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAC7C,IAAID,SAAS,IAAI,IAAI,EAAE;IACrBA,SAAS,GAAG,EAAE;EAChB;EACA;EACAA,SAAS,GAAGE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,SAAS,CAAC,EAAExB,6BAA6B,CAAC;EAC3E;EACAuB,CAAC,GAAG,CAAC,CAACA,CAAC,EAAEM,OAAO,CAACL,SAAS,CAAC;EAC3B,OAAOC,SAAS,GAAGF,CAAC,GAAG,CAACA,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,GAAGA,CAACC,GAAG,EAAE;EACvBA,GAAG,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACvB,OAAOD,CAAC,GAAGC,CAAC;EACd,CAAC,CAAC;EACF,OAAOH,GAAG;AACZ;AACA;AACA;AACA;AACA,OAAO,SAASI,YAAYA,CAAC9B,GAAG,EAAE;EAChCA,GAAG,GAAG,CAACA,GAAG;EACV,IAAI+B,KAAK,CAAC/B,GAAG,CAAC,EAAE;IACd,OAAO,CAAC;EACV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIA,GAAG,GAAG,KAAK,EAAE;IACf,IAAIgC,CAAC,GAAG,CAAC;IACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,CAAC,IAAI,EAAE,EAAE;MACpC,IAAIX,IAAI,CAACJ,KAAK,CAACjB,GAAG,GAAGgC,CAAC,CAAC,GAAGA,CAAC,KAAKhC,GAAG,EAAE;QACnC,OAAOiC,CAAC;MACV;IACF;EACF;EACA,OAAOC,gBAAgB,CAAClC,GAAG,CAAC;AAC9B;AACA;AACA;AACA;AACA,OAAO,SAASkC,gBAAgBA,CAAClC,GAAG,EAAE;EACpC;EACA,IAAIH,GAAG,GAAGG,GAAG,CAACmC,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACtC;EACA,IAAIC,MAAM,GAAGxC,GAAG,CAACyC,OAAO,CAAC,GAAG,CAAC;EAC7B,IAAIC,GAAG,GAAGF,MAAM,GAAG,CAAC,GAAG,CAACxC,GAAG,CAAC2C,KAAK,CAACH,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;EACjD,IAAII,kBAAkB,GAAGJ,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAGxC,GAAG,CAAC6C,MAAM;EACzD,IAAIC,QAAQ,GAAG9C,GAAG,CAACyC,OAAO,CAAC,GAAG,CAAC;EAC/B,IAAIM,cAAc,GAAGD,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGF,kBAAkB,GAAG,CAAC,GAAGE,QAAQ;EACzE,OAAOtB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEqB,cAAc,GAAGL,GAAG,CAAC;AAC1C;AACA;AACA;AACA;AACA,OAAO,SAASM,iBAAiBA,CAACC,UAAU,EAAEC,WAAW,EAAE;EACzD,IAAIC,GAAG,GAAG3B,IAAI,CAAC2B,GAAG;EAClB,IAAIC,IAAI,GAAG5B,IAAI,CAAC4B,IAAI;EACpB,IAAIC,YAAY,GAAG7B,IAAI,CAAC8B,KAAK,CAACH,GAAG,CAACF,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGG,IAAI,CAAC;EACxE,IAAIG,YAAY,GAAG/B,IAAI,CAACJ,KAAK,CAAC+B,GAAG,CAAC3B,IAAI,CAACgC,GAAG,CAACN,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGE,IAAI,CAAC;EACpF;EACA,IAAI9B,SAAS,GAAGE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAAC2B,YAAY,GAAGE,YAAY,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACvE,OAAO,CAACE,QAAQ,CAACnC,SAAS,CAAC,GAAG,EAAE,GAAGA,SAAS;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoC,uBAAuBA,CAACC,SAAS,EAAEC,GAAG,EAAEtC,SAAS,EAAE;EACjE,IAAI,CAACqC,SAAS,CAACC,GAAG,CAAC,EAAE;IACnB,OAAO,CAAC;EACV;EACA,IAAIC,KAAK,GAAGC,eAAe,CAACH,SAAS,EAAErC,SAAS,CAAC;EACjD,OAAOuC,KAAK,CAACD,GAAG,CAAC,IAAI,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,eAAeA,CAACH,SAAS,EAAErC,SAAS,EAAE;EACpD,IAAIyC,GAAG,GAAGnE,MAAM,CAACoE,MAAM,CAACL,SAAS,EAAE,UAAUM,GAAG,EAAE9D,GAAG,EAAE;IACrD,OAAO8D,GAAG,IAAI/B,KAAK,CAAC/B,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG,CAAC;EACrC,CAAC,EAAE,CAAC,CAAC;EACL,IAAI4D,GAAG,KAAK,CAAC,EAAE;IACb,OAAO,EAAE;EACX;EACA,IAAIG,MAAM,GAAG1C,IAAI,CAAC2C,GAAG,CAAC,EAAE,EAAE7C,SAAS,CAAC;EACpC,IAAI8C,aAAa,GAAGxE,MAAM,CAACyE,GAAG,CAACV,SAAS,EAAE,UAAUxD,GAAG,EAAE;IACvD,OAAO,CAAC+B,KAAK,CAAC/B,GAAG,CAAC,GAAG,CAAC,GAAGA,GAAG,IAAI4D,GAAG,GAAGG,MAAM,GAAG,GAAG;EACpD,CAAC,CAAC;EACF,IAAII,WAAW,GAAGJ,MAAM,GAAG,GAAG;EAC9B,IAAIL,KAAK,GAAGjE,MAAM,CAACyE,GAAG,CAACD,aAAa,EAAE,UAAUG,KAAK,EAAE;IACrD;IACA,OAAO/C,IAAI,CAAC8B,KAAK,CAACiB,KAAK,CAAC;EAC1B,CAAC,CAAC;EACF,IAAIC,UAAU,GAAG5E,MAAM,CAACoE,MAAM,CAACH,KAAK,EAAE,UAAUI,GAAG,EAAE9D,GAAG,EAAE;IACxD,OAAO8D,GAAG,GAAG9D,GAAG;EAClB,CAAC,EAAE,CAAC,CAAC;EACL,IAAIsE,SAAS,GAAG7E,MAAM,CAACyE,GAAG,CAACD,aAAa,EAAE,UAAUG,KAAK,EAAEX,GAAG,EAAE;IAC9D,OAAOW,KAAK,GAAGV,KAAK,CAACD,GAAG,CAAC;EAC3B,CAAC,CAAC;EACF;EACA,OAAOY,UAAU,GAAGF,WAAW,EAAE;IAC/B;IACA,IAAI5C,GAAG,GAAGgD,MAAM,CAACC,iBAAiB;IAClC,IAAIC,KAAK,GAAG,IAAI;IAChB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEyC,GAAG,GAAGJ,SAAS,CAAC5B,MAAM,EAAET,CAAC,GAAGyC,GAAG,EAAE,EAAEzC,CAAC,EAAE;MACpD,IAAIqC,SAAS,CAACrC,CAAC,CAAC,GAAGV,GAAG,EAAE;QACtBA,GAAG,GAAG+C,SAAS,CAACrC,CAAC,CAAC;QAClBwC,KAAK,GAAGxC,CAAC;MACX;IACF;IACA;IACA,EAAEyB,KAAK,CAACe,KAAK,CAAC;IACdH,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC;IACpB,EAAEJ,UAAU;EACd;EACA,OAAO5E,MAAM,CAACyE,GAAG,CAACR,KAAK,EAAE,UAAUiB,IAAI,EAAE;IACvC,OAAOA,IAAI,GAAGZ,MAAM;EACtB,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,SAASa,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAClC,IAAIC,YAAY,GAAG1D,IAAI,CAACE,GAAG,CAACO,YAAY,CAAC+C,IAAI,CAAC,EAAE/C,YAAY,CAACgD,IAAI,CAAC,CAAC;EACnE;EACA;EACA,IAAIlB,GAAG,GAAGiB,IAAI,GAAGC,IAAI;EACrB;EACA,OAAOC,YAAY,GAAGpF,6BAA6B,GAAGiE,GAAG,GAAG3C,KAAK,CAAC2C,GAAG,EAAEmB,YAAY,CAAC;AACtF;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAG,gBAAgB;AAC9C;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAChC,IAAIC,GAAG,GAAG9D,IAAI,CAAC+D,EAAE,GAAG,CAAC;EACrB,OAAO,CAACF,MAAM,GAAGC,GAAG,GAAGA,GAAG,IAAIA,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,kBAAkBA,CAACrF,GAAG,EAAE;EACtC,OAAOA,GAAG,GAAG,CAACN,cAAc,IAAIM,GAAG,GAAGN,cAAc;AACtD;AACA;AACA,IAAI4F,QAAQ,GAAG,yIAAyI,CAAC,CAAC;AAC1J;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,IAAIA,KAAK,YAAYC,IAAI,EAAE;IACzB,OAAOD,KAAK;EACd,CAAC,MAAM,IAAI/F,MAAM,CAACoB,QAAQ,CAAC2E,KAAK,CAAC,EAAE;IACjC;IACA;IACA;IACA;IACA;IACA,IAAI1E,KAAK,GAAGwE,QAAQ,CAACI,IAAI,CAACF,KAAK,CAAC;IAChC,IAAI,CAAC1E,KAAK,EAAE;MACV;MACA,OAAO,IAAI2E,IAAI,CAACzE,GAAG,CAAC;IACtB;IACA;IACA,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,EAAE;MACb;MACA;MACA,OAAO,IAAI2E,IAAI,CAAC,CAAC3E,KAAK,CAAC,CAAC,CAAC,EAAE,EAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAG,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC6E,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9J;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,KACK;MACH,IAAIC,IAAI,GAAG,CAAC9E,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;MACzB,IAAIA,KAAK,CAAC,CAAC,CAAC,CAAC+E,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QAClCD,IAAI,IAAI,CAAC9E,KAAK,CAAC,CAAC,CAAC,CAAC0B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAC/B;MACA,OAAO,IAAIiD,IAAI,CAACA,IAAI,CAACK,GAAG,CAAC,CAAChF,KAAK,CAAC,CAAC,CAAC,EAAE,EAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE8E,IAAI,EAAE,EAAE9E,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAG,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC6E,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9J;EACF,CAAC,MAAM,IAAIH,KAAK,IAAI,IAAI,EAAE;IACxB,OAAO,IAAIC,IAAI,CAACzE,GAAG,CAAC;EACtB;EACA,OAAO,IAAIyE,IAAI,CAACpE,IAAI,CAACJ,KAAK,CAACuE,KAAK,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASO,QAAQA,CAAC/F,GAAG,EAAE;EAC5B,OAAOqB,IAAI,CAAC2C,GAAG,CAAC,EAAE,EAAEgC,gBAAgB,CAAChG,GAAG,CAAC,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgG,gBAAgBA,CAAChG,GAAG,EAAE;EACpC,IAAIA,GAAG,KAAK,CAAC,EAAE;IACb,OAAO,CAAC;EACV;EACA,IAAIuC,GAAG,GAAGlB,IAAI,CAAC8B,KAAK,CAAC9B,IAAI,CAAC2B,GAAG,CAAChD,GAAG,CAAC,GAAGqB,IAAI,CAAC4B,IAAI,CAAC;EAC/C;AACF;AACA;AACA;AACA;EACE,IAAIjD,GAAG,GAAGqB,IAAI,CAAC2C,GAAG,CAAC,EAAE,EAAEzB,GAAG,CAAC,IAAI,EAAE,EAAE;IACjCA,GAAG,EAAE;EACP;EACA,OAAOA,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0D,IAAIA,CAACjG,GAAG,EAAEiB,KAAK,EAAE;EAC/B,IAAIiF,QAAQ,GAAGF,gBAAgB,CAAChG,GAAG,CAAC;EACpC,IAAImG,KAAK,GAAG9E,IAAI,CAAC2C,GAAG,CAAC,EAAE,EAAEkC,QAAQ,CAAC;EAClC,IAAIE,CAAC,GAAGpG,GAAG,GAAGmG,KAAK,CAAC,CAAC;EACrB,IAAIE,EAAE;EACN,IAAIpF,KAAK,EAAE;IACT,IAAImF,CAAC,GAAG,GAAG,EAAE;MACXC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAID,CAAC,GAAG,GAAG,EAAE;MAClBC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAID,CAAC,GAAG,CAAC,EAAE;MAChBC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAID,CAAC,GAAG,CAAC,EAAE;MAChBC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM;MACLA,EAAE,GAAG,EAAE;IACT;EACF,CAAC,MAAM;IACL,IAAID,CAAC,GAAG,CAAC,EAAE;MACTC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAID,CAAC,GAAG,CAAC,EAAE;MAChBC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAID,CAAC,GAAG,CAAC,EAAE;MAChBC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM,IAAID,CAAC,GAAG,CAAC,EAAE;MAChBC,EAAE,GAAG,CAAC;IACR,CAAC,MAAM;MACLA,EAAE,GAAG,EAAE;IACT;EACF;EACArG,GAAG,GAAGqG,EAAE,GAAGF,KAAK;EAChB;EACA;EACA,OAAOD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAClG,GAAG,CAACwB,OAAO,CAAC0E,QAAQ,GAAG,CAAC,GAAG,CAACA,QAAQ,GAAG,CAAC,CAAC,GAAGlG,GAAG;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsG,QAAQA,CAACC,MAAM,EAAEC,CAAC,EAAE;EAClC,IAAIC,CAAC,GAAG,CAACF,MAAM,CAAC7D,MAAM,GAAG,CAAC,IAAI8D,CAAC,GAAG,CAAC;EACnC,IAAIE,CAAC,GAAGrF,IAAI,CAAC8B,KAAK,CAACsD,CAAC,CAAC;EACrB,IAAIE,CAAC,GAAG,CAACJ,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC;EACtB,IAAI1E,CAAC,GAAGyE,CAAC,GAAGC,CAAC;EACb,OAAO1E,CAAC,GAAG2E,CAAC,GAAG3E,CAAC,IAAIuE,MAAM,CAACG,CAAC,CAAC,GAAGC,CAAC,CAAC,GAAGA,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EACpCA,IAAI,CAAClF,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACxB,OAAOiF,UAAU,CAAClF,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACrC,CAAC,CAAC;EACF,IAAIkF,IAAI,GAAG,CAACC,QAAQ;EACpB,IAAIC,SAAS,GAAG,CAAC;EACjB,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4E,IAAI,CAACnE,MAAM,GAAG;IAChC,IAAIwE,QAAQ,GAAGL,IAAI,CAAC5E,CAAC,CAAC,CAACiF,QAAQ;IAC/B,IAAIC,OAAO,GAAGN,IAAI,CAAC5E,CAAC,CAAC,CAACmF,KAAK;IAC3B,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAE,EAAE;MAC7B,IAAIH,QAAQ,CAACG,EAAE,CAAC,IAAIN,IAAI,EAAE;QACxBG,QAAQ,CAACG,EAAE,CAAC,GAAGN,IAAI;QACnBI,OAAO,CAACE,EAAE,CAAC,GAAG,CAACA,EAAE,GAAG,CAAC,GAAGJ,SAAS,GAAG,CAAC;MACvC;MACAF,IAAI,GAAGG,QAAQ,CAACG,EAAE,CAAC;MACnBJ,SAAS,GAAGE,OAAO,CAACE,EAAE,CAAC;IACzB;IACA,IAAIH,QAAQ,CAAC,CAAC,CAAC,KAAKA,QAAQ,CAAC,CAAC,CAAC,IAAIC,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MAChEN,IAAI,CAACS,MAAM,CAACrF,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC,MAAM;MACLA,CAAC,EAAE;IACL;EACF;EACA,OAAO4E,IAAI;EACX,SAASC,UAAUA,CAAClF,CAAC,EAAEC,CAAC,EAAEwF,EAAE,EAAE;IAC5B,OAAOzF,CAAC,CAACsF,QAAQ,CAACG,EAAE,CAAC,GAAGxF,CAAC,CAACqF,QAAQ,CAACG,EAAE,CAAC,IAAIzF,CAAC,CAACsF,QAAQ,CAACG,EAAE,CAAC,KAAKxF,CAAC,CAACqF,QAAQ,CAACG,EAAE,CAAC,KAAKzF,CAAC,CAACwF,KAAK,CAACC,EAAE,CAAC,GAAGxF,CAAC,CAACuF,KAAK,CAACC,EAAE,CAAC,MAAM,CAACA,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAACA,EAAE,IAAIP,UAAU,CAAClF,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7J;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0F,eAAeA,CAACvH,GAAG,EAAE;EACnC,IAAIwH,QAAQ,GAAGzG,UAAU,CAACf,GAAG,CAAC;EAC9B,OAAOwH,QAAQ,IAAIxH,GAAG,CAAC;EAAA,IACnBwH,QAAQ,KAAK,CAAC,IAAI,CAAC/H,MAAM,CAACoB,QAAQ,CAACb,GAAG,CAAC,IAAIA,GAAG,CAACsC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EAAA,EACpEkF,QAAQ,GAAGxG,GAAG;AAClB;AACA;AACA;AACA;AACA,OAAO,SAASyG,SAASA,CAACzH,GAAG,EAAE;EAC7B,OAAO,CAAC+B,KAAK,CAACwF,eAAe,CAACvH,GAAG,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0H,eAAeA,CAAA,EAAG;EAChC,OAAOrG,IAAI,CAACJ,KAAK,CAACI,IAAI,CAACsG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAAChG,CAAC,EAAEC,CAAC,EAAE;EAC7C,IAAIA,CAAC,KAAK,CAAC,EAAE;IACX,OAAOD,CAAC;EACV;EACA,OAAOgG,wBAAwB,CAAC/F,CAAC,EAAED,CAAC,GAAGC,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgG,sBAAsBA,CAACjG,CAAC,EAAEC,CAAC,EAAE;EAC3C,IAAID,CAAC,IAAI,IAAI,EAAE;IACb,OAAOC,CAAC;EACV;EACA,IAAIA,CAAC,IAAI,IAAI,EAAE;IACb,OAAOD,CAAC;EACV;EACA,OAAOA,CAAC,GAAGC,CAAC,GAAG+F,wBAAwB,CAAChG,CAAC,EAAEC,CAAC,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}