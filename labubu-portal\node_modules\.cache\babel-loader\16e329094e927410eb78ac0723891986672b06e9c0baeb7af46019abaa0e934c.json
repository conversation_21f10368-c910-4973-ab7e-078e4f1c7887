{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as numberUtil from '../util/number.js';\nimport * as formatUtil from '../util/format.js';\nimport Scale from './Scale.js';\nimport * as helper from './helper.js';\nvar roundNumber = numberUtil.round;\nvar IntervalScale = /** @class */function (_super) {\n  __extends(IntervalScale, _super);\n  function IntervalScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'interval';\n    // Step is calculated in adjustExtent.\n    _this._interval = 0;\n    _this._intervalPrecision = 2;\n    return _this;\n  }\n  IntervalScale.prototype.parse = function (val) {\n    return val;\n  };\n  IntervalScale.prototype.contain = function (val) {\n    return helper.contain(val, this._extent);\n  };\n  IntervalScale.prototype.normalize = function (val) {\n    return helper.normalize(val, this._extent);\n  };\n  IntervalScale.prototype.scale = function (val) {\n    return helper.scale(val, this._extent);\n  };\n  IntervalScale.prototype.setExtent = function (start, end) {\n    var thisExtent = this._extent;\n    // start,end may be a Number like '25',so...\n    if (!isNaN(start)) {\n      thisExtent[0] = parseFloat(start);\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = parseFloat(end);\n    }\n  };\n  IntervalScale.prototype.unionExtent = function (other) {\n    var extent = this._extent;\n    other[0] < extent[0] && (extent[0] = other[0]);\n    other[1] > extent[1] && (extent[1] = other[1]);\n    // unionExtent may called by it's sub classes\n    this.setExtent(extent[0], extent[1]);\n  };\n  IntervalScale.prototype.getInterval = function () {\n    return this._interval;\n  };\n  IntervalScale.prototype.setInterval = function (interval) {\n    this._interval = interval;\n    // Dropped auto calculated niceExtent and use user-set extent.\n    // We assume user wants to set both interval, min, max to get a better result.\n    this._niceExtent = this._extent.slice();\n    this._intervalPrecision = helper.getIntervalPrecision(interval);\n  };\n  /**\r\n   * @param expandToNicedExtent Whether expand the ticks to niced extent.\r\n   */\n  IntervalScale.prototype.getTicks = function (expandToNicedExtent) {\n    var interval = this._interval;\n    var extent = this._extent;\n    var niceTickExtent = this._niceExtent;\n    var intervalPrecision = this._intervalPrecision;\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    // Consider this case: using dataZoom toolbox, zoom and zoom.\n    var safeLimit = 10000;\n    if (extent[0] < niceTickExtent[0]) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(niceTickExtent[0] - interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[0]\n        });\n      }\n    }\n    var tick = niceTickExtent[0];\n    while (tick <= niceTickExtent[1]) {\n      ticks.push({\n        value: tick\n      });\n      // Avoid rounding error\n      tick = roundNumber(tick + interval, intervalPrecision);\n      if (tick === ticks[ticks.length - 1].value) {\n        // Consider out of safe float point, e.g.,\n        // -3711126.9907707 + 2e-10 === -3711126.9907707\n        break;\n      }\n      if (ticks.length > safeLimit) {\n        return [];\n      }\n    }\n    // Consider this case: the last item of ticks is smaller\n    // than niceTickExtent[1] and niceTickExtent[1] === extent[1].\n    var lastNiceTick = ticks.length ? ticks[ticks.length - 1].value : niceTickExtent[1];\n    if (extent[1] > lastNiceTick) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(lastNiceTick + interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[1]\n        });\n      }\n    }\n    return ticks;\n  };\n  IntervalScale.prototype.getMinorTicks = function (splitNumber) {\n    var ticks = this.getTicks(true);\n    var minorTicks = [];\n    var extent = this.getExtent();\n    for (var i = 1; i < ticks.length; i++) {\n      var nextTick = ticks[i];\n      var prevTick = ticks[i - 1];\n      var count = 0;\n      var minorTicksGroup = [];\n      var interval = nextTick.value - prevTick.value;\n      var minorInterval = interval / splitNumber;\n      while (count < splitNumber - 1) {\n        var minorTick = roundNumber(prevTick.value + (count + 1) * minorInterval);\n        // For the first and last interval. The count may be less than splitNumber.\n        if (minorTick > extent[0] && minorTick < extent[1]) {\n          minorTicksGroup.push(minorTick);\n        }\n        count++;\n      }\n      minorTicks.push(minorTicksGroup);\n    }\n    return minorTicks;\n  };\n  /**\r\n   * @param opt.precision If 'auto', use nice presision.\r\n   * @param opt.pad returns 1.50 but not 1.5 if precision is 2.\r\n   */\n  IntervalScale.prototype.getLabel = function (data, opt) {\n    if (data == null) {\n      return '';\n    }\n    var precision = opt && opt.precision;\n    if (precision == null) {\n      precision = numberUtil.getPrecision(data.value) || 0;\n    } else if (precision === 'auto') {\n      // Should be more precise then tick.\n      precision = this._intervalPrecision;\n    }\n    // (1) If `precision` is set, 12.005 should be display as '12.00500'.\n    // (2) Use roundNumber (toFixed) to avoid scientific notation like '3.5e-7'.\n    var dataNum = roundNumber(data.value, precision, true);\n    return formatUtil.addCommas(dataNum);\n  };\n  /**\r\n   * @param splitNumber By default `5`.\r\n   */\n  IntervalScale.prototype.calcNiceTicks = function (splitNumber, minInterval, maxInterval) {\n    splitNumber = splitNumber || 5;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (!isFinite(span)) {\n      return;\n    }\n    // User may set axis min 0 and data are all negative\n    // FIXME If it needs to reverse ?\n    if (span < 0) {\n      span = -span;\n      extent.reverse();\n    }\n    var result = helper.intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval);\n    this._intervalPrecision = result.intervalPrecision;\n    this._interval = result.interval;\n    this._niceExtent = result.niceTickExtent;\n  };\n  IntervalScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent;\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      if (extent[0] !== 0) {\n        // Expand extent\n        // Note that extents can be both negative. See #13154\n        var expandSize = Math.abs(extent[0]);\n        // In the fowllowing case\n        //      Axis has been fixed max 100\n        //      Plus data are all 100 and axis extent are [100, 100].\n        // Extend to the both side will cause expanded max is larger than fixed max.\n        // So only expand to the smaller side.\n        if (!opt.fixMax) {\n          extent[1] += expandSize / 2;\n          extent[0] -= expandSize / 2;\n        } else {\n          extent[0] -= expandSize / 2;\n        }\n      } else {\n        extent[1] = 1;\n      }\n    }\n    var span = extent[1] - extent[0];\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (!isFinite(span)) {\n      extent[0] = 0;\n      extent[1] = 1;\n    }\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n    // let extent = this._extent;\n    var interval = this._interval;\n    if (!opt.fixMin) {\n      extent[0] = roundNumber(Math.floor(extent[0] / interval) * interval);\n    }\n    if (!opt.fixMax) {\n      extent[1] = roundNumber(Math.ceil(extent[1] / interval) * interval);\n    }\n  };\n  IntervalScale.prototype.setNiceExtent = function (min, max) {\n    this._niceExtent = [min, max];\n  };\n  IntervalScale.type = 'interval';\n  return IntervalScale;\n}(Scale);\nScale.registerClass(IntervalScale);\nexport default IntervalScale;", "map": {"version": 3, "names": ["__extends", "numberUtil", "formatUtil", "Scale", "helper", "roundNumber", "round", "IntervalScale", "_super", "_this", "apply", "arguments", "type", "_interval", "_intervalPrecision", "prototype", "parse", "val", "contain", "_extent", "normalize", "scale", "setExtent", "start", "end", "thisExtent", "isNaN", "parseFloat", "unionExtent", "other", "extent", "getInterval", "setInterval", "interval", "_niceExtent", "slice", "getIntervalPrecision", "getTicks", "expandToNicedExtent", "niceTickExtent", "intervalPrecision", "ticks", "safeLimit", "push", "value", "tick", "length", "lastNiceTick", "getMinorTicks", "splitNumber", "minorTicks", "getExtent", "i", "nextTick", "prevTick", "count", "minorTicksGroup", "minorInterval", "minor<PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "data", "opt", "precision", "getPrecision", "dataNum", "addCommas", "calcNiceTicks", "minInterval", "maxInterval", "span", "isFinite", "reverse", "result", "intervalScaleNiceTicks", "calcNiceExtent", "expandSize", "Math", "abs", "fixMax", "fixMin", "floor", "ceil", "setNiceExtent", "min", "max", "registerClass"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/scale/Interval.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as numberUtil from '../util/number.js';\nimport * as formatUtil from '../util/format.js';\nimport Scale from './Scale.js';\nimport * as helper from './helper.js';\nvar roundNumber = numberUtil.round;\nvar IntervalScale = /** @class */function (_super) {\n  __extends(IntervalScale, _super);\n  function IntervalScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'interval';\n    // Step is calculated in adjustExtent.\n    _this._interval = 0;\n    _this._intervalPrecision = 2;\n    return _this;\n  }\n  IntervalScale.prototype.parse = function (val) {\n    return val;\n  };\n  IntervalScale.prototype.contain = function (val) {\n    return helper.contain(val, this._extent);\n  };\n  IntervalScale.prototype.normalize = function (val) {\n    return helper.normalize(val, this._extent);\n  };\n  IntervalScale.prototype.scale = function (val) {\n    return helper.scale(val, this._extent);\n  };\n  IntervalScale.prototype.setExtent = function (start, end) {\n    var thisExtent = this._extent;\n    // start,end may be a Number like '25',so...\n    if (!isNaN(start)) {\n      thisExtent[0] = parseFloat(start);\n    }\n    if (!isNaN(end)) {\n      thisExtent[1] = parseFloat(end);\n    }\n  };\n  IntervalScale.prototype.unionExtent = function (other) {\n    var extent = this._extent;\n    other[0] < extent[0] && (extent[0] = other[0]);\n    other[1] > extent[1] && (extent[1] = other[1]);\n    // unionExtent may called by it's sub classes\n    this.setExtent(extent[0], extent[1]);\n  };\n  IntervalScale.prototype.getInterval = function () {\n    return this._interval;\n  };\n  IntervalScale.prototype.setInterval = function (interval) {\n    this._interval = interval;\n    // Dropped auto calculated niceExtent and use user-set extent.\n    // We assume user wants to set both interval, min, max to get a better result.\n    this._niceExtent = this._extent.slice();\n    this._intervalPrecision = helper.getIntervalPrecision(interval);\n  };\n  /**\r\n   * @param expandToNicedExtent Whether expand the ticks to niced extent.\r\n   */\n  IntervalScale.prototype.getTicks = function (expandToNicedExtent) {\n    var interval = this._interval;\n    var extent = this._extent;\n    var niceTickExtent = this._niceExtent;\n    var intervalPrecision = this._intervalPrecision;\n    var ticks = [];\n    // If interval is 0, return [];\n    if (!interval) {\n      return ticks;\n    }\n    // Consider this case: using dataZoom toolbox, zoom and zoom.\n    var safeLimit = 10000;\n    if (extent[0] < niceTickExtent[0]) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(niceTickExtent[0] - interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[0]\n        });\n      }\n    }\n    var tick = niceTickExtent[0];\n    while (tick <= niceTickExtent[1]) {\n      ticks.push({\n        value: tick\n      });\n      // Avoid rounding error\n      tick = roundNumber(tick + interval, intervalPrecision);\n      if (tick === ticks[ticks.length - 1].value) {\n        // Consider out of safe float point, e.g.,\n        // -3711126.9907707 + 2e-10 === -3711126.9907707\n        break;\n      }\n      if (ticks.length > safeLimit) {\n        return [];\n      }\n    }\n    // Consider this case: the last item of ticks is smaller\n    // than niceTickExtent[1] and niceTickExtent[1] === extent[1].\n    var lastNiceTick = ticks.length ? ticks[ticks.length - 1].value : niceTickExtent[1];\n    if (extent[1] > lastNiceTick) {\n      if (expandToNicedExtent) {\n        ticks.push({\n          value: roundNumber(lastNiceTick + interval, intervalPrecision)\n        });\n      } else {\n        ticks.push({\n          value: extent[1]\n        });\n      }\n    }\n    return ticks;\n  };\n  IntervalScale.prototype.getMinorTicks = function (splitNumber) {\n    var ticks = this.getTicks(true);\n    var minorTicks = [];\n    var extent = this.getExtent();\n    for (var i = 1; i < ticks.length; i++) {\n      var nextTick = ticks[i];\n      var prevTick = ticks[i - 1];\n      var count = 0;\n      var minorTicksGroup = [];\n      var interval = nextTick.value - prevTick.value;\n      var minorInterval = interval / splitNumber;\n      while (count < splitNumber - 1) {\n        var minorTick = roundNumber(prevTick.value + (count + 1) * minorInterval);\n        // For the first and last interval. The count may be less than splitNumber.\n        if (minorTick > extent[0] && minorTick < extent[1]) {\n          minorTicksGroup.push(minorTick);\n        }\n        count++;\n      }\n      minorTicks.push(minorTicksGroup);\n    }\n    return minorTicks;\n  };\n  /**\r\n   * @param opt.precision If 'auto', use nice presision.\r\n   * @param opt.pad returns 1.50 but not 1.5 if precision is 2.\r\n   */\n  IntervalScale.prototype.getLabel = function (data, opt) {\n    if (data == null) {\n      return '';\n    }\n    var precision = opt && opt.precision;\n    if (precision == null) {\n      precision = numberUtil.getPrecision(data.value) || 0;\n    } else if (precision === 'auto') {\n      // Should be more precise then tick.\n      precision = this._intervalPrecision;\n    }\n    // (1) If `precision` is set, 12.005 should be display as '12.00500'.\n    // (2) Use roundNumber (toFixed) to avoid scientific notation like '3.5e-7'.\n    var dataNum = roundNumber(data.value, precision, true);\n    return formatUtil.addCommas(dataNum);\n  };\n  /**\r\n   * @param splitNumber By default `5`.\r\n   */\n  IntervalScale.prototype.calcNiceTicks = function (splitNumber, minInterval, maxInterval) {\n    splitNumber = splitNumber || 5;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (!isFinite(span)) {\n      return;\n    }\n    // User may set axis min 0 and data are all negative\n    // FIXME If it needs to reverse ?\n    if (span < 0) {\n      span = -span;\n      extent.reverse();\n    }\n    var result = helper.intervalScaleNiceTicks(extent, splitNumber, minInterval, maxInterval);\n    this._intervalPrecision = result.intervalPrecision;\n    this._interval = result.interval;\n    this._niceExtent = result.niceTickExtent;\n  };\n  IntervalScale.prototype.calcNiceExtent = function (opt) {\n    var extent = this._extent;\n    // If extent start and end are same, expand them\n    if (extent[0] === extent[1]) {\n      if (extent[0] !== 0) {\n        // Expand extent\n        // Note that extents can be both negative. See #13154\n        var expandSize = Math.abs(extent[0]);\n        // In the fowllowing case\n        //      Axis has been fixed max 100\n        //      Plus data are all 100 and axis extent are [100, 100].\n        // Extend to the both side will cause expanded max is larger than fixed max.\n        // So only expand to the smaller side.\n        if (!opt.fixMax) {\n          extent[1] += expandSize / 2;\n          extent[0] -= expandSize / 2;\n        } else {\n          extent[0] -= expandSize / 2;\n        }\n      } else {\n        extent[1] = 1;\n      }\n    }\n    var span = extent[1] - extent[0];\n    // If there are no data and extent are [Infinity, -Infinity]\n    if (!isFinite(span)) {\n      extent[0] = 0;\n      extent[1] = 1;\n    }\n    this.calcNiceTicks(opt.splitNumber, opt.minInterval, opt.maxInterval);\n    // let extent = this._extent;\n    var interval = this._interval;\n    if (!opt.fixMin) {\n      extent[0] = roundNumber(Math.floor(extent[0] / interval) * interval);\n    }\n    if (!opt.fixMax) {\n      extent[1] = roundNumber(Math.ceil(extent[1] / interval) * interval);\n    }\n  };\n  IntervalScale.prototype.setNiceExtent = function (min, max) {\n    this._niceExtent = [min, max];\n  };\n  IntervalScale.type = 'interval';\n  return IntervalScale;\n}(Scale);\nScale.registerClass(IntervalScale);\nexport default IntervalScale;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,IAAIC,WAAW,GAAGJ,UAAU,CAACK,KAAK;AAClC,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDR,SAAS,CAACO,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,UAAU;IACvB;IACAH,KAAK,CAACI,SAAS,GAAG,CAAC;IACnBJ,KAAK,CAACK,kBAAkB,GAAG,CAAC;IAC5B,OAAOL,KAAK;EACd;EACAF,aAAa,CAACQ,SAAS,CAACC,KAAK,GAAG,UAAUC,GAAG,EAAE;IAC7C,OAAOA,GAAG;EACZ,CAAC;EACDV,aAAa,CAACQ,SAAS,CAACG,OAAO,GAAG,UAAUD,GAAG,EAAE;IAC/C,OAAOb,MAAM,CAACc,OAAO,CAACD,GAAG,EAAE,IAAI,CAACE,OAAO,CAAC;EAC1C,CAAC;EACDZ,aAAa,CAACQ,SAAS,CAACK,SAAS,GAAG,UAAUH,GAAG,EAAE;IACjD,OAAOb,MAAM,CAACgB,SAAS,CAACH,GAAG,EAAE,IAAI,CAACE,OAAO,CAAC;EAC5C,CAAC;EACDZ,aAAa,CAACQ,SAAS,CAACM,KAAK,GAAG,UAAUJ,GAAG,EAAE;IAC7C,OAAOb,MAAM,CAACiB,KAAK,CAACJ,GAAG,EAAE,IAAI,CAACE,OAAO,CAAC;EACxC,CAAC;EACDZ,aAAa,CAACQ,SAAS,CAACO,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACxD,IAAIC,UAAU,GAAG,IAAI,CAACN,OAAO;IAC7B;IACA,IAAI,CAACO,KAAK,CAACH,KAAK,CAAC,EAAE;MACjBE,UAAU,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACJ,KAAK,CAAC;IACnC;IACA,IAAI,CAACG,KAAK,CAACF,GAAG,CAAC,EAAE;MACfC,UAAU,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACH,GAAG,CAAC;IACjC;EACF,CAAC;EACDjB,aAAa,CAACQ,SAAS,CAACa,WAAW,GAAG,UAAUC,KAAK,EAAE;IACrD,IAAIC,MAAM,GAAG,IAAI,CAACX,OAAO;IACzBU,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9CA,KAAK,CAAC,CAAC,CAAC,GAAGC,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9C;IACA,IAAI,CAACP,SAAS,CAACQ,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC;EACDvB,aAAa,CAACQ,SAAS,CAACgB,WAAW,GAAG,YAAY;IAChD,OAAO,IAAI,CAAClB,SAAS;EACvB,CAAC;EACDN,aAAa,CAACQ,SAAS,CAACiB,WAAW,GAAG,UAAUC,QAAQ,EAAE;IACxD,IAAI,CAACpB,SAAS,GAAGoB,QAAQ;IACzB;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAACf,OAAO,CAACgB,KAAK,CAAC,CAAC;IACvC,IAAI,CAACrB,kBAAkB,GAAGV,MAAM,CAACgC,oBAAoB,CAACH,QAAQ,CAAC;EACjE,CAAC;EACD;AACF;AACA;EACE1B,aAAa,CAACQ,SAAS,CAACsB,QAAQ,GAAG,UAAUC,mBAAmB,EAAE;IAChE,IAAIL,QAAQ,GAAG,IAAI,CAACpB,SAAS;IAC7B,IAAIiB,MAAM,GAAG,IAAI,CAACX,OAAO;IACzB,IAAIoB,cAAc,GAAG,IAAI,CAACL,WAAW;IACrC,IAAIM,iBAAiB,GAAG,IAAI,CAAC1B,kBAAkB;IAC/C,IAAI2B,KAAK,GAAG,EAAE;IACd;IACA,IAAI,CAACR,QAAQ,EAAE;MACb,OAAOQ,KAAK;IACd;IACA;IACA,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIZ,MAAM,CAAC,CAAC,CAAC,GAAGS,cAAc,CAAC,CAAC,CAAC,EAAE;MACjC,IAAID,mBAAmB,EAAE;QACvBG,KAAK,CAACE,IAAI,CAAC;UACTC,KAAK,EAAEvC,WAAW,CAACkC,cAAc,CAAC,CAAC,CAAC,GAAGN,QAAQ,EAAEO,iBAAiB;QACpE,CAAC,CAAC;MACJ,CAAC,MAAM;QACLC,KAAK,CAACE,IAAI,CAAC;UACTC,KAAK,EAAEd,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ;IACF;IACA,IAAIe,IAAI,GAAGN,cAAc,CAAC,CAAC,CAAC;IAC5B,OAAOM,IAAI,IAAIN,cAAc,CAAC,CAAC,CAAC,EAAE;MAChCE,KAAK,CAACE,IAAI,CAAC;QACTC,KAAK,EAAEC;MACT,CAAC,CAAC;MACF;MACAA,IAAI,GAAGxC,WAAW,CAACwC,IAAI,GAAGZ,QAAQ,EAAEO,iBAAiB,CAAC;MACtD,IAAIK,IAAI,KAAKJ,KAAK,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC,CAACF,KAAK,EAAE;QAC1C;QACA;QACA;MACF;MACA,IAAIH,KAAK,CAACK,MAAM,GAAGJ,SAAS,EAAE;QAC5B,OAAO,EAAE;MACX;IACF;IACA;IACA;IACA,IAAIK,YAAY,GAAGN,KAAK,CAACK,MAAM,GAAGL,KAAK,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC,CAACF,KAAK,GAAGL,cAAc,CAAC,CAAC,CAAC;IACnF,IAAIT,MAAM,CAAC,CAAC,CAAC,GAAGiB,YAAY,EAAE;MAC5B,IAAIT,mBAAmB,EAAE;QACvBG,KAAK,CAACE,IAAI,CAAC;UACTC,KAAK,EAAEvC,WAAW,CAAC0C,YAAY,GAAGd,QAAQ,EAAEO,iBAAiB;QAC/D,CAAC,CAAC;MACJ,CAAC,MAAM;QACLC,KAAK,CAACE,IAAI,CAAC;UACTC,KAAK,EAAEd,MAAM,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ;IACF;IACA,OAAOW,KAAK;EACd,CAAC;EACDlC,aAAa,CAACQ,SAAS,CAACiC,aAAa,GAAG,UAAUC,WAAW,EAAE;IAC7D,IAAIR,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAAC,IAAI,CAAC;IAC/B,IAAIa,UAAU,GAAG,EAAE;IACnB,IAAIpB,MAAM,GAAG,IAAI,CAACqB,SAAS,CAAC,CAAC;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,KAAK,CAACK,MAAM,EAAEM,CAAC,EAAE,EAAE;MACrC,IAAIC,QAAQ,GAAGZ,KAAK,CAACW,CAAC,CAAC;MACvB,IAAIE,QAAQ,GAAGb,KAAK,CAACW,CAAC,GAAG,CAAC,CAAC;MAC3B,IAAIG,KAAK,GAAG,CAAC;MACb,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIvB,QAAQ,GAAGoB,QAAQ,CAACT,KAAK,GAAGU,QAAQ,CAACV,KAAK;MAC9C,IAAIa,aAAa,GAAGxB,QAAQ,GAAGgB,WAAW;MAC1C,OAAOM,KAAK,GAAGN,WAAW,GAAG,CAAC,EAAE;QAC9B,IAAIS,SAAS,GAAGrD,WAAW,CAACiD,QAAQ,CAACV,KAAK,GAAG,CAACW,KAAK,GAAG,CAAC,IAAIE,aAAa,CAAC;QACzE;QACA,IAAIC,SAAS,GAAG5B,MAAM,CAAC,CAAC,CAAC,IAAI4B,SAAS,GAAG5B,MAAM,CAAC,CAAC,CAAC,EAAE;UAClD0B,eAAe,CAACb,IAAI,CAACe,SAAS,CAAC;QACjC;QACAH,KAAK,EAAE;MACT;MACAL,UAAU,CAACP,IAAI,CAACa,eAAe,CAAC;IAClC;IACA,OAAON,UAAU;EACnB,CAAC;EACD;AACF;AACA;AACA;EACE3C,aAAa,CAACQ,SAAS,CAAC4C,QAAQ,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACtD,IAAID,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,EAAE;IACX;IACA,IAAIE,SAAS,GAAGD,GAAG,IAAIA,GAAG,CAACC,SAAS;IACpC,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAG7D,UAAU,CAAC8D,YAAY,CAACH,IAAI,CAAChB,KAAK,CAAC,IAAI,CAAC;IACtD,CAAC,MAAM,IAAIkB,SAAS,KAAK,MAAM,EAAE;MAC/B;MACAA,SAAS,GAAG,IAAI,CAAChD,kBAAkB;IACrC;IACA;IACA;IACA,IAAIkD,OAAO,GAAG3D,WAAW,CAACuD,IAAI,CAAChB,KAAK,EAAEkB,SAAS,EAAE,IAAI,CAAC;IACtD,OAAO5D,UAAU,CAAC+D,SAAS,CAACD,OAAO,CAAC;EACtC,CAAC;EACD;AACF;AACA;EACEzD,aAAa,CAACQ,SAAS,CAACmD,aAAa,GAAG,UAAUjB,WAAW,EAAEkB,WAAW,EAAEC,WAAW,EAAE;IACvFnB,WAAW,GAAGA,WAAW,IAAI,CAAC;IAC9B,IAAInB,MAAM,GAAG,IAAI,CAACX,OAAO;IACzB,IAAIkD,IAAI,GAAGvC,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAChC,IAAI,CAACwC,QAAQ,CAACD,IAAI,CAAC,EAAE;MACnB;IACF;IACA;IACA;IACA,IAAIA,IAAI,GAAG,CAAC,EAAE;MACZA,IAAI,GAAG,CAACA,IAAI;MACZvC,MAAM,CAACyC,OAAO,CAAC,CAAC;IAClB;IACA,IAAIC,MAAM,GAAGpE,MAAM,CAACqE,sBAAsB,CAAC3C,MAAM,EAAEmB,WAAW,EAAEkB,WAAW,EAAEC,WAAW,CAAC;IACzF,IAAI,CAACtD,kBAAkB,GAAG0D,MAAM,CAAChC,iBAAiB;IAClD,IAAI,CAAC3B,SAAS,GAAG2D,MAAM,CAACvC,QAAQ;IAChC,IAAI,CAACC,WAAW,GAAGsC,MAAM,CAACjC,cAAc;EAC1C,CAAC;EACDhC,aAAa,CAACQ,SAAS,CAAC2D,cAAc,GAAG,UAAUb,GAAG,EAAE;IACtD,IAAI/B,MAAM,GAAG,IAAI,CAACX,OAAO;IACzB;IACA,IAAIW,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAAE;MAC3B,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACnB;QACA;QACA,IAAI6C,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC/C,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC+B,GAAG,CAACiB,MAAM,EAAE;UACfhD,MAAM,CAAC,CAAC,CAAC,IAAI6C,UAAU,GAAG,CAAC;UAC3B7C,MAAM,CAAC,CAAC,CAAC,IAAI6C,UAAU,GAAG,CAAC;QAC7B,CAAC,MAAM;UACL7C,MAAM,CAAC,CAAC,CAAC,IAAI6C,UAAU,GAAG,CAAC;QAC7B;MACF,CAAC,MAAM;QACL7C,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;MACf;IACF;IACA,IAAIuC,IAAI,GAAGvC,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAChC;IACA,IAAI,CAACwC,QAAQ,CAACD,IAAI,CAAC,EAAE;MACnBvC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;MACbA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;IACf;IACA,IAAI,CAACoC,aAAa,CAACL,GAAG,CAACZ,WAAW,EAAEY,GAAG,CAACM,WAAW,EAAEN,GAAG,CAACO,WAAW,CAAC;IACrE;IACA,IAAInC,QAAQ,GAAG,IAAI,CAACpB,SAAS;IAC7B,IAAI,CAACgD,GAAG,CAACkB,MAAM,EAAE;MACfjD,MAAM,CAAC,CAAC,CAAC,GAAGzB,WAAW,CAACuE,IAAI,CAACI,KAAK,CAAClD,MAAM,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAAC,GAAGA,QAAQ,CAAC;IACtE;IACA,IAAI,CAAC4B,GAAG,CAACiB,MAAM,EAAE;MACfhD,MAAM,CAAC,CAAC,CAAC,GAAGzB,WAAW,CAACuE,IAAI,CAACK,IAAI,CAACnD,MAAM,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAAC,GAAGA,QAAQ,CAAC;IACrE;EACF,CAAC;EACD1B,aAAa,CAACQ,SAAS,CAACmE,aAAa,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAE;IAC1D,IAAI,CAAClD,WAAW,GAAG,CAACiD,GAAG,EAAEC,GAAG,CAAC;EAC/B,CAAC;EACD7E,aAAa,CAACK,IAAI,GAAG,UAAU;EAC/B,OAAOL,aAAa;AACtB,CAAC,CAACJ,KAAK,CAAC;AACRA,KAAK,CAACkF,aAAa,CAAC9E,aAAa,CAAC;AAClC,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}