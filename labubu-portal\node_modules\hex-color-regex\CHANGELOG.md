

## 1.1.0 / 2017-06-03
- Release v1.1.0
- Add support for "alpha" channel in hex colors (merge [#11](https://github.com/regexhq/hex-color-regex/pull/11)):
  - `#RRGGBBAA`
  - `#RGBA`

Reference
- https://googlechrome.github.io/samples/css-alpha-channel/
- https://www.chromestatus.com/feature/5685348285808640

## 1.0.3 / 2015-07-14
- Release v1.0.3 / npm@v1.0.3
- update stuff, add related libs, @tunnckoCore
- merge pull request #8, @kevva
- Null guard, @bevacqua

## 1.0.2 / 2015-06-01
- Release v1.0.2 / npm@v1.0.2
- david to watch for devDeps
- matching groups, close #6
- update readme example
- add more tests
- switch coveralls with codeclimate coverage
- add and update tests
- update, close #5
- add strict mode, merge #4

## 1.0.1 / 2015-03-26
- Release v1.0.1 / npm@v1.0.1
- bump deps
- add `bluebird` as devDep

## 1.0.0 / 2015-02-03
- Release v1.0.0 / npm@v1.0.0
- add keywords
- add usage examples
- update tests
- remove `after_script` from travis
- replace `mocha` with `mukla`
- fix regex

## 0.0.0 - 2015-02-02
- first commits