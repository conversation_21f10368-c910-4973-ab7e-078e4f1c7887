{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as curveTool from 'zrender/lib/core/curve.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport { getSymbolSize } from './graphHelper.js';\nvar v1 = [];\nvar v2 = [];\nvar v3 = [];\nvar quadraticAt = curveTool.quadraticAt;\nvar v2DistSquare = vec2.distSquare;\nvar mathAbs = Math.abs;\nfunction intersectCurveCircle(curvePoints, center, radius) {\n  var p0 = curvePoints[0];\n  var p1 = curvePoints[1];\n  var p2 = curvePoints[2];\n  var d = Infinity;\n  var t;\n  var radiusSquare = radius * radius;\n  var interval = 0.1;\n  for (var _t = 0.1; _t <= 0.9; _t += 0.1) {\n    v1[0] = quadraticAt(p0[0], p1[0], p2[0], _t);\n    v1[1] = quadraticAt(p0[1], p1[1], p2[1], _t);\n    var diff = mathAbs(v2DistSquare(v1, center) - radiusSquare);\n    if (diff < d) {\n      d = diff;\n      t = _t;\n    }\n  }\n  // Assume the segment is monotone，Find root through Bisection method\n  // At most 32 iteration\n  for (var i = 0; i < 32; i++) {\n    // let prev = t - interval;\n    var next = t + interval;\n    // v1[0] = quadraticAt(p0[0], p1[0], p2[0], prev);\n    // v1[1] = quadraticAt(p0[1], p1[1], p2[1], prev);\n    v2[0] = quadraticAt(p0[0], p1[0], p2[0], t);\n    v2[1] = quadraticAt(p0[1], p1[1], p2[1], t);\n    v3[0] = quadraticAt(p0[0], p1[0], p2[0], next);\n    v3[1] = quadraticAt(p0[1], p1[1], p2[1], next);\n    var diff = v2DistSquare(v2, center) - radiusSquare;\n    if (mathAbs(diff) < 1e-2) {\n      break;\n    }\n    // let prevDiff = v2DistSquare(v1, center) - radiusSquare;\n    var nextDiff = v2DistSquare(v3, center) - radiusSquare;\n    interval /= 2;\n    if (diff < 0) {\n      if (nextDiff >= 0) {\n        t = t + interval;\n      } else {\n        t = t - interval;\n      }\n    } else {\n      if (nextDiff >= 0) {\n        t = t - interval;\n      } else {\n        t = t + interval;\n      }\n    }\n  }\n  return t;\n}\n// Adjust edge to avoid\nexport default function adjustEdge(graph, scale) {\n  var tmp0 = [];\n  var quadraticSubdivide = curveTool.quadraticSubdivide;\n  var pts = [[], [], []];\n  var pts2 = [[], []];\n  var v = [];\n  scale /= 2;\n  graph.eachEdge(function (edge, idx) {\n    var linePoints = edge.getLayout();\n    var fromSymbol = edge.getVisual('fromSymbol');\n    var toSymbol = edge.getVisual('toSymbol');\n    if (!linePoints.__original) {\n      linePoints.__original = [vec2.clone(linePoints[0]), vec2.clone(linePoints[1])];\n      if (linePoints[2]) {\n        linePoints.__original.push(vec2.clone(linePoints[2]));\n      }\n    }\n    var originalPoints = linePoints.__original;\n    // Quadratic curve\n    if (linePoints[2] != null) {\n      vec2.copy(pts[0], originalPoints[0]);\n      vec2.copy(pts[1], originalPoints[2]);\n      vec2.copy(pts[2], originalPoints[1]);\n      if (fromSymbol && fromSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node1);\n        var t = intersectCurveCircle(pts, originalPoints[0], symbolSize * scale);\n        // Subdivide and get the second\n        quadraticSubdivide(pts[0][0], pts[1][0], pts[2][0], t, tmp0);\n        pts[0][0] = tmp0[3];\n        pts[1][0] = tmp0[4];\n        quadraticSubdivide(pts[0][1], pts[1][1], pts[2][1], t, tmp0);\n        pts[0][1] = tmp0[3];\n        pts[1][1] = tmp0[4];\n      }\n      if (toSymbol && toSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node2);\n        var t = intersectCurveCircle(pts, originalPoints[1], symbolSize * scale);\n        // Subdivide and get the first\n        quadraticSubdivide(pts[0][0], pts[1][0], pts[2][0], t, tmp0);\n        pts[1][0] = tmp0[1];\n        pts[2][0] = tmp0[2];\n        quadraticSubdivide(pts[0][1], pts[1][1], pts[2][1], t, tmp0);\n        pts[1][1] = tmp0[1];\n        pts[2][1] = tmp0[2];\n      }\n      // Copy back to layout\n      vec2.copy(linePoints[0], pts[0]);\n      vec2.copy(linePoints[1], pts[2]);\n      vec2.copy(linePoints[2], pts[1]);\n    }\n    // Line\n    else {\n      vec2.copy(pts2[0], originalPoints[0]);\n      vec2.copy(pts2[1], originalPoints[1]);\n      vec2.sub(v, pts2[1], pts2[0]);\n      vec2.normalize(v, v);\n      if (fromSymbol && fromSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node1);\n        vec2.scaleAndAdd(pts2[0], pts2[0], v, symbolSize * scale);\n      }\n      if (toSymbol && toSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node2);\n        vec2.scaleAndAdd(pts2[1], pts2[1], v, -symbolSize * scale);\n      }\n      vec2.copy(linePoints[0], pts2[0]);\n      vec2.copy(linePoints[1], pts2[1]);\n    }\n  });\n}", "map": {"version": 3, "names": ["curveTool", "vec2", "getSymbolSize", "v1", "v2", "v3", "quadraticAt", "v2DistSquare", "distSquare", "mathAbs", "Math", "abs", "intersectCurveCircle", "curvePoints", "center", "radius", "p0", "p1", "p2", "d", "Infinity", "t", "radiusSquare", "interval", "_t", "diff", "i", "next", "nextDiff", "adjustEdge", "graph", "scale", "tmp0", "quadraticSubdivide", "pts", "pts2", "v", "eachEdge", "edge", "idx", "linePoints", "getLayout", "fromSymbol", "getVisual", "toSymbol", "__original", "clone", "push", "originalPoints", "copy", "symbolSize", "node1", "node2", "sub", "normalize", "scaleAndAdd"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/graph/adjustEdge.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as curveTool from 'zrender/lib/core/curve.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport { getSymbolSize } from './graphHelper.js';\nvar v1 = [];\nvar v2 = [];\nvar v3 = [];\nvar quadraticAt = curveTool.quadraticAt;\nvar v2DistSquare = vec2.distSquare;\nvar mathAbs = Math.abs;\nfunction intersectCurveCircle(curvePoints, center, radius) {\n  var p0 = curvePoints[0];\n  var p1 = curvePoints[1];\n  var p2 = curvePoints[2];\n  var d = Infinity;\n  var t;\n  var radiusSquare = radius * radius;\n  var interval = 0.1;\n  for (var _t = 0.1; _t <= 0.9; _t += 0.1) {\n    v1[0] = quadraticAt(p0[0], p1[0], p2[0], _t);\n    v1[1] = quadraticAt(p0[1], p1[1], p2[1], _t);\n    var diff = mathAbs(v2DistSquare(v1, center) - radiusSquare);\n    if (diff < d) {\n      d = diff;\n      t = _t;\n    }\n  }\n  // Assume the segment is monotone，Find root through Bisection method\n  // At most 32 iteration\n  for (var i = 0; i < 32; i++) {\n    // let prev = t - interval;\n    var next = t + interval;\n    // v1[0] = quadraticAt(p0[0], p1[0], p2[0], prev);\n    // v1[1] = quadraticAt(p0[1], p1[1], p2[1], prev);\n    v2[0] = quadraticAt(p0[0], p1[0], p2[0], t);\n    v2[1] = quadraticAt(p0[1], p1[1], p2[1], t);\n    v3[0] = quadraticAt(p0[0], p1[0], p2[0], next);\n    v3[1] = quadraticAt(p0[1], p1[1], p2[1], next);\n    var diff = v2DistSquare(v2, center) - radiusSquare;\n    if (mathAbs(diff) < 1e-2) {\n      break;\n    }\n    // let prevDiff = v2DistSquare(v1, center) - radiusSquare;\n    var nextDiff = v2DistSquare(v3, center) - radiusSquare;\n    interval /= 2;\n    if (diff < 0) {\n      if (nextDiff >= 0) {\n        t = t + interval;\n      } else {\n        t = t - interval;\n      }\n    } else {\n      if (nextDiff >= 0) {\n        t = t - interval;\n      } else {\n        t = t + interval;\n      }\n    }\n  }\n  return t;\n}\n// Adjust edge to avoid\nexport default function adjustEdge(graph, scale) {\n  var tmp0 = [];\n  var quadraticSubdivide = curveTool.quadraticSubdivide;\n  var pts = [[], [], []];\n  var pts2 = [[], []];\n  var v = [];\n  scale /= 2;\n  graph.eachEdge(function (edge, idx) {\n    var linePoints = edge.getLayout();\n    var fromSymbol = edge.getVisual('fromSymbol');\n    var toSymbol = edge.getVisual('toSymbol');\n    if (!linePoints.__original) {\n      linePoints.__original = [vec2.clone(linePoints[0]), vec2.clone(linePoints[1])];\n      if (linePoints[2]) {\n        linePoints.__original.push(vec2.clone(linePoints[2]));\n      }\n    }\n    var originalPoints = linePoints.__original;\n    // Quadratic curve\n    if (linePoints[2] != null) {\n      vec2.copy(pts[0], originalPoints[0]);\n      vec2.copy(pts[1], originalPoints[2]);\n      vec2.copy(pts[2], originalPoints[1]);\n      if (fromSymbol && fromSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node1);\n        var t = intersectCurveCircle(pts, originalPoints[0], symbolSize * scale);\n        // Subdivide and get the second\n        quadraticSubdivide(pts[0][0], pts[1][0], pts[2][0], t, tmp0);\n        pts[0][0] = tmp0[3];\n        pts[1][0] = tmp0[4];\n        quadraticSubdivide(pts[0][1], pts[1][1], pts[2][1], t, tmp0);\n        pts[0][1] = tmp0[3];\n        pts[1][1] = tmp0[4];\n      }\n      if (toSymbol && toSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node2);\n        var t = intersectCurveCircle(pts, originalPoints[1], symbolSize * scale);\n        // Subdivide and get the first\n        quadraticSubdivide(pts[0][0], pts[1][0], pts[2][0], t, tmp0);\n        pts[1][0] = tmp0[1];\n        pts[2][0] = tmp0[2];\n        quadraticSubdivide(pts[0][1], pts[1][1], pts[2][1], t, tmp0);\n        pts[1][1] = tmp0[1];\n        pts[2][1] = tmp0[2];\n      }\n      // Copy back to layout\n      vec2.copy(linePoints[0], pts[0]);\n      vec2.copy(linePoints[1], pts[2]);\n      vec2.copy(linePoints[2], pts[1]);\n    }\n    // Line\n    else {\n      vec2.copy(pts2[0], originalPoints[0]);\n      vec2.copy(pts2[1], originalPoints[1]);\n      vec2.sub(v, pts2[1], pts2[0]);\n      vec2.normalize(v, v);\n      if (fromSymbol && fromSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node1);\n        vec2.scaleAndAdd(pts2[0], pts2[0], v, symbolSize * scale);\n      }\n      if (toSymbol && toSymbol !== 'none') {\n        var symbolSize = getSymbolSize(edge.node2);\n        vec2.scaleAndAdd(pts2[1], pts2[1], v, -symbolSize * scale);\n      }\n      vec2.copy(linePoints[0], pts2[0]);\n      vec2.copy(linePoints[1], pts2[1]);\n    }\n  });\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,SAAS,MAAM,2BAA2B;AACtD,OAAO,KAAKC,IAAI,MAAM,4BAA4B;AAClD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,IAAIC,EAAE,GAAG,EAAE;AACX,IAAIC,EAAE,GAAG,EAAE;AACX,IAAIC,EAAE,GAAG,EAAE;AACX,IAAIC,WAAW,GAAGN,SAAS,CAACM,WAAW;AACvC,IAAIC,YAAY,GAAGN,IAAI,CAACO,UAAU;AAClC,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,SAASC,oBAAoBA,CAACC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAE;EACzD,IAAIC,EAAE,GAAGH,WAAW,CAAC,CAAC,CAAC;EACvB,IAAII,EAAE,GAAGJ,WAAW,CAAC,CAAC,CAAC;EACvB,IAAIK,EAAE,GAAGL,WAAW,CAAC,CAAC,CAAC;EACvB,IAAIM,CAAC,GAAGC,QAAQ;EAChB,IAAIC,CAAC;EACL,IAAIC,YAAY,GAAGP,MAAM,GAAGA,MAAM;EAClC,IAAIQ,QAAQ,GAAG,GAAG;EAClB,KAAK,IAAIC,EAAE,GAAG,GAAG,EAAEA,EAAE,IAAI,GAAG,EAAEA,EAAE,IAAI,GAAG,EAAE;IACvCrB,EAAE,CAAC,CAAC,CAAC,GAAGG,WAAW,CAACU,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEM,EAAE,CAAC;IAC5CrB,EAAE,CAAC,CAAC,CAAC,GAAGG,WAAW,CAACU,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEM,EAAE,CAAC;IAC5C,IAAIC,IAAI,GAAGhB,OAAO,CAACF,YAAY,CAACJ,EAAE,EAAEW,MAAM,CAAC,GAAGQ,YAAY,CAAC;IAC3D,IAAIG,IAAI,GAAGN,CAAC,EAAE;MACZA,CAAC,GAAGM,IAAI;MACRJ,CAAC,GAAGG,EAAE;IACR;EACF;EACA;EACA;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IAC3B;IACA,IAAIC,IAAI,GAAGN,CAAC,GAAGE,QAAQ;IACvB;IACA;IACAnB,EAAE,CAAC,CAAC,CAAC,GAAGE,WAAW,CAACU,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEG,CAAC,CAAC;IAC3CjB,EAAE,CAAC,CAAC,CAAC,GAAGE,WAAW,CAACU,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEG,CAAC,CAAC;IAC3ChB,EAAE,CAAC,CAAC,CAAC,GAAGC,WAAW,CAACU,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAES,IAAI,CAAC;IAC9CtB,EAAE,CAAC,CAAC,CAAC,GAAGC,WAAW,CAACU,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,EAAES,IAAI,CAAC;IAC9C,IAAIF,IAAI,GAAGlB,YAAY,CAACH,EAAE,EAAEU,MAAM,CAAC,GAAGQ,YAAY;IAClD,IAAIb,OAAO,CAACgB,IAAI,CAAC,GAAG,IAAI,EAAE;MACxB;IACF;IACA;IACA,IAAIG,QAAQ,GAAGrB,YAAY,CAACF,EAAE,EAAES,MAAM,CAAC,GAAGQ,YAAY;IACtDC,QAAQ,IAAI,CAAC;IACb,IAAIE,IAAI,GAAG,CAAC,EAAE;MACZ,IAAIG,QAAQ,IAAI,CAAC,EAAE;QACjBP,CAAC,GAAGA,CAAC,GAAGE,QAAQ;MAClB,CAAC,MAAM;QACLF,CAAC,GAAGA,CAAC,GAAGE,QAAQ;MAClB;IACF,CAAC,MAAM;MACL,IAAIK,QAAQ,IAAI,CAAC,EAAE;QACjBP,CAAC,GAAGA,CAAC,GAAGE,QAAQ;MAClB,CAAC,MAAM;QACLF,CAAC,GAAGA,CAAC,GAAGE,QAAQ;MAClB;IACF;EACF;EACA,OAAOF,CAAC;AACV;AACA;AACA,eAAe,SAASQ,UAAUA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC/C,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,kBAAkB,GAAGjC,SAAS,CAACiC,kBAAkB;EACrD,IAAIC,GAAG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACtB,IAAIC,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;EACnB,IAAIC,CAAC,GAAG,EAAE;EACVL,KAAK,IAAI,CAAC;EACVD,KAAK,CAACO,QAAQ,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;IAClC,IAAIC,UAAU,GAAGF,IAAI,CAACG,SAAS,CAAC,CAAC;IACjC,IAAIC,UAAU,GAAGJ,IAAI,CAACK,SAAS,CAAC,YAAY,CAAC;IAC7C,IAAIC,QAAQ,GAAGN,IAAI,CAACK,SAAS,CAAC,UAAU,CAAC;IACzC,IAAI,CAACH,UAAU,CAACK,UAAU,EAAE;MAC1BL,UAAU,CAACK,UAAU,GAAG,CAAC5C,IAAI,CAAC6C,KAAK,CAACN,UAAU,CAAC,CAAC,CAAC,CAAC,EAAEvC,IAAI,CAAC6C,KAAK,CAACN,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;QACjBA,UAAU,CAACK,UAAU,CAACE,IAAI,CAAC9C,IAAI,CAAC6C,KAAK,CAACN,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD;IACF;IACA,IAAIQ,cAAc,GAAGR,UAAU,CAACK,UAAU;IAC1C;IACA,IAAIL,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MACzBvC,IAAI,CAACgD,IAAI,CAACf,GAAG,CAAC,CAAC,CAAC,EAAEc,cAAc,CAAC,CAAC,CAAC,CAAC;MACpC/C,IAAI,CAACgD,IAAI,CAACf,GAAG,CAAC,CAAC,CAAC,EAAEc,cAAc,CAAC,CAAC,CAAC,CAAC;MACpC/C,IAAI,CAACgD,IAAI,CAACf,GAAG,CAAC,CAAC,CAAC,EAAEc,cAAc,CAAC,CAAC,CAAC,CAAC;MACpC,IAAIN,UAAU,IAAIA,UAAU,KAAK,MAAM,EAAE;QACvC,IAAIQ,UAAU,GAAGhD,aAAa,CAACoC,IAAI,CAACa,KAAK,CAAC;QAC1C,IAAI9B,CAAC,GAAGT,oBAAoB,CAACsB,GAAG,EAAEc,cAAc,CAAC,CAAC,CAAC,EAAEE,UAAU,GAAGnB,KAAK,CAAC;QACxE;QACAE,kBAAkB,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,EAAEW,IAAI,CAAC;QAC5DE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;QACnBE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;QACnBC,kBAAkB,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,EAAEW,IAAI,CAAC;QAC5DE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;QACnBE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;MACrB;MACA,IAAIY,QAAQ,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAIM,UAAU,GAAGhD,aAAa,CAACoC,IAAI,CAACc,KAAK,CAAC;QAC1C,IAAI/B,CAAC,GAAGT,oBAAoB,CAACsB,GAAG,EAAEc,cAAc,CAAC,CAAC,CAAC,EAAEE,UAAU,GAAGnB,KAAK,CAAC;QACxE;QACAE,kBAAkB,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,EAAEW,IAAI,CAAC;QAC5DE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;QACnBE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;QACnBC,kBAAkB,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEb,CAAC,EAAEW,IAAI,CAAC;QAC5DE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;QACnBE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,IAAI,CAAC,CAAC,CAAC;MACrB;MACA;MACA/B,IAAI,CAACgD,IAAI,CAACT,UAAU,CAAC,CAAC,CAAC,EAAEN,GAAG,CAAC,CAAC,CAAC,CAAC;MAChCjC,IAAI,CAACgD,IAAI,CAACT,UAAU,CAAC,CAAC,CAAC,EAAEN,GAAG,CAAC,CAAC,CAAC,CAAC;MAChCjC,IAAI,CAACgD,IAAI,CAACT,UAAU,CAAC,CAAC,CAAC,EAAEN,GAAG,CAAC,CAAC,CAAC,CAAC;IAClC;IACA;IAAA,KACK;MACHjC,IAAI,CAACgD,IAAI,CAACd,IAAI,CAAC,CAAC,CAAC,EAAEa,cAAc,CAAC,CAAC,CAAC,CAAC;MACrC/C,IAAI,CAACgD,IAAI,CAACd,IAAI,CAAC,CAAC,CAAC,EAAEa,cAAc,CAAC,CAAC,CAAC,CAAC;MACrC/C,IAAI,CAACoD,GAAG,CAACjB,CAAC,EAAED,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7BlC,IAAI,CAACqD,SAAS,CAAClB,CAAC,EAAEA,CAAC,CAAC;MACpB,IAAIM,UAAU,IAAIA,UAAU,KAAK,MAAM,EAAE;QACvC,IAAIQ,UAAU,GAAGhD,aAAa,CAACoC,IAAI,CAACa,KAAK,CAAC;QAC1ClD,IAAI,CAACsD,WAAW,CAACpB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEC,CAAC,EAAEc,UAAU,GAAGnB,KAAK,CAAC;MAC3D;MACA,IAAIa,QAAQ,IAAIA,QAAQ,KAAK,MAAM,EAAE;QACnC,IAAIM,UAAU,GAAGhD,aAAa,CAACoC,IAAI,CAACc,KAAK,CAAC;QAC1CnD,IAAI,CAACsD,WAAW,CAACpB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEC,CAAC,EAAE,CAACc,UAAU,GAAGnB,KAAK,CAAC;MAC5D;MACA9B,IAAI,CAACgD,IAAI,CAACT,UAAU,CAAC,CAAC,CAAC,EAAEL,IAAI,CAAC,CAAC,CAAC,CAAC;MACjClC,IAAI,CAACgD,IAAI,CAACT,UAAU,CAAC,CAAC,CAAC,EAAEL,IAAI,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}