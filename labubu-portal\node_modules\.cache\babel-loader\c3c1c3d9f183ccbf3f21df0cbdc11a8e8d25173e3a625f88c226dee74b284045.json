{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nfunction normalize(a) {\n  if (!(a instanceof Array)) {\n    a = [a, a];\n  }\n  return a;\n}\nvar linesVisual = {\n  seriesType: 'lines',\n  reset: function (seriesModel) {\n    var symbolType = normalize(seriesModel.get('symbol'));\n    var symbolSize = normalize(seriesModel.get('symbolSize'));\n    var data = seriesModel.getData();\n    data.setVisual('fromSymbol', symbolType && symbolType[0]);\n    data.setVisual('toSymbol', symbolType && symbolType[1]);\n    data.setVisual('fromSymbolSize', symbolSize && symbolSize[0]);\n    data.setVisual('toSymbolSize', symbolSize && symbolSize[1]);\n    function dataEach(data, idx) {\n      var itemModel = data.getItemModel(idx);\n      var symbolType = normalize(itemModel.getShallow('symbol', true));\n      var symbolSize = normalize(itemModel.getShallow('symbolSize', true));\n      symbolType[0] && data.setItemVisual(idx, 'fromSymbol', symbolType[0]);\n      symbolType[1] && data.setItemVisual(idx, 'toSymbol', symbolType[1]);\n      symbolSize[0] && data.setItemVisual(idx, 'fromSymbolSize', symbolSize[0]);\n      symbolSize[1] && data.setItemVisual(idx, 'toSymbolSize', symbolSize[1]);\n    }\n    return {\n      dataEach: data.hasItemOption ? dataEach : null\n    };\n  }\n};\nexport default linesVisual;", "map": {"version": 3, "names": ["normalize", "a", "Array", "linesVisual", "seriesType", "reset", "seriesModel", "symbolType", "get", "symbolSize", "data", "getData", "setVisual", "dataEach", "idx", "itemModel", "getItemModel", "getShallow", "setItemVisual", "hasItemOption"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/lines/linesVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nfunction normalize(a) {\n  if (!(a instanceof Array)) {\n    a = [a, a];\n  }\n  return a;\n}\nvar linesVisual = {\n  seriesType: 'lines',\n  reset: function (seriesModel) {\n    var symbolType = normalize(seriesModel.get('symbol'));\n    var symbolSize = normalize(seriesModel.get('symbolSize'));\n    var data = seriesModel.getData();\n    data.setVisual('fromSymbol', symbolType && symbolType[0]);\n    data.setVisual('toSymbol', symbolType && symbolType[1]);\n    data.setVisual('fromSymbolSize', symbolSize && symbolSize[0]);\n    data.setVisual('toSymbolSize', symbolSize && symbolSize[1]);\n    function dataEach(data, idx) {\n      var itemModel = data.getItemModel(idx);\n      var symbolType = normalize(itemModel.getShallow('symbol', true));\n      var symbolSize = normalize(itemModel.getShallow('symbolSize', true));\n      symbolType[0] && data.setItemVisual(idx, 'fromSymbol', symbolType[0]);\n      symbolType[1] && data.setItemVisual(idx, 'toSymbol', symbolType[1]);\n      symbolSize[0] && data.setItemVisual(idx, 'fromSymbolSize', symbolSize[0]);\n      symbolSize[1] && data.setItemVisual(idx, 'toSymbolSize', symbolSize[1]);\n    }\n    return {\n      dataEach: data.hasItemOption ? dataEach : null\n    };\n  }\n};\nexport default linesVisual;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,CAAC,EAAE;EACpB,IAAI,EAAEA,CAAC,YAAYC,KAAK,CAAC,EAAE;IACzBD,CAAC,GAAG,CAACA,CAAC,EAAEA,CAAC,CAAC;EACZ;EACA,OAAOA,CAAC;AACV;AACA,IAAIE,WAAW,GAAG;EAChBC,UAAU,EAAE,OAAO;EACnBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAE;IAC5B,IAAIC,UAAU,GAAGP,SAAS,CAACM,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrD,IAAIC,UAAU,GAAGT,SAAS,CAACM,WAAW,CAACE,GAAG,CAAC,YAAY,CAAC,CAAC;IACzD,IAAIE,IAAI,GAAGJ,WAAW,CAACK,OAAO,CAAC,CAAC;IAChCD,IAAI,CAACE,SAAS,CAAC,YAAY,EAAEL,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IACzDG,IAAI,CAACE,SAAS,CAAC,UAAU,EAAEL,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IACvDG,IAAI,CAACE,SAAS,CAAC,gBAAgB,EAAEH,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7DC,IAAI,CAACE,SAAS,CAAC,cAAc,EAAEH,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3D,SAASI,QAAQA,CAACH,IAAI,EAAEI,GAAG,EAAE;MAC3B,IAAIC,SAAS,GAAGL,IAAI,CAACM,YAAY,CAACF,GAAG,CAAC;MACtC,IAAIP,UAAU,GAAGP,SAAS,CAACe,SAAS,CAACE,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;MAChE,IAAIR,UAAU,GAAGT,SAAS,CAACe,SAAS,CAACE,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;MACpEV,UAAU,CAAC,CAAC,CAAC,IAAIG,IAAI,CAACQ,aAAa,CAACJ,GAAG,EAAE,YAAY,EAAEP,UAAU,CAAC,CAAC,CAAC,CAAC;MACrEA,UAAU,CAAC,CAAC,CAAC,IAAIG,IAAI,CAACQ,aAAa,CAACJ,GAAG,EAAE,UAAU,EAAEP,UAAU,CAAC,CAAC,CAAC,CAAC;MACnEE,UAAU,CAAC,CAAC,CAAC,IAAIC,IAAI,CAACQ,aAAa,CAACJ,GAAG,EAAE,gBAAgB,EAAEL,UAAU,CAAC,CAAC,CAAC,CAAC;MACzEA,UAAU,CAAC,CAAC,CAAC,IAAIC,IAAI,CAACQ,aAAa,CAACJ,GAAG,EAAE,cAAc,EAAEL,UAAU,CAAC,CAAC,CAAC,CAAC;IACzE;IACA,OAAO;MACLI,QAAQ,EAAEH,IAAI,CAACS,aAAa,GAAGN,QAAQ,GAAG;IAC5C,CAAC;EACH;AACF,CAAC;AACD,eAAeV,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}