{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar contrastColor = '#B9B8CE';\nvar backgroundColor = '#100C2A';\nvar axisCommon = function () {\n  return {\n    axisLine: {\n      lineStyle: {\n        color: contrastColor\n      }\n    },\n    splitLine: {\n      lineStyle: {\n        color: '#484753'\n      }\n    },\n    splitArea: {\n      areaStyle: {\n        color: ['rgba(255,255,255,0.02)', 'rgba(255,255,255,0.05)']\n      }\n    },\n    minorSplitLine: {\n      lineStyle: {\n        color: '#20203B'\n      }\n    }\n  };\n};\nvar colorPalette = ['#4992ff', '#7cffb2', '#fddd60', '#ff6e76', '#58d9f9', '#05c091', '#ff8a45', '#8d48e3', '#dd79ff'];\nvar theme = {\n  darkMode: true,\n  color: colorPalette,\n  backgroundColor: backgroundColor,\n  axisPointer: {\n    lineStyle: {\n      color: '#817f91'\n    },\n    crossStyle: {\n      color: '#817f91'\n    },\n    label: {\n      // TODO Contrast of label backgorundColor\n      color: '#fff'\n    }\n  },\n  legend: {\n    textStyle: {\n      color: contrastColor\n    },\n    pageTextStyle: {\n      color: contrastColor\n    }\n  },\n  textStyle: {\n    color: contrastColor\n  },\n  title: {\n    textStyle: {\n      color: '#EEF1FA'\n    },\n    subtextStyle: {\n      color: '#B9B8CE'\n    }\n  },\n  toolbox: {\n    iconStyle: {\n      borderColor: contrastColor\n    }\n  },\n  dataZoom: {\n    borderColor: '#71708A',\n    textStyle: {\n      color: contrastColor\n    },\n    brushStyle: {\n      color: 'rgba(135,163,206,0.3)'\n    },\n    handleStyle: {\n      color: '#353450',\n      borderColor: '#C5CBE3'\n    },\n    moveHandleStyle: {\n      color: '#B0B6C3',\n      opacity: 0.3\n    },\n    fillerColor: 'rgba(135,163,206,0.2)',\n    emphasis: {\n      handleStyle: {\n        borderColor: '#91B7F2',\n        color: '#4D587D'\n      },\n      moveHandleStyle: {\n        color: '#636D9A',\n        opacity: 0.7\n      }\n    },\n    dataBackground: {\n      lineStyle: {\n        color: '#71708A',\n        width: 1\n      },\n      areaStyle: {\n        color: '#71708A'\n      }\n    },\n    selectedDataBackground: {\n      lineStyle: {\n        color: '#87A3CE'\n      },\n      areaStyle: {\n        color: '#87A3CE'\n      }\n    }\n  },\n  visualMap: {\n    textStyle: {\n      color: contrastColor\n    }\n  },\n  timeline: {\n    lineStyle: {\n      color: contrastColor\n    },\n    label: {\n      color: contrastColor\n    },\n    controlStyle: {\n      color: contrastColor,\n      borderColor: contrastColor\n    }\n  },\n  calendar: {\n    itemStyle: {\n      color: backgroundColor\n    },\n    dayLabel: {\n      color: contrastColor\n    },\n    monthLabel: {\n      color: contrastColor\n    },\n    yearLabel: {\n      color: contrastColor\n    }\n  },\n  timeAxis: axisCommon(),\n  logAxis: axisCommon(),\n  valueAxis: axisCommon(),\n  categoryAxis: axisCommon(),\n  line: {\n    symbol: 'circle'\n  },\n  graph: {\n    color: colorPalette\n  },\n  gauge: {\n    title: {\n      color: contrastColor\n    },\n    axisLine: {\n      lineStyle: {\n        color: [[1, 'rgba(207,212,219,0.2)']]\n      }\n    },\n    axisLabel: {\n      color: contrastColor\n    },\n    detail: {\n      color: '#EEF1FA'\n    }\n  },\n  candlestick: {\n    itemStyle: {\n      color: '#f64e56',\n      color0: '#54ea92',\n      borderColor: '#f64e56',\n      borderColor0: '#54ea92'\n      // borderColor: '#ca2824',\n      // borderColor0: '#09a443'\n    }\n  }\n};\ntheme.categoryAxis.splitLine.show = false;\nexport default theme;", "map": {"version": 3, "names": ["contrastColor", "backgroundColor", "axisCommon", "axisLine", "lineStyle", "color", "splitLine", "splitArea", "areaStyle", "minorSplitLine", "colorPalette", "theme", "darkMode", "axisPointer", "crossStyle", "label", "legend", "textStyle", "pageTextStyle", "title", "subtextStyle", "toolbox", "iconStyle", "borderColor", "dataZoom", "brushStyle", "handleStyle", "moveHandleStyle", "opacity", "fillerColor", "emphasis", "dataBackground", "width", "selectedDataBackground", "visualMap", "timeline", "controlStyle", "calendar", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "timeAxis", "logAxis", "valueAxis", "categoryAxis", "line", "symbol", "graph", "gauge", "axisLabel", "detail", "candlestick", "color0", "borderColor0", "show"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/theme/dark.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar contrastColor = '#B9B8CE';\nvar backgroundColor = '#100C2A';\nvar axisCommon = function () {\n  return {\n    axisLine: {\n      lineStyle: {\n        color: contrastColor\n      }\n    },\n    splitLine: {\n      lineStyle: {\n        color: '#484753'\n      }\n    },\n    splitArea: {\n      areaStyle: {\n        color: ['rgba(255,255,255,0.02)', 'rgba(255,255,255,0.05)']\n      }\n    },\n    minorSplitLine: {\n      lineStyle: {\n        color: '#20203B'\n      }\n    }\n  };\n};\nvar colorPalette = ['#4992ff', '#7cffb2', '#fddd60', '#ff6e76', '#58d9f9', '#05c091', '#ff8a45', '#8d48e3', '#dd79ff'];\nvar theme = {\n  darkMode: true,\n  color: colorPalette,\n  backgroundColor: backgroundColor,\n  axisPointer: {\n    lineStyle: {\n      color: '#817f91'\n    },\n    crossStyle: {\n      color: '#817f91'\n    },\n    label: {\n      // TODO Contrast of label backgorundColor\n      color: '#fff'\n    }\n  },\n  legend: {\n    textStyle: {\n      color: contrastColor\n    },\n    pageTextStyle: {\n      color: contrastColor\n    }\n  },\n  textStyle: {\n    color: contrastColor\n  },\n  title: {\n    textStyle: {\n      color: '#EEF1FA'\n    },\n    subtextStyle: {\n      color: '#B9B8CE'\n    }\n  },\n  toolbox: {\n    iconStyle: {\n      borderColor: contrastColor\n    }\n  },\n  dataZoom: {\n    borderColor: '#71708A',\n    textStyle: {\n      color: contrastColor\n    },\n    brushStyle: {\n      color: 'rgba(135,163,206,0.3)'\n    },\n    handleStyle: {\n      color: '#353450',\n      borderColor: '#C5CBE3'\n    },\n    moveHandleStyle: {\n      color: '#B0B6C3',\n      opacity: 0.3\n    },\n    fillerColor: 'rgba(135,163,206,0.2)',\n    emphasis: {\n      handleStyle: {\n        borderColor: '#91B7F2',\n        color: '#4D587D'\n      },\n      moveHandleStyle: {\n        color: '#636D9A',\n        opacity: 0.7\n      }\n    },\n    dataBackground: {\n      lineStyle: {\n        color: '#71708A',\n        width: 1\n      },\n      areaStyle: {\n        color: '#71708A'\n      }\n    },\n    selectedDataBackground: {\n      lineStyle: {\n        color: '#87A3CE'\n      },\n      areaStyle: {\n        color: '#87A3CE'\n      }\n    }\n  },\n  visualMap: {\n    textStyle: {\n      color: contrastColor\n    }\n  },\n  timeline: {\n    lineStyle: {\n      color: contrastColor\n    },\n    label: {\n      color: contrastColor\n    },\n    controlStyle: {\n      color: contrastColor,\n      borderColor: contrastColor\n    }\n  },\n  calendar: {\n    itemStyle: {\n      color: backgroundColor\n    },\n    dayLabel: {\n      color: contrastColor\n    },\n    monthLabel: {\n      color: contrastColor\n    },\n    yearLabel: {\n      color: contrastColor\n    }\n  },\n  timeAxis: axisCommon(),\n  logAxis: axisCommon(),\n  valueAxis: axisCommon(),\n  categoryAxis: axisCommon(),\n  line: {\n    symbol: 'circle'\n  },\n  graph: {\n    color: colorPalette\n  },\n  gauge: {\n    title: {\n      color: contrastColor\n    },\n    axisLine: {\n      lineStyle: {\n        color: [[1, 'rgba(207,212,219,0.2)']]\n      }\n    },\n    axisLabel: {\n      color: contrastColor\n    },\n    detail: {\n      color: '#EEF1FA'\n    }\n  },\n  candlestick: {\n    itemStyle: {\n      color: '#f64e56',\n      color0: '#54ea92',\n      borderColor: '#f64e56',\n      borderColor0: '#54ea92'\n      // borderColor: '#ca2824',\n      // borderColor0: '#09a443'\n    }\n  }\n};\ntheme.categoryAxis.splitLine.show = false;\nexport default theme;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa,GAAG,SAAS;AAC7B,IAAIC,eAAe,GAAG,SAAS;AAC/B,IAAIC,UAAU,GAAG,SAAAA,CAAA,EAAY;EAC3B,OAAO;IACLC,QAAQ,EAAE;MACRC,SAAS,EAAE;QACTC,KAAK,EAAEL;MACT;IACF,CAAC;IACDM,SAAS,EAAE;MACTF,SAAS,EAAE;QACTC,KAAK,EAAE;MACT;IACF,CAAC;IACDE,SAAS,EAAE;MACTC,SAAS,EAAE;QACTH,KAAK,EAAE,CAAC,wBAAwB,EAAE,wBAAwB;MAC5D;IACF,CAAC;IACDI,cAAc,EAAE;MACdL,SAAS,EAAE;QACTC,KAAK,EAAE;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD,IAAIK,YAAY,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AACtH,IAAIC,KAAK,GAAG;EACVC,QAAQ,EAAE,IAAI;EACdP,KAAK,EAAEK,YAAY;EACnBT,eAAe,EAAEA,eAAe;EAChCY,WAAW,EAAE;IACXT,SAAS,EAAE;MACTC,KAAK,EAAE;IACT,CAAC;IACDS,UAAU,EAAE;MACVT,KAAK,EAAE;IACT,CAAC;IACDU,KAAK,EAAE;MACL;MACAV,KAAK,EAAE;IACT;EACF,CAAC;EACDW,MAAM,EAAE;IACNC,SAAS,EAAE;MACTZ,KAAK,EAAEL;IACT,CAAC;IACDkB,aAAa,EAAE;MACbb,KAAK,EAAEL;IACT;EACF,CAAC;EACDiB,SAAS,EAAE;IACTZ,KAAK,EAAEL;EACT,CAAC;EACDmB,KAAK,EAAE;IACLF,SAAS,EAAE;MACTZ,KAAK,EAAE;IACT,CAAC;IACDe,YAAY,EAAE;MACZf,KAAK,EAAE;IACT;EACF,CAAC;EACDgB,OAAO,EAAE;IACPC,SAAS,EAAE;MACTC,WAAW,EAAEvB;IACf;EACF,CAAC;EACDwB,QAAQ,EAAE;IACRD,WAAW,EAAE,SAAS;IACtBN,SAAS,EAAE;MACTZ,KAAK,EAAEL;IACT,CAAC;IACDyB,UAAU,EAAE;MACVpB,KAAK,EAAE;IACT,CAAC;IACDqB,WAAW,EAAE;MACXrB,KAAK,EAAE,SAAS;MAChBkB,WAAW,EAAE;IACf,CAAC;IACDI,eAAe,EAAE;MACftB,KAAK,EAAE,SAAS;MAChBuB,OAAO,EAAE;IACX,CAAC;IACDC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE;MACRJ,WAAW,EAAE;QACXH,WAAW,EAAE,SAAS;QACtBlB,KAAK,EAAE;MACT,CAAC;MACDsB,eAAe,EAAE;QACftB,KAAK,EAAE,SAAS;QAChBuB,OAAO,EAAE;MACX;IACF,CAAC;IACDG,cAAc,EAAE;MACd3B,SAAS,EAAE;QACTC,KAAK,EAAE,SAAS;QAChB2B,KAAK,EAAE;MACT,CAAC;MACDxB,SAAS,EAAE;QACTH,KAAK,EAAE;MACT;IACF,CAAC;IACD4B,sBAAsB,EAAE;MACtB7B,SAAS,EAAE;QACTC,KAAK,EAAE;MACT,CAAC;MACDG,SAAS,EAAE;QACTH,KAAK,EAAE;MACT;IACF;EACF,CAAC;EACD6B,SAAS,EAAE;IACTjB,SAAS,EAAE;MACTZ,KAAK,EAAEL;IACT;EACF,CAAC;EACDmC,QAAQ,EAAE;IACR/B,SAAS,EAAE;MACTC,KAAK,EAAEL;IACT,CAAC;IACDe,KAAK,EAAE;MACLV,KAAK,EAAEL;IACT,CAAC;IACDoC,YAAY,EAAE;MACZ/B,KAAK,EAAEL,aAAa;MACpBuB,WAAW,EAAEvB;IACf;EACF,CAAC;EACDqC,QAAQ,EAAE;IACRC,SAAS,EAAE;MACTjC,KAAK,EAAEJ;IACT,CAAC;IACDsC,QAAQ,EAAE;MACRlC,KAAK,EAAEL;IACT,CAAC;IACDwC,UAAU,EAAE;MACVnC,KAAK,EAAEL;IACT,CAAC;IACDyC,SAAS,EAAE;MACTpC,KAAK,EAAEL;IACT;EACF,CAAC;EACD0C,QAAQ,EAAExC,UAAU,CAAC,CAAC;EACtByC,OAAO,EAAEzC,UAAU,CAAC,CAAC;EACrB0C,SAAS,EAAE1C,UAAU,CAAC,CAAC;EACvB2C,YAAY,EAAE3C,UAAU,CAAC,CAAC;EAC1B4C,IAAI,EAAE;IACJC,MAAM,EAAE;EACV,CAAC;EACDC,KAAK,EAAE;IACL3C,KAAK,EAAEK;EACT,CAAC;EACDuC,KAAK,EAAE;IACL9B,KAAK,EAAE;MACLd,KAAK,EAAEL;IACT,CAAC;IACDG,QAAQ,EAAE;MACRC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,uBAAuB,CAAC;MACtC;IACF,CAAC;IACD6C,SAAS,EAAE;MACT7C,KAAK,EAAEL;IACT,CAAC;IACDmD,MAAM,EAAE;MACN9C,KAAK,EAAE;IACT;EACF,CAAC;EACD+C,WAAW,EAAE;IACXd,SAAS,EAAE;MACTjC,KAAK,EAAE,SAAS;MAChBgD,MAAM,EAAE,SAAS;MACjB9B,WAAW,EAAE,SAAS;MACtB+B,YAAY,EAAE;MACd;MACA;IACF;EACF;AACF,CAAC;AACD3C,KAAK,CAACkC,YAAY,CAACvC,SAAS,CAACiD,IAAI,GAAG,KAAK;AACzC,eAAe5C,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}