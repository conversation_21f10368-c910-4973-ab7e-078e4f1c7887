{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO Optimize on polar\nimport * as colorUtil from 'zrender/lib/tool/color.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, setStatesStylesFromModel } from '../../util/states.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport { retrieve, mergeAll, map, curry, filter, extend, isString } from 'zrender/lib/core/util.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport MarkerModel from './MarkerModel.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nvar inner = makeInner();\nvar markAreaTransform = function (seriesModel, coordSys, maModel, item) {\n  // item may be null\n  var item0 = item[0];\n  var item1 = item[1];\n  if (!item0 || !item1) {\n    return;\n  }\n  var lt = markerHelper.dataTransform(seriesModel, item0);\n  var rb = markerHelper.dataTransform(seriesModel, item1);\n  // FIXME make sure lt is less than rb\n  var ltCoord = lt.coord;\n  var rbCoord = rb.coord;\n  ltCoord[0] = retrieve(ltCoord[0], -Infinity);\n  ltCoord[1] = retrieve(ltCoord[1], -Infinity);\n  rbCoord[0] = retrieve(rbCoord[0], Infinity);\n  rbCoord[1] = retrieve(rbCoord[1], Infinity);\n  // Merge option into one\n  var result = mergeAll([{}, lt, rb]);\n  result.coord = [lt.coord, rb.coord];\n  result.x0 = lt.x;\n  result.y0 = lt.y;\n  result.x1 = rb.x;\n  result.y1 = rb.y;\n  return result;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markArea has one dim\nfunction ifMarkAreaHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]);\n}\nfunction markAreaFilter(coordSys, item) {\n  var fromCoord = item.coord[0];\n  var toCoord = item.coord[1];\n  var item0 = {\n    coord: fromCoord,\n    x: item.x0,\n    y: item.y0\n  };\n  var item1 = {\n    coord: toCoord,\n    x: item.x1,\n    y: item.y1\n  };\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    // In case\n    // {\n    //  markArea: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkAreaHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkAreaHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n    // Directly returning true may also do the work,\n    // because markArea will not be shown automatically\n    // when it's not included in coordinate system.\n    // But filtering ahead can avoid keeping rendering markArea\n    // when there are too many of them.\n    return markerHelper.zoneFilter(coordSys, item0, item1);\n  }\n  return markerHelper.dataFilter(coordSys, item0) || markerHelper.dataFilter(coordSys, item1);\n}\n// dims can be ['x0', 'y0'], ['x1', 'y1'], ['x0', 'y1'], ['x1', 'y0']\nfunction getSingleMarkerEndPoint(data, idx, dims, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get(dims[0]), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get(dims[1]), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Consider the case that user input the right-bottom point first\n      // Pick the larger x and y as 'x1' and 'y1'\n      var pointValue0 = data.getValues(['x0', 'y0'], idx);\n      var pointValue1 = data.getValues(['x1', 'y1'], idx);\n      var clampPointValue0 = coordSys.clampData(pointValue0);\n      var clampPointValue1 = coordSys.clampData(pointValue1);\n      var pointValue = [];\n      if (dims[0] === 'x0') {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue1[0] : pointValue0[0];\n      } else {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue0[0] : pointValue1[0];\n      }\n      if (dims[1] === 'y0') {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue1[1] : pointValue0[1];\n      } else {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue0[1] : pointValue1[1];\n      }\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(pointValue, dims, true);\n    } else {\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      var pt = [x, y];\n      coordSys.clampData && coordSys.clampData(pt, pt);\n      point = coordSys.dataToPoint(pt, true);\n    }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      if (isInfinity(x)) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[dims[0] === 'x0' ? 0 : 1]);\n      } else if (isInfinity(y)) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[dims[1] === 'y0' ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  return point;\n}\nexport var dimPermutations = [['x0', 'y0'], ['x1', 'y0'], ['x1', 'y1'], ['x0', 'y1']];\nvar MarkAreaView = /** @class */function (_super) {\n  __extends(MarkAreaView, _super);\n  function MarkAreaView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaView.type;\n    return _this;\n  }\n  MarkAreaView.prototype.updateTransform = function (markAreaModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var maModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markArea');\n      if (maModel) {\n        var areaData_1 = maModel.getData();\n        areaData_1.each(function (idx) {\n          var points = map(dimPermutations, function (dim) {\n            return getSingleMarkerEndPoint(areaData_1, idx, dim, seriesModel, api);\n          });\n          // Layout\n          areaData_1.setItemLayout(idx, points);\n          var el = areaData_1.getItemGraphicEl(idx);\n          el.setShape('points', points);\n        });\n      }\n    }, this);\n  };\n  MarkAreaView.prototype.renderSeries = function (seriesModel, maModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var areaGroupMap = this.markerGroupMap;\n    var polygonGroup = areaGroupMap.get(seriesId) || areaGroupMap.set(seriesId, {\n      group: new graphic.Group()\n    });\n    this.group.add(polygonGroup.group);\n    this.markKeep(polygonGroup);\n    var areaData = createList(coordSys, seriesModel, maModel);\n    // Line data for tooltip and formatter\n    maModel.setData(areaData);\n    // Update visual and layout of line\n    areaData.each(function (idx) {\n      // Layout\n      var points = map(dimPermutations, function (dim) {\n        return getSingleMarkerEndPoint(areaData, idx, dim, seriesModel, api);\n      });\n      var xAxisScale = coordSys.getAxis('x').scale;\n      var yAxisScale = coordSys.getAxis('y').scale;\n      var xAxisExtent = xAxisScale.getExtent();\n      var yAxisExtent = yAxisScale.getExtent();\n      var xPointExtent = [xAxisScale.parse(areaData.get('x0', idx)), xAxisScale.parse(areaData.get('x1', idx))];\n      var yPointExtent = [yAxisScale.parse(areaData.get('y0', idx)), yAxisScale.parse(areaData.get('y1', idx))];\n      numberUtil.asc(xPointExtent);\n      numberUtil.asc(yPointExtent);\n      var overlapped = !(xAxisExtent[0] > xPointExtent[1] || xAxisExtent[1] < xPointExtent[0] || yAxisExtent[0] > yPointExtent[1] || yAxisExtent[1] < yPointExtent[0]);\n      // If none of the area is inside coordSys, allClipped is set to be true\n      // in layout so that label will not be displayed. See #12591\n      var allClipped = !overlapped;\n      areaData.setItemLayout(idx, {\n        points: points,\n        allClipped: allClipped\n      });\n      var style = areaData.getItemModel(idx).getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n        if (isString(style.fill)) {\n          style.fill = colorUtil.modifyAlpha(style.fill, 0.4);\n        }\n      }\n      if (!style.stroke) {\n        style.stroke = color;\n      }\n      // Visual\n      areaData.setItemVisual(idx, 'style', style);\n    });\n    areaData.diff(inner(polygonGroup).data).add(function (idx) {\n      var layout = areaData.getItemLayout(idx);\n      if (!layout.allClipped) {\n        var polygon = new graphic.Polygon({\n          shape: {\n            points: layout.points\n          }\n        });\n        areaData.setItemGraphicEl(idx, polygon);\n        polygonGroup.group.add(polygon);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(oldIdx);\n      var layout = areaData.getItemLayout(newIdx);\n      if (!layout.allClipped) {\n        if (polygon) {\n          graphic.updateProps(polygon, {\n            shape: {\n              points: layout.points\n            }\n          }, maModel, newIdx);\n        } else {\n          polygon = new graphic.Polygon({\n            shape: {\n              points: layout.points\n            }\n          });\n        }\n        areaData.setItemGraphicEl(newIdx, polygon);\n        polygonGroup.group.add(polygon);\n      } else if (polygon) {\n        polygonGroup.group.remove(polygon);\n      }\n    }).remove(function (idx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(idx);\n      polygonGroup.group.remove(polygon);\n    }).execute();\n    areaData.eachItemGraphicEl(function (polygon, idx) {\n      var itemModel = areaData.getItemModel(idx);\n      var style = areaData.getItemVisual(idx, 'style');\n      polygon.useStyle(areaData.getItemVisual(idx, 'style'));\n      setLabelStyle(polygon, getLabelStatesModels(itemModel), {\n        labelFetcher: maModel,\n        labelDataIndex: idx,\n        defaultText: areaData.getName(idx) || '',\n        inheritColor: isString(style.fill) ? colorUtil.modifyAlpha(style.fill, 1) : '#000'\n      });\n      setStatesStylesFromModel(polygon, itemModel);\n      toggleHoverEmphasis(polygon, null, null, itemModel.get(['emphasis', 'disabled']));\n      getECData(polygon).dataModel = maModel;\n    });\n    inner(polygonGroup).data = areaData;\n    polygonGroup.group.silent = maModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkAreaView.type = 'markArea';\n  return MarkAreaView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, maModel) {\n  var areaData;\n  var dataDims;\n  var dims = ['x0', 'y0', 'x1', 'y1'];\n  if (coordSys) {\n    var coordDimsInfos_1 = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var data = seriesModel.getData();\n      var info = data.getDimensionInfo(data.mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n    dataDims = map(dims, function (dim, idx) {\n      return {\n        name: dim,\n        type: coordDimsInfos_1[idx % 2].type\n      };\n    });\n    areaData = new SeriesData(dataDims, maModel);\n  } else {\n    dataDims = [{\n      name: 'value',\n      type: 'float'\n    }];\n    areaData = new SeriesData(dataDims, maModel);\n  }\n  var optData = map(maModel.get('data'), curry(markAreaTransform, seriesModel, coordSys, maModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markAreaFilter, coordSys));\n  }\n  var dimValueGetter = coordSys ? function (item, dimName, dataIndex, dimIndex) {\n    // TODO should convert to ParsedValue?\n    var rawVal = item.coord[Math.floor(dimIndex / 2)][dimIndex % 2];\n    return parseDataValue(rawVal, dataDims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dataDims[dimIndex]);\n  };\n  areaData.initData(optData, null, dimValueGetter);\n  areaData.hasItemOption = true;\n  return areaData;\n}\nexport default MarkAreaView;", "map": {"version": 3, "names": ["__extends", "colorUtil", "SeriesData", "numberUtil", "graphic", "toggleHoverEmphasis", "setStatesStylesFromModel", "marker<PERSON>elper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrieve", "mergeAll", "map", "curry", "filter", "extend", "isString", "isCoordinateSystemType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "makeInner", "getVisualFromData", "setLabelStyle", "getLabelStatesModels", "getECData", "parseDataValue", "inner", "markAreaTransform", "seriesModel", "coordSys", "maModel", "item", "item0", "item1", "lt", "dataTransform", "rb", "ltCoord", "coord", "rbCoord", "Infinity", "result", "x0", "x", "y0", "y", "x1", "y1", "isInfinity", "val", "isNaN", "isFinite", "ifMarkAreaHasOnlyDim", "dimIndex", "fromCoord", "toCoord", "otherDimIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zoneFilter", "dataFilter", "getSingleMarkerEndPoint", "data", "idx", "dims", "api", "coordinateSystem", "itemModel", "getItemModel", "point", "xPx", "parsePercent", "get", "getWidth", "yPx", "getHeight", "getMarkerPosition", "pointValue0", "getV<PERSON>ues", "pointValue1", "clampPointValue0", "clampData", "clampPointValue1", "pointValue", "pt", "dataToPoint", "xAxis", "getAxis", "yAxis", "toGlobalCoord", "getExtent", "dimPermutations", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "_this", "apply", "arguments", "type", "prototype", "updateTransform", "markAreaModel", "ecModel", "eachSeries", "getMarkerModelFromSeries", "areaData_1", "getData", "each", "points", "dim", "setItemLayout", "el", "getItemGraphicEl", "setShape", "renderSeries", "seriesId", "id", "seriesData", "areaGroupMap", "markerGroupMap", "polygonGroup", "set", "group", "Group", "add", "<PERSON><PERSON><PERSON>", "areaData", "createList", "setData", "xAxisScale", "scale", "yAxisScale", "xAxisExtent", "yAxisExtent", "xPointExtent", "parse", "yPointExtent", "asc", "overlapped", "allClipped", "style", "getModel", "getItemStyle", "color", "fill", "modifyAlpha", "stroke", "setItemVisual", "diff", "layout", "getItemLayout", "polygon", "Polygon", "shape", "setItemGraphicEl", "update", "newIdx", "oldIdx", "updateProps", "remove", "execute", "eachItemGraphicEl", "getItemVisual", "useStyle", "labelFetcher", "labelDataIndex", "defaultText", "getName", "inheritColor", "dataModel", "silent", "dataDims", "coordDimsInfos_1", "dimensions", "coordDim", "info", "getDimensionInfo", "mapDimension", "name", "ordinalMeta", "optData", "dimValueGetter", "dimName", "dataIndex", "rawVal", "Math", "floor", "value", "initData", "hasItemOption"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/marker/MarkAreaView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO Optimize on polar\nimport * as colorUtil from 'zrender/lib/tool/color.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, setStatesStylesFromModel } from '../../util/states.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport { retrieve, mergeAll, map, curry, filter, extend, isString } from 'zrender/lib/core/util.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport MarkerModel from './MarkerModel.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nvar inner = makeInner();\nvar markAreaTransform = function (seriesModel, coordSys, maModel, item) {\n  // item may be null\n  var item0 = item[0];\n  var item1 = item[1];\n  if (!item0 || !item1) {\n    return;\n  }\n  var lt = markerHelper.dataTransform(seriesModel, item0);\n  var rb = markerHelper.dataTransform(seriesModel, item1);\n  // FIXME make sure lt is less than rb\n  var ltCoord = lt.coord;\n  var rbCoord = rb.coord;\n  ltCoord[0] = retrieve(ltCoord[0], -Infinity);\n  ltCoord[1] = retrieve(ltCoord[1], -Infinity);\n  rbCoord[0] = retrieve(rbCoord[0], Infinity);\n  rbCoord[1] = retrieve(rbCoord[1], Infinity);\n  // Merge option into one\n  var result = mergeAll([{}, lt, rb]);\n  result.coord = [lt.coord, rb.coord];\n  result.x0 = lt.x;\n  result.y0 = lt.y;\n  result.x1 = rb.x;\n  result.y1 = rb.y;\n  return result;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markArea has one dim\nfunction ifMarkAreaHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]);\n}\nfunction markAreaFilter(coordSys, item) {\n  var fromCoord = item.coord[0];\n  var toCoord = item.coord[1];\n  var item0 = {\n    coord: fromCoord,\n    x: item.x0,\n    y: item.y0\n  };\n  var item1 = {\n    coord: toCoord,\n    x: item.x1,\n    y: item.y1\n  };\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    // In case\n    // {\n    //  markArea: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkAreaHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkAreaHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n    // Directly returning true may also do the work,\n    // because markArea will not be shown automatically\n    // when it's not included in coordinate system.\n    // But filtering ahead can avoid keeping rendering markArea\n    // when there are too many of them.\n    return markerHelper.zoneFilter(coordSys, item0, item1);\n  }\n  return markerHelper.dataFilter(coordSys, item0) || markerHelper.dataFilter(coordSys, item1);\n}\n// dims can be ['x0', 'y0'], ['x1', 'y1'], ['x0', 'y1'], ['x1', 'y0']\nfunction getSingleMarkerEndPoint(data, idx, dims, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get(dims[0]), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get(dims[1]), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Consider the case that user input the right-bottom point first\n      // Pick the larger x and y as 'x1' and 'y1'\n      var pointValue0 = data.getValues(['x0', 'y0'], idx);\n      var pointValue1 = data.getValues(['x1', 'y1'], idx);\n      var clampPointValue0 = coordSys.clampData(pointValue0);\n      var clampPointValue1 = coordSys.clampData(pointValue1);\n      var pointValue = [];\n      if (dims[0] === 'x0') {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue1[0] : pointValue0[0];\n      } else {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue0[0] : pointValue1[0];\n      }\n      if (dims[1] === 'y0') {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue1[1] : pointValue0[1];\n      } else {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue0[1] : pointValue1[1];\n      }\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(pointValue, dims, true);\n    } else {\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      var pt = [x, y];\n      coordSys.clampData && coordSys.clampData(pt, pt);\n      point = coordSys.dataToPoint(pt, true);\n    }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      if (isInfinity(x)) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[dims[0] === 'x0' ? 0 : 1]);\n      } else if (isInfinity(y)) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[dims[1] === 'y0' ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  return point;\n}\nexport var dimPermutations = [['x0', 'y0'], ['x1', 'y0'], ['x1', 'y1'], ['x0', 'y1']];\nvar MarkAreaView = /** @class */function (_super) {\n  __extends(MarkAreaView, _super);\n  function MarkAreaView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaView.type;\n    return _this;\n  }\n  MarkAreaView.prototype.updateTransform = function (markAreaModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var maModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markArea');\n      if (maModel) {\n        var areaData_1 = maModel.getData();\n        areaData_1.each(function (idx) {\n          var points = map(dimPermutations, function (dim) {\n            return getSingleMarkerEndPoint(areaData_1, idx, dim, seriesModel, api);\n          });\n          // Layout\n          areaData_1.setItemLayout(idx, points);\n          var el = areaData_1.getItemGraphicEl(idx);\n          el.setShape('points', points);\n        });\n      }\n    }, this);\n  };\n  MarkAreaView.prototype.renderSeries = function (seriesModel, maModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var areaGroupMap = this.markerGroupMap;\n    var polygonGroup = areaGroupMap.get(seriesId) || areaGroupMap.set(seriesId, {\n      group: new graphic.Group()\n    });\n    this.group.add(polygonGroup.group);\n    this.markKeep(polygonGroup);\n    var areaData = createList(coordSys, seriesModel, maModel);\n    // Line data for tooltip and formatter\n    maModel.setData(areaData);\n    // Update visual and layout of line\n    areaData.each(function (idx) {\n      // Layout\n      var points = map(dimPermutations, function (dim) {\n        return getSingleMarkerEndPoint(areaData, idx, dim, seriesModel, api);\n      });\n      var xAxisScale = coordSys.getAxis('x').scale;\n      var yAxisScale = coordSys.getAxis('y').scale;\n      var xAxisExtent = xAxisScale.getExtent();\n      var yAxisExtent = yAxisScale.getExtent();\n      var xPointExtent = [xAxisScale.parse(areaData.get('x0', idx)), xAxisScale.parse(areaData.get('x1', idx))];\n      var yPointExtent = [yAxisScale.parse(areaData.get('y0', idx)), yAxisScale.parse(areaData.get('y1', idx))];\n      numberUtil.asc(xPointExtent);\n      numberUtil.asc(yPointExtent);\n      var overlapped = !(xAxisExtent[0] > xPointExtent[1] || xAxisExtent[1] < xPointExtent[0] || yAxisExtent[0] > yPointExtent[1] || yAxisExtent[1] < yPointExtent[0]);\n      // If none of the area is inside coordSys, allClipped is set to be true\n      // in layout so that label will not be displayed. See #12591\n      var allClipped = !overlapped;\n      areaData.setItemLayout(idx, {\n        points: points,\n        allClipped: allClipped\n      });\n      var style = areaData.getItemModel(idx).getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n        if (isString(style.fill)) {\n          style.fill = colorUtil.modifyAlpha(style.fill, 0.4);\n        }\n      }\n      if (!style.stroke) {\n        style.stroke = color;\n      }\n      // Visual\n      areaData.setItemVisual(idx, 'style', style);\n    });\n    areaData.diff(inner(polygonGroup).data).add(function (idx) {\n      var layout = areaData.getItemLayout(idx);\n      if (!layout.allClipped) {\n        var polygon = new graphic.Polygon({\n          shape: {\n            points: layout.points\n          }\n        });\n        areaData.setItemGraphicEl(idx, polygon);\n        polygonGroup.group.add(polygon);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(oldIdx);\n      var layout = areaData.getItemLayout(newIdx);\n      if (!layout.allClipped) {\n        if (polygon) {\n          graphic.updateProps(polygon, {\n            shape: {\n              points: layout.points\n            }\n          }, maModel, newIdx);\n        } else {\n          polygon = new graphic.Polygon({\n            shape: {\n              points: layout.points\n            }\n          });\n        }\n        areaData.setItemGraphicEl(newIdx, polygon);\n        polygonGroup.group.add(polygon);\n      } else if (polygon) {\n        polygonGroup.group.remove(polygon);\n      }\n    }).remove(function (idx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(idx);\n      polygonGroup.group.remove(polygon);\n    }).execute();\n    areaData.eachItemGraphicEl(function (polygon, idx) {\n      var itemModel = areaData.getItemModel(idx);\n      var style = areaData.getItemVisual(idx, 'style');\n      polygon.useStyle(areaData.getItemVisual(idx, 'style'));\n      setLabelStyle(polygon, getLabelStatesModels(itemModel), {\n        labelFetcher: maModel,\n        labelDataIndex: idx,\n        defaultText: areaData.getName(idx) || '',\n        inheritColor: isString(style.fill) ? colorUtil.modifyAlpha(style.fill, 1) : '#000'\n      });\n      setStatesStylesFromModel(polygon, itemModel);\n      toggleHoverEmphasis(polygon, null, null, itemModel.get(['emphasis', 'disabled']));\n      getECData(polygon).dataModel = maModel;\n    });\n    inner(polygonGroup).data = areaData;\n    polygonGroup.group.silent = maModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkAreaView.type = 'markArea';\n  return MarkAreaView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, maModel) {\n  var areaData;\n  var dataDims;\n  var dims = ['x0', 'y0', 'x1', 'y1'];\n  if (coordSys) {\n    var coordDimsInfos_1 = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var data = seriesModel.getData();\n      var info = data.getDimensionInfo(data.mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n    dataDims = map(dims, function (dim, idx) {\n      return {\n        name: dim,\n        type: coordDimsInfos_1[idx % 2].type\n      };\n    });\n    areaData = new SeriesData(dataDims, maModel);\n  } else {\n    dataDims = [{\n      name: 'value',\n      type: 'float'\n    }];\n    areaData = new SeriesData(dataDims, maModel);\n  }\n  var optData = map(maModel.get('data'), curry(markAreaTransform, seriesModel, coordSys, maModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markAreaFilter, coordSys));\n  }\n  var dimValueGetter = coordSys ? function (item, dimName, dataIndex, dimIndex) {\n    // TODO should convert to ParsedValue?\n    var rawVal = item.coord[Math.floor(dimIndex / 2)][dimIndex % 2];\n    return parseDataValue(rawVal, dataDims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dataDims[dimIndex]);\n  };\n  areaData.initData(optData, null, dimValueGetter);\n  areaData.hasItemOption = true;\n  return areaData;\n}\nexport default MarkAreaView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,EAAEC,wBAAwB,QAAQ,sBAAsB;AACpF,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,0BAA0B;AACnG,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,cAAc,QAAQ,sCAAsC;AACrE,IAAIC,KAAK,GAAGN,SAAS,CAAC,CAAC;AACvB,IAAIO,iBAAiB,GAAG,SAAAA,CAAUC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACtE;EACA,IAAIC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;EACnB,IAAIE,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI,CAACC,KAAK,IAAI,CAACC,KAAK,EAAE;IACpB;EACF;EACA,IAAIC,EAAE,GAAGzB,YAAY,CAAC0B,aAAa,CAACP,WAAW,EAAEI,KAAK,CAAC;EACvD,IAAII,EAAE,GAAG3B,YAAY,CAAC0B,aAAa,CAACP,WAAW,EAAEK,KAAK,CAAC;EACvD;EACA,IAAII,OAAO,GAAGH,EAAE,CAACI,KAAK;EACtB,IAAIC,OAAO,GAAGH,EAAE,CAACE,KAAK;EACtBD,OAAO,CAAC,CAAC,CAAC,GAAG1B,QAAQ,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAE,CAACG,QAAQ,CAAC;EAC5CH,OAAO,CAAC,CAAC,CAAC,GAAG1B,QAAQ,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAE,CAACG,QAAQ,CAAC;EAC5CD,OAAO,CAAC,CAAC,CAAC,GAAG5B,QAAQ,CAAC4B,OAAO,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;EAC3CD,OAAO,CAAC,CAAC,CAAC,GAAG5B,QAAQ,CAAC4B,OAAO,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;EAC3C;EACA,IAAIC,MAAM,GAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEsB,EAAE,EAAEE,EAAE,CAAC,CAAC;EACnCK,MAAM,CAACH,KAAK,GAAG,CAACJ,EAAE,CAACI,KAAK,EAAEF,EAAE,CAACE,KAAK,CAAC;EACnCG,MAAM,CAACC,EAAE,GAAGR,EAAE,CAACS,CAAC;EAChBF,MAAM,CAACG,EAAE,GAAGV,EAAE,CAACW,CAAC;EAChBJ,MAAM,CAACK,EAAE,GAAGV,EAAE,CAACO,CAAC;EAChBF,MAAM,CAACM,EAAE,GAAGX,EAAE,CAACS,CAAC;EAChB,OAAOJ,MAAM;AACf,CAAC;AACD,SAASO,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAO,CAACC,KAAK,CAACD,GAAG,CAAC,IAAI,CAACE,QAAQ,CAACF,GAAG,CAAC;AACtC;AACA;AACA,SAASG,oBAAoBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE1B,QAAQ,EAAE;EACpE,IAAI2B,aAAa,GAAG,CAAC,GAAGH,QAAQ;EAChC,OAAOL,UAAU,CAACM,SAAS,CAACE,aAAa,CAAC,CAAC,IAAIR,UAAU,CAACO,OAAO,CAACC,aAAa,CAAC,CAAC;AACnF;AACA,SAASC,cAAcA,CAAC5B,QAAQ,EAAEE,IAAI,EAAE;EACtC,IAAIuB,SAAS,GAAGvB,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC;EAC7B,IAAIiB,OAAO,GAAGxB,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC;EAC3B,IAAIN,KAAK,GAAG;IACVM,KAAK,EAAEgB,SAAS;IAChBX,CAAC,EAAEZ,IAAI,CAACW,EAAE;IACVG,CAAC,EAAEd,IAAI,CAACa;EACV,CAAC;EACD,IAAIX,KAAK,GAAG;IACVK,KAAK,EAAEiB,OAAO;IACdZ,CAAC,EAAEZ,IAAI,CAACe,EAAE;IACVD,CAAC,EAAEd,IAAI,CAACgB;EACV,CAAC;EACD,IAAI7B,sBAAsB,CAACW,QAAQ,EAAE,aAAa,CAAC,EAAE;IACnD;IACA;IACA;IACA;IACA;IACA;IACA,IAAIyB,SAAS,IAAIC,OAAO,KAAKH,oBAAoB,CAAC,CAAC,EAAEE,SAAS,EAAEC,OAAO,EAAE1B,QAAQ,CAAC,IAAIuB,oBAAoB,CAAC,CAAC,EAAEE,SAAS,EAAEC,OAAO,EAAE1B,QAAQ,CAAC,CAAC,EAAE;MAC5I,OAAO,IAAI;IACb;IACA;IACA;IACA;IACA;IACA;IACA,OAAOpB,YAAY,CAACiD,UAAU,CAAC7B,QAAQ,EAAEG,KAAK,EAAEC,KAAK,CAAC;EACxD;EACA,OAAOxB,YAAY,CAACkD,UAAU,CAAC9B,QAAQ,EAAEG,KAAK,CAAC,IAAIvB,YAAY,CAACkD,UAAU,CAAC9B,QAAQ,EAAEI,KAAK,CAAC;AAC7F;AACA;AACA,SAAS2B,uBAAuBA,CAACC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEnC,WAAW,EAAEoC,GAAG,EAAE;EAClE,IAAInC,QAAQ,GAAGD,WAAW,CAACqC,gBAAgB;EAC3C,IAAIC,SAAS,GAAGL,IAAI,CAACM,YAAY,CAACL,GAAG,CAAC;EACtC,IAAIM,KAAK;EACT,IAAIC,GAAG,GAAGhE,UAAU,CAACiE,YAAY,CAACJ,SAAS,CAACK,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC;EACzE,IAAIC,GAAG,GAAGpE,UAAU,CAACiE,YAAY,CAACJ,SAAS,CAACK,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAG,CAACU,SAAS,CAAC,CAAC,CAAC;EAC1E,IAAI,CAACxB,KAAK,CAACmB,GAAG,CAAC,IAAI,CAACnB,KAAK,CAACuB,GAAG,CAAC,EAAE;IAC9BL,KAAK,GAAG,CAACC,GAAG,EAAEI,GAAG,CAAC;EACpB,CAAC,MAAM;IACL;IACA,IAAI7C,WAAW,CAAC+C,iBAAiB,EAAE;MACjC;MACA;MACA,IAAIC,WAAW,GAAGf,IAAI,CAACgB,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEf,GAAG,CAAC;MACnD,IAAIgB,WAAW,GAAGjB,IAAI,CAACgB,SAAS,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEf,GAAG,CAAC;MACnD,IAAIiB,gBAAgB,GAAGlD,QAAQ,CAACmD,SAAS,CAACJ,WAAW,CAAC;MACtD,IAAIK,gBAAgB,GAAGpD,QAAQ,CAACmD,SAAS,CAACF,WAAW,CAAC;MACtD,IAAII,UAAU,GAAG,EAAE;MACnB,IAAInB,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACpBmB,UAAU,CAAC,CAAC,CAAC,GAAGH,gBAAgB,CAAC,CAAC,CAAC,GAAGE,gBAAgB,CAAC,CAAC,CAAC,GAAGH,WAAW,CAAC,CAAC,CAAC,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC7F,CAAC,MAAM;QACLM,UAAU,CAAC,CAAC,CAAC,GAAGH,gBAAgB,CAAC,CAAC,CAAC,GAAGE,gBAAgB,CAAC,CAAC,CAAC,GAAGL,WAAW,CAAC,CAAC,CAAC,GAAGE,WAAW,CAAC,CAAC,CAAC;MAC7F;MACA,IAAIf,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACpBmB,UAAU,CAAC,CAAC,CAAC,GAAGH,gBAAgB,CAAC,CAAC,CAAC,GAAGE,gBAAgB,CAAC,CAAC,CAAC,GAAGH,WAAW,CAAC,CAAC,CAAC,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC7F,CAAC,MAAM;QACLM,UAAU,CAAC,CAAC,CAAC,GAAGH,gBAAgB,CAAC,CAAC,CAAC,GAAGE,gBAAgB,CAAC,CAAC,CAAC,GAAGL,WAAW,CAAC,CAAC,CAAC,GAAGE,WAAW,CAAC,CAAC,CAAC;MAC7F;MACA;MACAV,KAAK,GAAGxC,WAAW,CAAC+C,iBAAiB,CAACO,UAAU,EAAEnB,IAAI,EAAE,IAAI,CAAC;IAC/D,CAAC,MAAM;MACL,IAAIpB,CAAC,GAAGkB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAC9B,IAAIjB,CAAC,GAAGgB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAC9B,IAAIqB,EAAE,GAAG,CAACxC,CAAC,EAAEE,CAAC,CAAC;MACfhB,QAAQ,CAACmD,SAAS,IAAInD,QAAQ,CAACmD,SAAS,CAACG,EAAE,EAAEA,EAAE,CAAC;MAChDf,KAAK,GAAGvC,QAAQ,CAACuD,WAAW,CAACD,EAAE,EAAE,IAAI,CAAC;IACxC;IACA,IAAIjE,sBAAsB,CAACW,QAAQ,EAAE,aAAa,CAAC,EAAE;MACnD;MACA,IAAIwD,KAAK,GAAGxD,QAAQ,CAACyD,OAAO,CAAC,GAAG,CAAC;MACjC,IAAIC,KAAK,GAAG1D,QAAQ,CAACyD,OAAO,CAAC,GAAG,CAAC;MACjC,IAAI3C,CAAC,GAAGkB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAC9B,IAAIjB,CAAC,GAAGgB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAC9B,IAAId,UAAU,CAACL,CAAC,CAAC,EAAE;QACjByB,KAAK,CAAC,CAAC,CAAC,GAAGiB,KAAK,CAACG,aAAa,CAACH,KAAK,CAACI,SAAS,CAAC,CAAC,CAAC1B,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7E,CAAC,MAAM,IAAIf,UAAU,CAACH,CAAC,CAAC,EAAE;QACxBuB,KAAK,CAAC,CAAC,CAAC,GAAGmB,KAAK,CAACC,aAAa,CAACD,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC1B,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7E;IACF;IACA;IACA,IAAI,CAACb,KAAK,CAACmB,GAAG,CAAC,EAAE;MACfD,KAAK,CAAC,CAAC,CAAC,GAAGC,GAAG;IAChB;IACA,IAAI,CAACnB,KAAK,CAACuB,GAAG,CAAC,EAAE;MACfL,KAAK,CAAC,CAAC,CAAC,GAAGK,GAAG;IAChB;EACF;EACA,OAAOL,KAAK;AACd;AACA,OAAO,IAAIsB,eAAe,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACrF,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChD1F,SAAS,CAACyF,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EACAF,YAAY,CAACM,SAAS,CAACC,eAAe,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAEpC,GAAG,EAAE;IAC9EoC,OAAO,CAACC,UAAU,CAAC,UAAUzE,WAAW,EAAE;MACxC,IAAIE,OAAO,GAAGX,WAAW,CAACmF,wBAAwB,CAAC1E,WAAW,EAAE,UAAU,CAAC;MAC3E,IAAIE,OAAO,EAAE;QACX,IAAIyE,UAAU,GAAGzE,OAAO,CAAC0E,OAAO,CAAC,CAAC;QAClCD,UAAU,CAACE,IAAI,CAAC,UAAU3C,GAAG,EAAE;UAC7B,IAAI4C,MAAM,GAAG7F,GAAG,CAAC6E,eAAe,EAAE,UAAUiB,GAAG,EAAE;YAC/C,OAAO/C,uBAAuB,CAAC2C,UAAU,EAAEzC,GAAG,EAAE6C,GAAG,EAAE/E,WAAW,EAAEoC,GAAG,CAAC;UACxE,CAAC,CAAC;UACF;UACAuC,UAAU,CAACK,aAAa,CAAC9C,GAAG,EAAE4C,MAAM,CAAC;UACrC,IAAIG,EAAE,GAAGN,UAAU,CAACO,gBAAgB,CAAChD,GAAG,CAAC;UACzC+C,EAAE,CAACE,QAAQ,CAAC,QAAQ,EAAEL,MAAM,CAAC;QAC/B,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDf,YAAY,CAACM,SAAS,CAACe,YAAY,GAAG,UAAUpF,WAAW,EAAEE,OAAO,EAAEsE,OAAO,EAAEpC,GAAG,EAAE;IAClF,IAAInC,QAAQ,GAAGD,WAAW,CAACqC,gBAAgB;IAC3C,IAAIgD,QAAQ,GAAGrF,WAAW,CAACsF,EAAE;IAC7B,IAAIC,UAAU,GAAGvF,WAAW,CAAC4E,OAAO,CAAC,CAAC;IACtC,IAAIY,YAAY,GAAG,IAAI,CAACC,cAAc;IACtC,IAAIC,YAAY,GAAGF,YAAY,CAAC7C,GAAG,CAAC0C,QAAQ,CAAC,IAAIG,YAAY,CAACG,GAAG,CAACN,QAAQ,EAAE;MAC1EO,KAAK,EAAE,IAAIlH,OAAO,CAACmH,KAAK,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACD,KAAK,CAACE,GAAG,CAACJ,YAAY,CAACE,KAAK,CAAC;IAClC,IAAI,CAACG,QAAQ,CAACL,YAAY,CAAC;IAC3B,IAAIM,QAAQ,GAAGC,UAAU,CAAChG,QAAQ,EAAED,WAAW,EAAEE,OAAO,CAAC;IACzD;IACAA,OAAO,CAACgG,OAAO,CAACF,QAAQ,CAAC;IACzB;IACAA,QAAQ,CAACnB,IAAI,CAAC,UAAU3C,GAAG,EAAE;MAC3B;MACA,IAAI4C,MAAM,GAAG7F,GAAG,CAAC6E,eAAe,EAAE,UAAUiB,GAAG,EAAE;QAC/C,OAAO/C,uBAAuB,CAACgE,QAAQ,EAAE9D,GAAG,EAAE6C,GAAG,EAAE/E,WAAW,EAAEoC,GAAG,CAAC;MACtE,CAAC,CAAC;MACF,IAAI+D,UAAU,GAAGlG,QAAQ,CAACyD,OAAO,CAAC,GAAG,CAAC,CAAC0C,KAAK;MAC5C,IAAIC,UAAU,GAAGpG,QAAQ,CAACyD,OAAO,CAAC,GAAG,CAAC,CAAC0C,KAAK;MAC5C,IAAIE,WAAW,GAAGH,UAAU,CAACtC,SAAS,CAAC,CAAC;MACxC,IAAI0C,WAAW,GAAGF,UAAU,CAACxC,SAAS,CAAC,CAAC;MACxC,IAAI2C,YAAY,GAAG,CAACL,UAAU,CAACM,KAAK,CAACT,QAAQ,CAACrD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,EAAEiE,UAAU,CAACM,KAAK,CAACT,QAAQ,CAACrD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,CAAC;MACzG,IAAIwE,YAAY,GAAG,CAACL,UAAU,CAACI,KAAK,CAACT,QAAQ,CAACrD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,EAAEmE,UAAU,CAACI,KAAK,CAACT,QAAQ,CAACrD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,CAAC;MACzGzD,UAAU,CAACkI,GAAG,CAACH,YAAY,CAAC;MAC5B/H,UAAU,CAACkI,GAAG,CAACD,YAAY,CAAC;MAC5B,IAAIE,UAAU,GAAG,EAAEN,WAAW,CAAC,CAAC,CAAC,GAAGE,YAAY,CAAC,CAAC,CAAC,IAAIF,WAAW,CAAC,CAAC,CAAC,GAAGE,YAAY,CAAC,CAAC,CAAC,IAAID,WAAW,CAAC,CAAC,CAAC,GAAGG,YAAY,CAAC,CAAC,CAAC,IAAIH,WAAW,CAAC,CAAC,CAAC,GAAGG,YAAY,CAAC,CAAC,CAAC,CAAC;MAChK;MACA;MACA,IAAIG,UAAU,GAAG,CAACD,UAAU;MAC5BZ,QAAQ,CAAChB,aAAa,CAAC9C,GAAG,EAAE;QAC1B4C,MAAM,EAAEA,MAAM;QACd+B,UAAU,EAAEA;MACd,CAAC,CAAC;MACF,IAAIC,KAAK,GAAGd,QAAQ,CAACzD,YAAY,CAACL,GAAG,CAAC,CAAC6E,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MAC3E,IAAIC,KAAK,GAAGxH,iBAAiB,CAAC8F,UAAU,EAAE,OAAO,CAAC;MAClD,IAAI,CAACuB,KAAK,CAACI,IAAI,EAAE;QACfJ,KAAK,CAACI,IAAI,GAAGD,KAAK;QAClB,IAAI5H,QAAQ,CAACyH,KAAK,CAACI,IAAI,CAAC,EAAE;UACxBJ,KAAK,CAACI,IAAI,GAAG3I,SAAS,CAAC4I,WAAW,CAACL,KAAK,CAACI,IAAI,EAAE,GAAG,CAAC;QACrD;MACF;MACA,IAAI,CAACJ,KAAK,CAACM,MAAM,EAAE;QACjBN,KAAK,CAACM,MAAM,GAAGH,KAAK;MACtB;MACA;MACAjB,QAAQ,CAACqB,aAAa,CAACnF,GAAG,EAAE,OAAO,EAAE4E,KAAK,CAAC;IAC7C,CAAC,CAAC;IACFd,QAAQ,CAACsB,IAAI,CAACxH,KAAK,CAAC4F,YAAY,CAAC,CAACzD,IAAI,CAAC,CAAC6D,GAAG,CAAC,UAAU5D,GAAG,EAAE;MACzD,IAAIqF,MAAM,GAAGvB,QAAQ,CAACwB,aAAa,CAACtF,GAAG,CAAC;MACxC,IAAI,CAACqF,MAAM,CAACV,UAAU,EAAE;QACtB,IAAIY,OAAO,GAAG,IAAI/I,OAAO,CAACgJ,OAAO,CAAC;UAChCC,KAAK,EAAE;YACL7C,MAAM,EAAEyC,MAAM,CAACzC;UACjB;QACF,CAAC,CAAC;QACFkB,QAAQ,CAAC4B,gBAAgB,CAAC1F,GAAG,EAAEuF,OAAO,CAAC;QACvC/B,YAAY,CAACE,KAAK,CAACE,GAAG,CAAC2B,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,CAACI,MAAM,CAAC,UAAUC,MAAM,EAAEC,MAAM,EAAE;MAClC,IAAIN,OAAO,GAAG3H,KAAK,CAAC4F,YAAY,CAAC,CAACzD,IAAI,CAACiD,gBAAgB,CAAC6C,MAAM,CAAC;MAC/D,IAAIR,MAAM,GAAGvB,QAAQ,CAACwB,aAAa,CAACM,MAAM,CAAC;MAC3C,IAAI,CAACP,MAAM,CAACV,UAAU,EAAE;QACtB,IAAIY,OAAO,EAAE;UACX/I,OAAO,CAACsJ,WAAW,CAACP,OAAO,EAAE;YAC3BE,KAAK,EAAE;cACL7C,MAAM,EAAEyC,MAAM,CAACzC;YACjB;UACF,CAAC,EAAE5E,OAAO,EAAE4H,MAAM,CAAC;QACrB,CAAC,MAAM;UACLL,OAAO,GAAG,IAAI/I,OAAO,CAACgJ,OAAO,CAAC;YAC5BC,KAAK,EAAE;cACL7C,MAAM,EAAEyC,MAAM,CAACzC;YACjB;UACF,CAAC,CAAC;QACJ;QACAkB,QAAQ,CAAC4B,gBAAgB,CAACE,MAAM,EAAEL,OAAO,CAAC;QAC1C/B,YAAY,CAACE,KAAK,CAACE,GAAG,CAAC2B,OAAO,CAAC;MACjC,CAAC,MAAM,IAAIA,OAAO,EAAE;QAClB/B,YAAY,CAACE,KAAK,CAACqC,MAAM,CAACR,OAAO,CAAC;MACpC;IACF,CAAC,CAAC,CAACQ,MAAM,CAAC,UAAU/F,GAAG,EAAE;MACvB,IAAIuF,OAAO,GAAG3H,KAAK,CAAC4F,YAAY,CAAC,CAACzD,IAAI,CAACiD,gBAAgB,CAAChD,GAAG,CAAC;MAC5DwD,YAAY,CAACE,KAAK,CAACqC,MAAM,CAACR,OAAO,CAAC;IACpC,CAAC,CAAC,CAACS,OAAO,CAAC,CAAC;IACZlC,QAAQ,CAACmC,iBAAiB,CAAC,UAAUV,OAAO,EAAEvF,GAAG,EAAE;MACjD,IAAII,SAAS,GAAG0D,QAAQ,CAACzD,YAAY,CAACL,GAAG,CAAC;MAC1C,IAAI4E,KAAK,GAAGd,QAAQ,CAACoC,aAAa,CAAClG,GAAG,EAAE,OAAO,CAAC;MAChDuF,OAAO,CAACY,QAAQ,CAACrC,QAAQ,CAACoC,aAAa,CAAClG,GAAG,EAAE,OAAO,CAAC,CAAC;MACtDxC,aAAa,CAAC+H,OAAO,EAAE9H,oBAAoB,CAAC2C,SAAS,CAAC,EAAE;QACtDgG,YAAY,EAAEpI,OAAO;QACrBqI,cAAc,EAAErG,GAAG;QACnBsG,WAAW,EAAExC,QAAQ,CAACyC,OAAO,CAACvG,GAAG,CAAC,IAAI,EAAE;QACxCwG,YAAY,EAAErJ,QAAQ,CAACyH,KAAK,CAACI,IAAI,CAAC,GAAG3I,SAAS,CAAC4I,WAAW,CAACL,KAAK,CAACI,IAAI,EAAE,CAAC,CAAC,GAAG;MAC9E,CAAC,CAAC;MACFtI,wBAAwB,CAAC6I,OAAO,EAAEnF,SAAS,CAAC;MAC5C3D,mBAAmB,CAAC8I,OAAO,EAAE,IAAI,EAAE,IAAI,EAAEnF,SAAS,CAACK,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;MACjF/C,SAAS,CAAC6H,OAAO,CAAC,CAACkB,SAAS,GAAGzI,OAAO;IACxC,CAAC,CAAC;IACFJ,KAAK,CAAC4F,YAAY,CAAC,CAACzD,IAAI,GAAG+D,QAAQ;IACnCN,YAAY,CAACE,KAAK,CAACgD,MAAM,GAAG1I,OAAO,CAACyC,GAAG,CAAC,QAAQ,CAAC,IAAI3C,WAAW,CAAC2C,GAAG,CAAC,QAAQ,CAAC;EAChF,CAAC;EACDoB,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAACjF,UAAU,CAAC;AACb,SAASmH,UAAUA,CAAChG,QAAQ,EAAED,WAAW,EAAEE,OAAO,EAAE;EAClD,IAAI8F,QAAQ;EACZ,IAAI6C,QAAQ;EACZ,IAAI1G,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnC,IAAIlC,QAAQ,EAAE;IACZ,IAAI6I,gBAAgB,GAAG7J,GAAG,CAACgB,QAAQ,IAAIA,QAAQ,CAAC8I,UAAU,EAAE,UAAUC,QAAQ,EAAE;MAC9E,IAAI/G,IAAI,GAAGjC,WAAW,CAAC4E,OAAO,CAAC,CAAC;MAChC,IAAIqE,IAAI,GAAGhH,IAAI,CAACiH,gBAAgB,CAACjH,IAAI,CAACkH,YAAY,CAACH,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;MACnE;MACA,OAAO5J,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE6J,IAAI,CAAC,EAAE;QAC9BG,IAAI,EAAEJ,QAAQ;QACd;QACAK,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IACFR,QAAQ,GAAG5J,GAAG,CAACkD,IAAI,EAAE,UAAU4C,GAAG,EAAE7C,GAAG,EAAE;MACvC,OAAO;QACLkH,IAAI,EAAErE,GAAG;QACTX,IAAI,EAAE0E,gBAAgB,CAAC5G,GAAG,GAAG,CAAC,CAAC,CAACkC;MAClC,CAAC;IACH,CAAC,CAAC;IACF4B,QAAQ,GAAG,IAAIxH,UAAU,CAACqK,QAAQ,EAAE3I,OAAO,CAAC;EAC9C,CAAC,MAAM;IACL2I,QAAQ,GAAG,CAAC;MACVO,IAAI,EAAE,OAAO;MACbhF,IAAI,EAAE;IACR,CAAC,CAAC;IACF4B,QAAQ,GAAG,IAAIxH,UAAU,CAACqK,QAAQ,EAAE3I,OAAO,CAAC;EAC9C;EACA,IAAIoJ,OAAO,GAAGrK,GAAG,CAACiB,OAAO,CAACyC,GAAG,CAAC,MAAM,CAAC,EAAEzD,KAAK,CAACa,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAChG,IAAID,QAAQ,EAAE;IACZqJ,OAAO,GAAGnK,MAAM,CAACmK,OAAO,EAAEpK,KAAK,CAAC2C,cAAc,EAAE5B,QAAQ,CAAC,CAAC;EAC5D;EACA,IAAIsJ,cAAc,GAAGtJ,QAAQ,GAAG,UAAUE,IAAI,EAAEqJ,OAAO,EAAEC,SAAS,EAAEhI,QAAQ,EAAE;IAC5E;IACA,IAAIiI,MAAM,GAAGvJ,IAAI,CAACO,KAAK,CAACiJ,IAAI,CAACC,KAAK,CAACnI,QAAQ,GAAG,CAAC,CAAC,CAAC,CAACA,QAAQ,GAAG,CAAC,CAAC;IAC/D,OAAO5B,cAAc,CAAC6J,MAAM,EAAEb,QAAQ,CAACpH,QAAQ,CAAC,CAAC;EACnD,CAAC,GAAG,UAAUtB,IAAI,EAAEqJ,OAAO,EAAEC,SAAS,EAAEhI,QAAQ,EAAE;IAChD,OAAO5B,cAAc,CAACM,IAAI,CAAC0J,KAAK,EAAEhB,QAAQ,CAACpH,QAAQ,CAAC,CAAC;EACvD,CAAC;EACDuE,QAAQ,CAAC8D,QAAQ,CAACR,OAAO,EAAE,IAAI,EAAEC,cAAc,CAAC;EAChDvD,QAAQ,CAAC+D,aAAa,GAAG,IAAI;EAC7B,OAAO/D,QAAQ;AACjB;AACA,eAAejC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}