{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { SERIES_LAYOUT_BY_COLUMN, SOURCE_FORMAT_OBJECT_ROWS, SOURCE_FORMAT_ARRAY_ROWS } from '../../util/types.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { createHashMap, bind, each, hasOwn, map, clone, isObject, extend, isNumber } from 'zrender/lib/core/util.js';\nimport { getRawSourceItemGetter, getRawSourceDataCounter, getRawSourceValueGetter } from './dataProvider.js';\nimport { parseDataValue } from './dataValueHelper.js';\nimport { log, makePrintable, throwError } from '../../util/log.js';\nimport { createSource, detectSourceFormat } from '../Source.js';\n/**\r\n * TODO: disable writable.\r\n * This structure will be exposed to users.\r\n */\nvar ExternalSource = /** @class */function () {\n  function ExternalSource() {}\n  ExternalSource.prototype.getRawData = function () {\n    // Only built-in transform available.\n    throw new Error('not supported');\n  };\n  ExternalSource.prototype.getRawDataItem = function (dataIndex) {\n    // Only built-in transform available.\n    throw new Error('not supported');\n  };\n  ExternalSource.prototype.cloneRawData = function () {\n    return;\n  };\n  /**\r\n   * @return If dimension not found, return null/undefined.\r\n   */\n  ExternalSource.prototype.getDimensionInfo = function (dim) {\n    return;\n  };\n  /**\r\n   * dimensions defined if and only if either:\r\n   * (a) dataset.dimensions are declared.\r\n   * (b) dataset data include dimensions definitions in data (detected or via specified `sourceHeader`).\r\n   * If dimensions are defined, `dimensionInfoAll` is corresponding to\r\n   * the defined dimensions.\r\n   * Otherwise, `dimensionInfoAll` is determined by data columns.\r\n   * @return Always return an array (even empty array).\r\n   */\n  ExternalSource.prototype.cloneAllDimensionInfo = function () {\n    return;\n  };\n  ExternalSource.prototype.count = function () {\n    return;\n  };\n  /**\r\n   * Only support by dimension index.\r\n   * No need to support by dimension name in transform function,\r\n   * because transform function is not case-specific, no need to use name literally.\r\n   */\n  ExternalSource.prototype.retrieveValue = function (dataIndex, dimIndex) {\n    return;\n  };\n  ExternalSource.prototype.retrieveValueFromItem = function (dataItem, dimIndex) {\n    return;\n  };\n  ExternalSource.prototype.convertValue = function (rawVal, dimInfo) {\n    return parseDataValue(rawVal, dimInfo);\n  };\n  return ExternalSource;\n}();\nexport { ExternalSource };\nfunction createExternalSource(internalSource, externalTransform) {\n  var extSource = new ExternalSource();\n  var data = internalSource.data;\n  var sourceFormat = extSource.sourceFormat = internalSource.sourceFormat;\n  var sourceHeaderCount = internalSource.startIndex;\n  var errMsg = '';\n  if (internalSource.seriesLayoutBy !== SERIES_LAYOUT_BY_COLUMN) {\n    // For the logic simplicity in transformer, only 'culumn' is\n    // supported in data transform. Otherwise, the `dimensionsDefine`\n    // might be detected by 'row', which probably confuses users.\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`seriesLayoutBy` of upstream dataset can only be \"column\" in data transform.';\n    }\n    throwError(errMsg);\n  }\n  // [MEMO]\n  // Create a new dimensions structure for exposing.\n  // Do not expose all dimension info to users directly.\n  // Because the dimension is probably auto detected from data and not might reliable.\n  // Should not lead the transformers to think that is reliable and return it.\n  // See [DIMENSION_INHERIT_RULE] in `sourceManager.ts`.\n  var dimensions = [];\n  var dimsByName = {};\n  var dimsDef = internalSource.dimensionsDefine;\n  if (dimsDef) {\n    each(dimsDef, function (dimDef, idx) {\n      var name = dimDef.name;\n      var dimDefExt = {\n        index: idx,\n        name: name,\n        displayName: dimDef.displayName\n      };\n      dimensions.push(dimDefExt);\n      // Users probably do not specify dimension name. For simplicity, data transform\n      // does not generate dimension name.\n      if (name != null) {\n        // Dimension name should not be duplicated.\n        // For simplicity, data transform forbids name duplication, do not generate\n        // new name like module `completeDimensions.ts` did, but just tell users.\n        var errMsg_1 = '';\n        if (hasOwn(dimsByName, name)) {\n          if (process.env.NODE_ENV !== 'production') {\n            errMsg_1 = 'dimension name \"' + name + '\" duplicated.';\n          }\n          throwError(errMsg_1);\n        }\n        dimsByName[name] = dimDefExt;\n      }\n    });\n  }\n  // If dimension definitions are not defined and can not be detected.\n  // e.g., pure data `[[11, 22], ...]`.\n  else {\n    for (var i = 0; i < internalSource.dimensionsDetectedCount || 0; i++) {\n      // Do not generete name or anything others. The consequence process in\n      // `transform` or `series` probably have there own name generation strategry.\n      dimensions.push({\n        index: i\n      });\n    }\n  }\n  // Implement public methods:\n  var rawItemGetter = getRawSourceItemGetter(sourceFormat, SERIES_LAYOUT_BY_COLUMN);\n  if (externalTransform.__isBuiltIn) {\n    extSource.getRawDataItem = function (dataIndex) {\n      return rawItemGetter(data, sourceHeaderCount, dimensions, dataIndex);\n    };\n    extSource.getRawData = bind(getRawData, null, internalSource);\n  }\n  extSource.cloneRawData = bind(cloneRawData, null, internalSource);\n  var rawCounter = getRawSourceDataCounter(sourceFormat, SERIES_LAYOUT_BY_COLUMN);\n  extSource.count = bind(rawCounter, null, data, sourceHeaderCount, dimensions);\n  var rawValueGetter = getRawSourceValueGetter(sourceFormat);\n  extSource.retrieveValue = function (dataIndex, dimIndex) {\n    var rawItem = rawItemGetter(data, sourceHeaderCount, dimensions, dataIndex);\n    return retrieveValueFromItem(rawItem, dimIndex);\n  };\n  var retrieveValueFromItem = extSource.retrieveValueFromItem = function (dataItem, dimIndex) {\n    if (dataItem == null) {\n      return;\n    }\n    var dimDef = dimensions[dimIndex];\n    // When `dimIndex` is `null`, `rawValueGetter` return the whole item.\n    if (dimDef) {\n      return rawValueGetter(dataItem, dimIndex, dimDef.name);\n    }\n  };\n  extSource.getDimensionInfo = bind(getDimensionInfo, null, dimensions, dimsByName);\n  extSource.cloneAllDimensionInfo = bind(cloneAllDimensionInfo, null, dimensions);\n  return extSource;\n}\nfunction getRawData(upstream) {\n  var sourceFormat = upstream.sourceFormat;\n  if (!isSupportedSourceFormat(sourceFormat)) {\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`getRawData` is not supported in source format ' + sourceFormat;\n    }\n    throwError(errMsg);\n  }\n  return upstream.data;\n}\nfunction cloneRawData(upstream) {\n  var sourceFormat = upstream.sourceFormat;\n  var data = upstream.data;\n  if (!isSupportedSourceFormat(sourceFormat)) {\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`cloneRawData` is not supported in source format ' + sourceFormat;\n    }\n    throwError(errMsg);\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var result = [];\n    for (var i = 0, len = data.length; i < len; i++) {\n      // Not strictly clone for performance\n      result.push(data[i].slice());\n    }\n    return result;\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    var result = [];\n    for (var i = 0, len = data.length; i < len; i++) {\n      // Not strictly clone for performance\n      result.push(extend({}, data[i]));\n    }\n    return result;\n  }\n}\nfunction getDimensionInfo(dimensions, dimsByName, dim) {\n  if (dim == null) {\n    return;\n  }\n  // Keep the same logic as `List::getDimension` did.\n  if (isNumber(dim)\n  // If being a number-like string but not being defined a dimension name.\n  || !isNaN(dim) && !hasOwn(dimsByName, dim)) {\n    return dimensions[dim];\n  } else if (hasOwn(dimsByName, dim)) {\n    return dimsByName[dim];\n  }\n}\nfunction cloneAllDimensionInfo(dimensions) {\n  return clone(dimensions);\n}\nvar externalTransformMap = createHashMap();\nexport function registerExternalTransform(externalTransform) {\n  externalTransform = clone(externalTransform);\n  var type = externalTransform.type;\n  var errMsg = '';\n  if (!type) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Must have a `type` when `registerTransform`.';\n    }\n    throwError(errMsg);\n  }\n  var typeParsed = type.split(':');\n  if (typeParsed.length !== 2) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Name must include namespace like \"ns:regression\".';\n    }\n    throwError(errMsg);\n  }\n  // Namespace 'echarts:xxx' is official namespace, where the transforms should\n  // be called directly via 'xxx' rather than 'echarts:xxx'.\n  var isBuiltIn = false;\n  if (typeParsed[0] === 'echarts') {\n    type = typeParsed[1];\n    isBuiltIn = true;\n  }\n  externalTransform.__isBuiltIn = isBuiltIn;\n  externalTransformMap.set(type, externalTransform);\n}\nexport function applyDataTransform(rawTransOption, sourceList, infoForPrint) {\n  var pipedTransOption = normalizeToArray(rawTransOption);\n  var pipeLen = pipedTransOption.length;\n  var errMsg = '';\n  if (!pipeLen) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'If `transform` declared, it should at least contain one transform.';\n    }\n    throwError(errMsg);\n  }\n  for (var i = 0, len = pipeLen; i < len; i++) {\n    var transOption = pipedTransOption[i];\n    sourceList = applySingleDataTransform(transOption, sourceList, infoForPrint, pipeLen === 1 ? null : i);\n    // piped transform only support single input, except the fist one.\n    // piped transform only support single output, except the last one.\n    if (i !== len - 1) {\n      sourceList.length = Math.max(sourceList.length, 1);\n    }\n  }\n  return sourceList;\n}\nfunction applySingleDataTransform(transOption, upSourceList, infoForPrint,\n// If `pipeIndex` is null/undefined, no piped transform.\npipeIndex) {\n  var errMsg = '';\n  if (!upSourceList.length) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Must have at least one upstream dataset.';\n    }\n    throwError(errMsg);\n  }\n  if (!isObject(transOption)) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'transform declaration must be an object rather than ' + typeof transOption + '.';\n    }\n    throwError(errMsg);\n  }\n  var transType = transOption.type;\n  var externalTransform = externalTransformMap.get(transType);\n  if (!externalTransform) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Can not find transform on type \"' + transType + '\".';\n    }\n    throwError(errMsg);\n  }\n  // Prepare source\n  var extUpSourceList = map(upSourceList, function (upSource) {\n    return createExternalSource(upSource, externalTransform);\n  });\n  var resultList = normalizeToArray(externalTransform.transform({\n    upstream: extUpSourceList[0],\n    upstreamList: extUpSourceList,\n    config: clone(transOption.config)\n  }));\n  if (process.env.NODE_ENV !== 'production') {\n    if (transOption.print) {\n      var printStrArr = map(resultList, function (extSource) {\n        var pipeIndexStr = pipeIndex != null ? ' === pipe index: ' + pipeIndex : '';\n        return ['=== dataset index: ' + infoForPrint.datasetIndex + pipeIndexStr + ' ===', '- transform result data:', makePrintable(extSource.data), '- transform result dimensions:', makePrintable(extSource.dimensions)].join('\\n');\n      }).join('\\n');\n      log(printStrArr);\n    }\n  }\n  return map(resultList, function (result, resultIndex) {\n    var errMsg = '';\n    if (!isObject(result)) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'A transform should not return some empty results.';\n      }\n      throwError(errMsg);\n    }\n    if (!result.data) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Transform result data should be not be null or undefined';\n      }\n      throwError(errMsg);\n    }\n    var sourceFormat = detectSourceFormat(result.data);\n    if (!isSupportedSourceFormat(sourceFormat)) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Transform result data should be array rows or object rows.';\n      }\n      throwError(errMsg);\n    }\n    var resultMetaRawOption;\n    var firstUpSource = upSourceList[0];\n    /**\r\n     * Intuitively, the end users known the content of the original `dataset.source`,\r\n     * calucating the transform result in mind.\r\n     * Suppose the original `dataset.source` is:\r\n     * ```js\r\n     * [\r\n     *     ['product', '2012', '2013', '2014', '2015'],\r\n     *     ['AAA', 41.1, 30.4, 65.1, 53.3],\r\n     *     ['BBB', 86.5, 92.1, 85.7, 83.1],\r\n     *     ['CCC', 24.1, 67.2, 79.5, 86.4]\r\n     * ]\r\n     * ```\r\n     * The dimension info have to be detected from the source data.\r\n     * Some of the transformers (like filter, sort) will follow the dimension info\r\n     * of upstream, while others use new dimensions (like aggregate).\r\n     * Transformer can output a field `dimensions` to define the its own output dimensions.\r\n     * We also allow transformers to ignore the output `dimensions` field, and\r\n     * inherit the upstream dimensions definition. It can reduce the burden of handling\r\n     * dimensions in transformers.\r\n     *\r\n     * See also [DIMENSION_INHERIT_RULE] in `sourceManager.ts`.\r\n     */\n    if (firstUpSource && resultIndex === 0\n    // If transformer returns `dimensions`, it means that the transformer has different\n    // dimensions definitions. We do not inherit anything from upstream.\n    && !result.dimensions) {\n      var startIndex = firstUpSource.startIndex;\n      // We copy the header of upstream to the result, because:\n      // (1) The returned data always does not contain header line and can not be used\n      // as dimension-detection. In this case we can not use \"detected dimensions\" of\n      // upstream directly, because it might be detected based on different `seriesLayoutBy`.\n      // (2) We should support that the series read the upstream source in `seriesLayoutBy: 'row'`.\n      // So the original detected header should be add to the result, otherwise they can not be read.\n      if (startIndex) {\n        result.data = firstUpSource.data.slice(0, startIndex).concat(result.data);\n      }\n      resultMetaRawOption = {\n        seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN,\n        sourceHeader: startIndex,\n        dimensions: firstUpSource.metaRawOption.dimensions\n      };\n    } else {\n      resultMetaRawOption = {\n        seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN,\n        sourceHeader: 0,\n        dimensions: result.dimensions\n      };\n    }\n    return createSource(result.data, resultMetaRawOption, null);\n  });\n}\nfunction isSupportedSourceFormat(sourceFormat) {\n  return sourceFormat === SOURCE_FORMAT_ARRAY_ROWS || sourceFormat === SOURCE_FORMAT_OBJECT_ROWS;\n}", "map": {"version": 3, "names": ["SERIES_LAYOUT_BY_COLUMN", "SOURCE_FORMAT_OBJECT_ROWS", "SOURCE_FORMAT_ARRAY_ROWS", "normalizeToArray", "createHashMap", "bind", "each", "hasOwn", "map", "clone", "isObject", "extend", "isNumber", "getRawSourceItemGetter", "getRawSourceDataCounter", "getRawSourceValueGetter", "parseDataValue", "log", "makePrintable", "throwError", "createSource", "detectSourceFormat", "ExternalSource", "prototype", "getRawData", "Error", "getRawDataItem", "dataIndex", "cloneRawData", "getDimensionInfo", "dim", "cloneAllDimensionInfo", "count", "retrieveValue", "dimIndex", "retrieveValueFromItem", "dataItem", "convertValue", "rawVal", "dimInfo", "createExternalSource", "internalSource", "externalTransform", "extSource", "data", "sourceFormat", "sourceHeaderCount", "startIndex", "errMsg", "seriesLayoutBy", "process", "env", "NODE_ENV", "dimensions", "dimsByName", "dimsDef", "dimensionsDefine", "dimDef", "idx", "name", "dimDefExt", "index", "displayName", "push", "errMsg_1", "i", "dimensionsDetectedCount", "rawItemGetter", "__isBuiltIn", "rawCounter", "rawValueGetter", "rawItem", "upstream", "isSupportedSourceFormat", "result", "len", "length", "slice", "isNaN", "externalTransformMap", "registerExternalTransform", "type", "typeParsed", "split", "isBuiltIn", "set", "applyDataTransform", "rawTransOption", "sourceList", "infoForPrint", "pipedTransOption", "pipeLen", "transOption", "applySingleDataTransform", "Math", "max", "upSourceList", "pipeIndex", "transType", "get", "extUpSourceList", "upSource", "resultList", "transform", "upstreamList", "config", "print", "printStrArr", "pipeIndexStr", "datasetIndex", "join", "resultIndex", "resultMetaRawOption", "firstUpSource", "concat", "sourceHeader", "metaRawOption"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/data/helper/transform.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { SERIES_LAYOUT_BY_COLUMN, SOURCE_FORMAT_OBJECT_ROWS, SOURCE_FORMAT_ARRAY_ROWS } from '../../util/types.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { createHashMap, bind, each, hasOwn, map, clone, isObject, extend, isNumber } from 'zrender/lib/core/util.js';\nimport { getRawSourceItemGetter, getRawSourceDataCounter, getRawSourceValueGetter } from './dataProvider.js';\nimport { parseDataValue } from './dataValueHelper.js';\nimport { log, makePrintable, throwError } from '../../util/log.js';\nimport { createSource, detectSourceFormat } from '../Source.js';\n/**\r\n * TODO: disable writable.\r\n * This structure will be exposed to users.\r\n */\nvar ExternalSource = /** @class */function () {\n  function ExternalSource() {}\n  ExternalSource.prototype.getRawData = function () {\n    // Only built-in transform available.\n    throw new Error('not supported');\n  };\n  ExternalSource.prototype.getRawDataItem = function (dataIndex) {\n    // Only built-in transform available.\n    throw new Error('not supported');\n  };\n  ExternalSource.prototype.cloneRawData = function () {\n    return;\n  };\n  /**\r\n   * @return If dimension not found, return null/undefined.\r\n   */\n  ExternalSource.prototype.getDimensionInfo = function (dim) {\n    return;\n  };\n  /**\r\n   * dimensions defined if and only if either:\r\n   * (a) dataset.dimensions are declared.\r\n   * (b) dataset data include dimensions definitions in data (detected or via specified `sourceHeader`).\r\n   * If dimensions are defined, `dimensionInfoAll` is corresponding to\r\n   * the defined dimensions.\r\n   * Otherwise, `dimensionInfoAll` is determined by data columns.\r\n   * @return Always return an array (even empty array).\r\n   */\n  ExternalSource.prototype.cloneAllDimensionInfo = function () {\n    return;\n  };\n  ExternalSource.prototype.count = function () {\n    return;\n  };\n  /**\r\n   * Only support by dimension index.\r\n   * No need to support by dimension name in transform function,\r\n   * because transform function is not case-specific, no need to use name literally.\r\n   */\n  ExternalSource.prototype.retrieveValue = function (dataIndex, dimIndex) {\n    return;\n  };\n  ExternalSource.prototype.retrieveValueFromItem = function (dataItem, dimIndex) {\n    return;\n  };\n  ExternalSource.prototype.convertValue = function (rawVal, dimInfo) {\n    return parseDataValue(rawVal, dimInfo);\n  };\n  return ExternalSource;\n}();\nexport { ExternalSource };\nfunction createExternalSource(internalSource, externalTransform) {\n  var extSource = new ExternalSource();\n  var data = internalSource.data;\n  var sourceFormat = extSource.sourceFormat = internalSource.sourceFormat;\n  var sourceHeaderCount = internalSource.startIndex;\n  var errMsg = '';\n  if (internalSource.seriesLayoutBy !== SERIES_LAYOUT_BY_COLUMN) {\n    // For the logic simplicity in transformer, only 'culumn' is\n    // supported in data transform. Otherwise, the `dimensionsDefine`\n    // might be detected by 'row', which probably confuses users.\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`seriesLayoutBy` of upstream dataset can only be \"column\" in data transform.';\n    }\n    throwError(errMsg);\n  }\n  // [MEMO]\n  // Create a new dimensions structure for exposing.\n  // Do not expose all dimension info to users directly.\n  // Because the dimension is probably auto detected from data and not might reliable.\n  // Should not lead the transformers to think that is reliable and return it.\n  // See [DIMENSION_INHERIT_RULE] in `sourceManager.ts`.\n  var dimensions = [];\n  var dimsByName = {};\n  var dimsDef = internalSource.dimensionsDefine;\n  if (dimsDef) {\n    each(dimsDef, function (dimDef, idx) {\n      var name = dimDef.name;\n      var dimDefExt = {\n        index: idx,\n        name: name,\n        displayName: dimDef.displayName\n      };\n      dimensions.push(dimDefExt);\n      // Users probably do not specify dimension name. For simplicity, data transform\n      // does not generate dimension name.\n      if (name != null) {\n        // Dimension name should not be duplicated.\n        // For simplicity, data transform forbids name duplication, do not generate\n        // new name like module `completeDimensions.ts` did, but just tell users.\n        var errMsg_1 = '';\n        if (hasOwn(dimsByName, name)) {\n          if (process.env.NODE_ENV !== 'production') {\n            errMsg_1 = 'dimension name \"' + name + '\" duplicated.';\n          }\n          throwError(errMsg_1);\n        }\n        dimsByName[name] = dimDefExt;\n      }\n    });\n  }\n  // If dimension definitions are not defined and can not be detected.\n  // e.g., pure data `[[11, 22], ...]`.\n  else {\n    for (var i = 0; i < internalSource.dimensionsDetectedCount || 0; i++) {\n      // Do not generete name or anything others. The consequence process in\n      // `transform` or `series` probably have there own name generation strategry.\n      dimensions.push({\n        index: i\n      });\n    }\n  }\n  // Implement public methods:\n  var rawItemGetter = getRawSourceItemGetter(sourceFormat, SERIES_LAYOUT_BY_COLUMN);\n  if (externalTransform.__isBuiltIn) {\n    extSource.getRawDataItem = function (dataIndex) {\n      return rawItemGetter(data, sourceHeaderCount, dimensions, dataIndex);\n    };\n    extSource.getRawData = bind(getRawData, null, internalSource);\n  }\n  extSource.cloneRawData = bind(cloneRawData, null, internalSource);\n  var rawCounter = getRawSourceDataCounter(sourceFormat, SERIES_LAYOUT_BY_COLUMN);\n  extSource.count = bind(rawCounter, null, data, sourceHeaderCount, dimensions);\n  var rawValueGetter = getRawSourceValueGetter(sourceFormat);\n  extSource.retrieveValue = function (dataIndex, dimIndex) {\n    var rawItem = rawItemGetter(data, sourceHeaderCount, dimensions, dataIndex);\n    return retrieveValueFromItem(rawItem, dimIndex);\n  };\n  var retrieveValueFromItem = extSource.retrieveValueFromItem = function (dataItem, dimIndex) {\n    if (dataItem == null) {\n      return;\n    }\n    var dimDef = dimensions[dimIndex];\n    // When `dimIndex` is `null`, `rawValueGetter` return the whole item.\n    if (dimDef) {\n      return rawValueGetter(dataItem, dimIndex, dimDef.name);\n    }\n  };\n  extSource.getDimensionInfo = bind(getDimensionInfo, null, dimensions, dimsByName);\n  extSource.cloneAllDimensionInfo = bind(cloneAllDimensionInfo, null, dimensions);\n  return extSource;\n}\nfunction getRawData(upstream) {\n  var sourceFormat = upstream.sourceFormat;\n  if (!isSupportedSourceFormat(sourceFormat)) {\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`getRawData` is not supported in source format ' + sourceFormat;\n    }\n    throwError(errMsg);\n  }\n  return upstream.data;\n}\nfunction cloneRawData(upstream) {\n  var sourceFormat = upstream.sourceFormat;\n  var data = upstream.data;\n  if (!isSupportedSourceFormat(sourceFormat)) {\n    var errMsg = '';\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = '`cloneRawData` is not supported in source format ' + sourceFormat;\n    }\n    throwError(errMsg);\n  }\n  if (sourceFormat === SOURCE_FORMAT_ARRAY_ROWS) {\n    var result = [];\n    for (var i = 0, len = data.length; i < len; i++) {\n      // Not strictly clone for performance\n      result.push(data[i].slice());\n    }\n    return result;\n  } else if (sourceFormat === SOURCE_FORMAT_OBJECT_ROWS) {\n    var result = [];\n    for (var i = 0, len = data.length; i < len; i++) {\n      // Not strictly clone for performance\n      result.push(extend({}, data[i]));\n    }\n    return result;\n  }\n}\nfunction getDimensionInfo(dimensions, dimsByName, dim) {\n  if (dim == null) {\n    return;\n  }\n  // Keep the same logic as `List::getDimension` did.\n  if (isNumber(dim)\n  // If being a number-like string but not being defined a dimension name.\n  || !isNaN(dim) && !hasOwn(dimsByName, dim)) {\n    return dimensions[dim];\n  } else if (hasOwn(dimsByName, dim)) {\n    return dimsByName[dim];\n  }\n}\nfunction cloneAllDimensionInfo(dimensions) {\n  return clone(dimensions);\n}\nvar externalTransformMap = createHashMap();\nexport function registerExternalTransform(externalTransform) {\n  externalTransform = clone(externalTransform);\n  var type = externalTransform.type;\n  var errMsg = '';\n  if (!type) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Must have a `type` when `registerTransform`.';\n    }\n    throwError(errMsg);\n  }\n  var typeParsed = type.split(':');\n  if (typeParsed.length !== 2) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Name must include namespace like \"ns:regression\".';\n    }\n    throwError(errMsg);\n  }\n  // Namespace 'echarts:xxx' is official namespace, where the transforms should\n  // be called directly via 'xxx' rather than 'echarts:xxx'.\n  var isBuiltIn = false;\n  if (typeParsed[0] === 'echarts') {\n    type = typeParsed[1];\n    isBuiltIn = true;\n  }\n  externalTransform.__isBuiltIn = isBuiltIn;\n  externalTransformMap.set(type, externalTransform);\n}\nexport function applyDataTransform(rawTransOption, sourceList, infoForPrint) {\n  var pipedTransOption = normalizeToArray(rawTransOption);\n  var pipeLen = pipedTransOption.length;\n  var errMsg = '';\n  if (!pipeLen) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'If `transform` declared, it should at least contain one transform.';\n    }\n    throwError(errMsg);\n  }\n  for (var i = 0, len = pipeLen; i < len; i++) {\n    var transOption = pipedTransOption[i];\n    sourceList = applySingleDataTransform(transOption, sourceList, infoForPrint, pipeLen === 1 ? null : i);\n    // piped transform only support single input, except the fist one.\n    // piped transform only support single output, except the last one.\n    if (i !== len - 1) {\n      sourceList.length = Math.max(sourceList.length, 1);\n    }\n  }\n  return sourceList;\n}\nfunction applySingleDataTransform(transOption, upSourceList, infoForPrint,\n// If `pipeIndex` is null/undefined, no piped transform.\npipeIndex) {\n  var errMsg = '';\n  if (!upSourceList.length) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Must have at least one upstream dataset.';\n    }\n    throwError(errMsg);\n  }\n  if (!isObject(transOption)) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'transform declaration must be an object rather than ' + typeof transOption + '.';\n    }\n    throwError(errMsg);\n  }\n  var transType = transOption.type;\n  var externalTransform = externalTransformMap.get(transType);\n  if (!externalTransform) {\n    if (process.env.NODE_ENV !== 'production') {\n      errMsg = 'Can not find transform on type \"' + transType + '\".';\n    }\n    throwError(errMsg);\n  }\n  // Prepare source\n  var extUpSourceList = map(upSourceList, function (upSource) {\n    return createExternalSource(upSource, externalTransform);\n  });\n  var resultList = normalizeToArray(externalTransform.transform({\n    upstream: extUpSourceList[0],\n    upstreamList: extUpSourceList,\n    config: clone(transOption.config)\n  }));\n  if (process.env.NODE_ENV !== 'production') {\n    if (transOption.print) {\n      var printStrArr = map(resultList, function (extSource) {\n        var pipeIndexStr = pipeIndex != null ? ' === pipe index: ' + pipeIndex : '';\n        return ['=== dataset index: ' + infoForPrint.datasetIndex + pipeIndexStr + ' ===', '- transform result data:', makePrintable(extSource.data), '- transform result dimensions:', makePrintable(extSource.dimensions)].join('\\n');\n      }).join('\\n');\n      log(printStrArr);\n    }\n  }\n  return map(resultList, function (result, resultIndex) {\n    var errMsg = '';\n    if (!isObject(result)) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'A transform should not return some empty results.';\n      }\n      throwError(errMsg);\n    }\n    if (!result.data) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Transform result data should be not be null or undefined';\n      }\n      throwError(errMsg);\n    }\n    var sourceFormat = detectSourceFormat(result.data);\n    if (!isSupportedSourceFormat(sourceFormat)) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Transform result data should be array rows or object rows.';\n      }\n      throwError(errMsg);\n    }\n    var resultMetaRawOption;\n    var firstUpSource = upSourceList[0];\n    /**\r\n     * Intuitively, the end users known the content of the original `dataset.source`,\r\n     * calucating the transform result in mind.\r\n     * Suppose the original `dataset.source` is:\r\n     * ```js\r\n     * [\r\n     *     ['product', '2012', '2013', '2014', '2015'],\r\n     *     ['AAA', 41.1, 30.4, 65.1, 53.3],\r\n     *     ['BBB', 86.5, 92.1, 85.7, 83.1],\r\n     *     ['CCC', 24.1, 67.2, 79.5, 86.4]\r\n     * ]\r\n     * ```\r\n     * The dimension info have to be detected from the source data.\r\n     * Some of the transformers (like filter, sort) will follow the dimension info\r\n     * of upstream, while others use new dimensions (like aggregate).\r\n     * Transformer can output a field `dimensions` to define the its own output dimensions.\r\n     * We also allow transformers to ignore the output `dimensions` field, and\r\n     * inherit the upstream dimensions definition. It can reduce the burden of handling\r\n     * dimensions in transformers.\r\n     *\r\n     * See also [DIMENSION_INHERIT_RULE] in `sourceManager.ts`.\r\n     */\n    if (firstUpSource && resultIndex === 0\n    // If transformer returns `dimensions`, it means that the transformer has different\n    // dimensions definitions. We do not inherit anything from upstream.\n    && !result.dimensions) {\n      var startIndex = firstUpSource.startIndex;\n      // We copy the header of upstream to the result, because:\n      // (1) The returned data always does not contain header line and can not be used\n      // as dimension-detection. In this case we can not use \"detected dimensions\" of\n      // upstream directly, because it might be detected based on different `seriesLayoutBy`.\n      // (2) We should support that the series read the upstream source in `seriesLayoutBy: 'row'`.\n      // So the original detected header should be add to the result, otherwise they can not be read.\n      if (startIndex) {\n        result.data = firstUpSource.data.slice(0, startIndex).concat(result.data);\n      }\n      resultMetaRawOption = {\n        seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN,\n        sourceHeader: startIndex,\n        dimensions: firstUpSource.metaRawOption.dimensions\n      };\n    } else {\n      resultMetaRawOption = {\n        seriesLayoutBy: SERIES_LAYOUT_BY_COLUMN,\n        sourceHeader: 0,\n        dimensions: result.dimensions\n      };\n    }\n    return createSource(result.data, resultMetaRawOption, null);\n  });\n}\nfunction isSupportedSourceFormat(sourceFormat) {\n  return sourceFormat === SOURCE_FORMAT_ARRAY_ROWS || sourceFormat === SOURCE_FORMAT_OBJECT_ROWS;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuB,EAAEC,yBAAyB,EAAEC,wBAAwB,QAAQ,qBAAqB;AAClH,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,aAAa,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,0BAA0B;AACpH,SAASC,sBAAsB,EAAEC,uBAAuB,EAAEC,uBAAuB,QAAQ,mBAAmB;AAC5G,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,GAAG,EAAEC,aAAa,EAAEC,UAAU,QAAQ,mBAAmB;AAClE,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,cAAc;AAC/D;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAG,aAAa,YAAY;EAC5C,SAASA,cAAcA,CAAA,EAAG,CAAC;EAC3BA,cAAc,CAACC,SAAS,CAACC,UAAU,GAAG,YAAY;IAChD;IACA,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;EAClC,CAAC;EACDH,cAAc,CAACC,SAAS,CAACG,cAAc,GAAG,UAAUC,SAAS,EAAE;IAC7D;IACA,MAAM,IAAIF,KAAK,CAAC,eAAe,CAAC;EAClC,CAAC;EACDH,cAAc,CAACC,SAAS,CAACK,YAAY,GAAG,YAAY;IAClD;EACF,CAAC;EACD;AACF;AACA;EACEN,cAAc,CAACC,SAAS,CAACM,gBAAgB,GAAG,UAAUC,GAAG,EAAE;IACzD;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACER,cAAc,CAACC,SAAS,CAACQ,qBAAqB,GAAG,YAAY;IAC3D;EACF,CAAC;EACDT,cAAc,CAACC,SAAS,CAACS,KAAK,GAAG,YAAY;IAC3C;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;EACEV,cAAc,CAACC,SAAS,CAACU,aAAa,GAAG,UAAUN,SAAS,EAAEO,QAAQ,EAAE;IACtE;EACF,CAAC;EACDZ,cAAc,CAACC,SAAS,CAACY,qBAAqB,GAAG,UAAUC,QAAQ,EAAEF,QAAQ,EAAE;IAC7E;EACF,CAAC;EACDZ,cAAc,CAACC,SAAS,CAACc,YAAY,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACjE,OAAOvB,cAAc,CAACsB,MAAM,EAAEC,OAAO,CAAC;EACxC,CAAC;EACD,OAAOjB,cAAc;AACvB,CAAC,CAAC,CAAC;AACH,SAASA,cAAc;AACvB,SAASkB,oBAAoBA,CAACC,cAAc,EAAEC,iBAAiB,EAAE;EAC/D,IAAIC,SAAS,GAAG,IAAIrB,cAAc,CAAC,CAAC;EACpC,IAAIsB,IAAI,GAAGH,cAAc,CAACG,IAAI;EAC9B,IAAIC,YAAY,GAAGF,SAAS,CAACE,YAAY,GAAGJ,cAAc,CAACI,YAAY;EACvE,IAAIC,iBAAiB,GAAGL,cAAc,CAACM,UAAU;EACjD,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIP,cAAc,CAACQ,cAAc,KAAKjD,uBAAuB,EAAE;IAC7D;IACA;IACA;IACA,IAAIkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,8EAA8E;IACzF;IACA7B,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIK,UAAU,GAAG,EAAE;EACnB,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB,IAAIC,OAAO,GAAGd,cAAc,CAACe,gBAAgB;EAC7C,IAAID,OAAO,EAAE;IACXjD,IAAI,CAACiD,OAAO,EAAE,UAAUE,MAAM,EAAEC,GAAG,EAAE;MACnC,IAAIC,IAAI,GAAGF,MAAM,CAACE,IAAI;MACtB,IAAIC,SAAS,GAAG;QACdC,KAAK,EAAEH,GAAG;QACVC,IAAI,EAAEA,IAAI;QACVG,WAAW,EAAEL,MAAM,CAACK;MACtB,CAAC;MACDT,UAAU,CAACU,IAAI,CAACH,SAAS,CAAC;MAC1B;MACA;MACA,IAAID,IAAI,IAAI,IAAI,EAAE;QAChB;QACA;QACA;QACA,IAAIK,QAAQ,GAAG,EAAE;QACjB,IAAIzD,MAAM,CAAC+C,UAAU,EAAEK,IAAI,CAAC,EAAE;UAC5B,IAAIT,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzCY,QAAQ,GAAG,kBAAkB,GAAGL,IAAI,GAAG,eAAe;UACxD;UACAxC,UAAU,CAAC6C,QAAQ,CAAC;QACtB;QACAV,UAAU,CAACK,IAAI,CAAC,GAAGC,SAAS;MAC9B;IACF,CAAC,CAAC;EACJ;EACA;EACA;EAAA,KACK;IACH,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,cAAc,CAACyB,uBAAuB,IAAI,CAAC,EAAED,CAAC,EAAE,EAAE;MACpE;MACA;MACAZ,UAAU,CAACU,IAAI,CAAC;QACdF,KAAK,EAAEI;MACT,CAAC,CAAC;IACJ;EACF;EACA;EACA,IAAIE,aAAa,GAAGtD,sBAAsB,CAACgC,YAAY,EAAE7C,uBAAuB,CAAC;EACjF,IAAI0C,iBAAiB,CAAC0B,WAAW,EAAE;IACjCzB,SAAS,CAACjB,cAAc,GAAG,UAAUC,SAAS,EAAE;MAC9C,OAAOwC,aAAa,CAACvB,IAAI,EAAEE,iBAAiB,EAAEO,UAAU,EAAE1B,SAAS,CAAC;IACtE,CAAC;IACDgB,SAAS,CAACnB,UAAU,GAAGnB,IAAI,CAACmB,UAAU,EAAE,IAAI,EAAEiB,cAAc,CAAC;EAC/D;EACAE,SAAS,CAACf,YAAY,GAAGvB,IAAI,CAACuB,YAAY,EAAE,IAAI,EAAEa,cAAc,CAAC;EACjE,IAAI4B,UAAU,GAAGvD,uBAAuB,CAAC+B,YAAY,EAAE7C,uBAAuB,CAAC;EAC/E2C,SAAS,CAACX,KAAK,GAAG3B,IAAI,CAACgE,UAAU,EAAE,IAAI,EAAEzB,IAAI,EAAEE,iBAAiB,EAAEO,UAAU,CAAC;EAC7E,IAAIiB,cAAc,GAAGvD,uBAAuB,CAAC8B,YAAY,CAAC;EAC1DF,SAAS,CAACV,aAAa,GAAG,UAAUN,SAAS,EAAEO,QAAQ,EAAE;IACvD,IAAIqC,OAAO,GAAGJ,aAAa,CAACvB,IAAI,EAAEE,iBAAiB,EAAEO,UAAU,EAAE1B,SAAS,CAAC;IAC3E,OAAOQ,qBAAqB,CAACoC,OAAO,EAAErC,QAAQ,CAAC;EACjD,CAAC;EACD,IAAIC,qBAAqB,GAAGQ,SAAS,CAACR,qBAAqB,GAAG,UAAUC,QAAQ,EAAEF,QAAQ,EAAE;IAC1F,IAAIE,QAAQ,IAAI,IAAI,EAAE;MACpB;IACF;IACA,IAAIqB,MAAM,GAAGJ,UAAU,CAACnB,QAAQ,CAAC;IACjC;IACA,IAAIuB,MAAM,EAAE;MACV,OAAOa,cAAc,CAAClC,QAAQ,EAAEF,QAAQ,EAAEuB,MAAM,CAACE,IAAI,CAAC;IACxD;EACF,CAAC;EACDhB,SAAS,CAACd,gBAAgB,GAAGxB,IAAI,CAACwB,gBAAgB,EAAE,IAAI,EAAEwB,UAAU,EAAEC,UAAU,CAAC;EACjFX,SAAS,CAACZ,qBAAqB,GAAG1B,IAAI,CAAC0B,qBAAqB,EAAE,IAAI,EAAEsB,UAAU,CAAC;EAC/E,OAAOV,SAAS;AAClB;AACA,SAASnB,UAAUA,CAACgD,QAAQ,EAAE;EAC5B,IAAI3B,YAAY,GAAG2B,QAAQ,CAAC3B,YAAY;EACxC,IAAI,CAAC4B,uBAAuB,CAAC5B,YAAY,CAAC,EAAE;IAC1C,IAAIG,MAAM,GAAG,EAAE;IACf,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,iDAAiD,GAAGH,YAAY;IAC3E;IACA1B,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA,OAAOwB,QAAQ,CAAC5B,IAAI;AACtB;AACA,SAAShB,YAAYA,CAAC4C,QAAQ,EAAE;EAC9B,IAAI3B,YAAY,GAAG2B,QAAQ,CAAC3B,YAAY;EACxC,IAAID,IAAI,GAAG4B,QAAQ,CAAC5B,IAAI;EACxB,IAAI,CAAC6B,uBAAuB,CAAC5B,YAAY,CAAC,EAAE;IAC1C,IAAIG,MAAM,GAAG,EAAE;IACf,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,mDAAmD,GAAGH,YAAY;IAC7E;IACA1B,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA,IAAIH,YAAY,KAAK3C,wBAAwB,EAAE;IAC7C,IAAIwE,MAAM,GAAG,EAAE;IACf,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEU,GAAG,GAAG/B,IAAI,CAACgC,MAAM,EAAEX,CAAC,GAAGU,GAAG,EAAEV,CAAC,EAAE,EAAE;MAC/C;MACAS,MAAM,CAACX,IAAI,CAACnB,IAAI,CAACqB,CAAC,CAAC,CAACY,KAAK,CAAC,CAAC,CAAC;IAC9B;IACA,OAAOH,MAAM;EACf,CAAC,MAAM,IAAI7B,YAAY,KAAK5C,yBAAyB,EAAE;IACrD,IAAIyE,MAAM,GAAG,EAAE;IACf,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEU,GAAG,GAAG/B,IAAI,CAACgC,MAAM,EAAEX,CAAC,GAAGU,GAAG,EAAEV,CAAC,EAAE,EAAE;MAC/C;MACAS,MAAM,CAACX,IAAI,CAACpD,MAAM,CAAC,CAAC,CAAC,EAAEiC,IAAI,CAACqB,CAAC,CAAC,CAAC,CAAC;IAClC;IACA,OAAOS,MAAM;EACf;AACF;AACA,SAAS7C,gBAAgBA,CAACwB,UAAU,EAAEC,UAAU,EAAExB,GAAG,EAAE;EACrD,IAAIA,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EACA;EACA,IAAIlB,QAAQ,CAACkB,GAAG;EAChB;EAAA,GACG,CAACgD,KAAK,CAAChD,GAAG,CAAC,IAAI,CAACvB,MAAM,CAAC+C,UAAU,EAAExB,GAAG,CAAC,EAAE;IAC1C,OAAOuB,UAAU,CAACvB,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIvB,MAAM,CAAC+C,UAAU,EAAExB,GAAG,CAAC,EAAE;IAClC,OAAOwB,UAAU,CAACxB,GAAG,CAAC;EACxB;AACF;AACA,SAASC,qBAAqBA,CAACsB,UAAU,EAAE;EACzC,OAAO5C,KAAK,CAAC4C,UAAU,CAAC;AAC1B;AACA,IAAI0B,oBAAoB,GAAG3E,aAAa,CAAC,CAAC;AAC1C,OAAO,SAAS4E,yBAAyBA,CAACtC,iBAAiB,EAAE;EAC3DA,iBAAiB,GAAGjC,KAAK,CAACiC,iBAAiB,CAAC;EAC5C,IAAIuC,IAAI,GAAGvC,iBAAiB,CAACuC,IAAI;EACjC,IAAIjC,MAAM,GAAG,EAAE;EACf,IAAI,CAACiC,IAAI,EAAE;IACT,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,8CAA8C;IACzD;IACA7B,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA,IAAIkC,UAAU,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAChC,IAAID,UAAU,CAACN,MAAM,KAAK,CAAC,EAAE;IAC3B,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,mDAAmD;IAC9D;IACA7B,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA;EACA;EACA,IAAIoC,SAAS,GAAG,KAAK;EACrB,IAAIF,UAAU,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;IAC/BD,IAAI,GAAGC,UAAU,CAAC,CAAC,CAAC;IACpBE,SAAS,GAAG,IAAI;EAClB;EACA1C,iBAAiB,CAAC0B,WAAW,GAAGgB,SAAS;EACzCL,oBAAoB,CAACM,GAAG,CAACJ,IAAI,EAAEvC,iBAAiB,CAAC;AACnD;AACA,OAAO,SAAS4C,kBAAkBA,CAACC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC3E,IAAIC,gBAAgB,GAAGvF,gBAAgB,CAACoF,cAAc,CAAC;EACvD,IAAII,OAAO,GAAGD,gBAAgB,CAACd,MAAM;EACrC,IAAI5B,MAAM,GAAG,EAAE;EACf,IAAI,CAAC2C,OAAO,EAAE;IACZ,IAAIzC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,oEAAoE;IAC/E;IACA7B,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEU,GAAG,GAAGgB,OAAO,EAAE1B,CAAC,GAAGU,GAAG,EAAEV,CAAC,EAAE,EAAE;IAC3C,IAAI2B,WAAW,GAAGF,gBAAgB,CAACzB,CAAC,CAAC;IACrCuB,UAAU,GAAGK,wBAAwB,CAACD,WAAW,EAAEJ,UAAU,EAAEC,YAAY,EAAEE,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG1B,CAAC,CAAC;IACtG;IACA;IACA,IAAIA,CAAC,KAAKU,GAAG,GAAG,CAAC,EAAE;MACjBa,UAAU,CAACZ,MAAM,GAAGkB,IAAI,CAACC,GAAG,CAACP,UAAU,CAACZ,MAAM,EAAE,CAAC,CAAC;IACpD;EACF;EACA,OAAOY,UAAU;AACnB;AACA,SAASK,wBAAwBA,CAACD,WAAW,EAAEI,YAAY,EAAEP,YAAY;AACzE;AACAQ,SAAS,EAAE;EACT,IAAIjD,MAAM,GAAG,EAAE;EACf,IAAI,CAACgD,YAAY,CAACpB,MAAM,EAAE;IACxB,IAAI1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,0CAA0C;IACrD;IACA7B,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA,IAAI,CAACtC,QAAQ,CAACkF,WAAW,CAAC,EAAE;IAC1B,IAAI1C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,sDAAsD,GAAG,OAAO4C,WAAW,GAAG,GAAG;IAC5F;IACAzE,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA,IAAIkD,SAAS,GAAGN,WAAW,CAACX,IAAI;EAChC,IAAIvC,iBAAiB,GAAGqC,oBAAoB,CAACoB,GAAG,CAACD,SAAS,CAAC;EAC3D,IAAI,CAACxD,iBAAiB,EAAE;IACtB,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCJ,MAAM,GAAG,kCAAkC,GAAGkD,SAAS,GAAG,IAAI;IAChE;IACA/E,UAAU,CAAC6B,MAAM,CAAC;EACpB;EACA;EACA,IAAIoD,eAAe,GAAG5F,GAAG,CAACwF,YAAY,EAAE,UAAUK,QAAQ,EAAE;IAC1D,OAAO7D,oBAAoB,CAAC6D,QAAQ,EAAE3D,iBAAiB,CAAC;EAC1D,CAAC,CAAC;EACF,IAAI4D,UAAU,GAAGnG,gBAAgB,CAACuC,iBAAiB,CAAC6D,SAAS,CAAC;IAC5D/B,QAAQ,EAAE4B,eAAe,CAAC,CAAC,CAAC;IAC5BI,YAAY,EAAEJ,eAAe;IAC7BK,MAAM,EAAEhG,KAAK,CAACmF,WAAW,CAACa,MAAM;EAClC,CAAC,CAAC,CAAC;EACH,IAAIvD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIwC,WAAW,CAACc,KAAK,EAAE;MACrB,IAAIC,WAAW,GAAGnG,GAAG,CAAC8F,UAAU,EAAE,UAAU3D,SAAS,EAAE;QACrD,IAAIiE,YAAY,GAAGX,SAAS,IAAI,IAAI,GAAG,mBAAmB,GAAGA,SAAS,GAAG,EAAE;QAC3E,OAAO,CAAC,qBAAqB,GAAGR,YAAY,CAACoB,YAAY,GAAGD,YAAY,GAAG,MAAM,EAAE,0BAA0B,EAAE1F,aAAa,CAACyB,SAAS,CAACC,IAAI,CAAC,EAAE,gCAAgC,EAAE1B,aAAa,CAACyB,SAAS,CAACU,UAAU,CAAC,CAAC,CAACyD,IAAI,CAAC,IAAI,CAAC;MACjO,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;MACb7F,GAAG,CAAC0F,WAAW,CAAC;IAClB;EACF;EACA,OAAOnG,GAAG,CAAC8F,UAAU,EAAE,UAAU5B,MAAM,EAAEqC,WAAW,EAAE;IACpD,IAAI/D,MAAM,GAAG,EAAE;IACf,IAAI,CAACtC,QAAQ,CAACgE,MAAM,CAAC,EAAE;MACrB,IAAIxB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCJ,MAAM,GAAG,mDAAmD;MAC9D;MACA7B,UAAU,CAAC6B,MAAM,CAAC;IACpB;IACA,IAAI,CAAC0B,MAAM,CAAC9B,IAAI,EAAE;MAChB,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCJ,MAAM,GAAG,0DAA0D;MACrE;MACA7B,UAAU,CAAC6B,MAAM,CAAC;IACpB;IACA,IAAIH,YAAY,GAAGxB,kBAAkB,CAACqD,MAAM,CAAC9B,IAAI,CAAC;IAClD,IAAI,CAAC6B,uBAAuB,CAAC5B,YAAY,CAAC,EAAE;MAC1C,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCJ,MAAM,GAAG,4DAA4D;MACvE;MACA7B,UAAU,CAAC6B,MAAM,CAAC;IACpB;IACA,IAAIgE,mBAAmB;IACvB,IAAIC,aAAa,GAAGjB,YAAY,CAAC,CAAC,CAAC;IACnC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIiB,aAAa,IAAIF,WAAW,KAAK;IACrC;IACA;IAAA,GACG,CAACrC,MAAM,CAACrB,UAAU,EAAE;MACrB,IAAIN,UAAU,GAAGkE,aAAa,CAAClE,UAAU;MACzC;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,UAAU,EAAE;QACd2B,MAAM,CAAC9B,IAAI,GAAGqE,aAAa,CAACrE,IAAI,CAACiC,KAAK,CAAC,CAAC,EAAE9B,UAAU,CAAC,CAACmE,MAAM,CAACxC,MAAM,CAAC9B,IAAI,CAAC;MAC3E;MACAoE,mBAAmB,GAAG;QACpB/D,cAAc,EAAEjD,uBAAuB;QACvCmH,YAAY,EAAEpE,UAAU;QACxBM,UAAU,EAAE4D,aAAa,CAACG,aAAa,CAAC/D;MAC1C,CAAC;IACH,CAAC,MAAM;MACL2D,mBAAmB,GAAG;QACpB/D,cAAc,EAAEjD,uBAAuB;QACvCmH,YAAY,EAAE,CAAC;QACf9D,UAAU,EAAEqB,MAAM,CAACrB;MACrB,CAAC;IACH;IACA,OAAOjC,YAAY,CAACsD,MAAM,CAAC9B,IAAI,EAAEoE,mBAAmB,EAAE,IAAI,CAAC;EAC7D,CAAC,CAAC;AACJ;AACA,SAASvC,uBAAuBA,CAAC5B,YAAY,EAAE;EAC7C,OAAOA,YAAY,KAAK3C,wBAAwB,IAAI2C,YAAY,KAAK5C,yBAAyB;AAChG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}