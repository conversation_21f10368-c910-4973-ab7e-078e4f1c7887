{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport categoryFilter from './categoryFilter.js';\nimport categoryVisual from './categoryVisual.js';\nimport edgeVisual from './edgeVisual.js';\nimport simpleLayout from './simpleLayout.js';\nimport circularLayout from './circularLayout.js';\nimport forceLayout from './forceLayout.js';\nimport createView from './createView.js';\nimport View from '../../coord/View.js';\nimport GraphView from './GraphView.js';\nimport GraphSeriesModel from './GraphSeries.js';\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nimport { noop } from 'zrender/lib/core/util.js';\nvar actionInfo = {\n  type: 'graphRoam',\n  event: 'graphRoam',\n  update: 'none'\n};\nexport function install(registers) {\n  registers.registerChartView(GraphView);\n  registers.registerSeriesModel(GraphSeriesModel);\n  registers.registerProcessor(categoryFilter);\n  registers.registerVisual(categoryVisual);\n  registers.registerVisual(edgeVisual);\n  registers.registerLayout(simpleLayout);\n  registers.registerLayout(registers.PRIORITY.VISUAL.POST_CHART_LAYOUT, circularLayout);\n  registers.registerLayout(forceLayout);\n  registers.registerCoordinateSystem('graphView', {\n    dimensions: View.dimensions,\n    create: createView\n  });\n  // Register legacy focus actions\n  registers.registerAction({\n    type: 'focusNodeAdjacency',\n    event: 'focusNodeAdjacency',\n    update: 'series:focusNodeAdjacency'\n  }, noop);\n  registers.registerAction({\n    type: 'unfocusNodeAdjacency',\n    event: 'unfocusNodeAdjacency',\n    update: 'series:unfocusNodeAdjacency'\n  }, noop);\n  // Register roam action.\n  registers.registerAction(actionInfo, function (payload, ecModel, api) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      query: payload\n    }, function (seriesModel) {\n      var coordSys = seriesModel.coordinateSystem;\n      var res = updateCenterAndZoom(coordSys, payload, undefined, api);\n      seriesModel.setCenter && seriesModel.setCenter(res.center);\n      seriesModel.setZoom && seriesModel.setZoom(res.zoom);\n    });\n  });\n}", "map": {"version": 3, "names": ["categoryFilter", "categoryVisual", "edgeVisual", "simpleLayout", "circularLayout", "forceLayout", "createView", "View", "GraphView", "GraphSeriesModel", "updateCenterAndZoom", "noop", "actionInfo", "type", "event", "update", "install", "registers", "registerChartView", "registerSeriesModel", "registerProcessor", "registerVisual", "registerLayout", "PRIORITY", "VISUAL", "POST_CHART_LAYOUT", "registerCoordinateSystem", "dimensions", "create", "registerAction", "payload", "ecModel", "api", "eachComponent", "mainType", "query", "seriesModel", "coordSys", "coordinateSystem", "res", "undefined", "setCenter", "center", "setZoom", "zoom"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/graph/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport categoryFilter from './categoryFilter.js';\nimport categoryVisual from './categoryVisual.js';\nimport edgeVisual from './edgeVisual.js';\nimport simpleLayout from './simpleLayout.js';\nimport circularLayout from './circularLayout.js';\nimport forceLayout from './forceLayout.js';\nimport createView from './createView.js';\nimport View from '../../coord/View.js';\nimport GraphView from './GraphView.js';\nimport GraphSeriesModel from './GraphSeries.js';\nimport { updateCenterAndZoom } from '../../action/roamHelper.js';\nimport { noop } from 'zrender/lib/core/util.js';\nvar actionInfo = {\n  type: 'graphRoam',\n  event: 'graphRoam',\n  update: 'none'\n};\nexport function install(registers) {\n  registers.registerChartView(GraphView);\n  registers.registerSeriesModel(GraphSeriesModel);\n  registers.registerProcessor(categoryFilter);\n  registers.registerVisual(categoryVisual);\n  registers.registerVisual(edgeVisual);\n  registers.registerLayout(simpleLayout);\n  registers.registerLayout(registers.PRIORITY.VISUAL.POST_CHART_LAYOUT, circularLayout);\n  registers.registerLayout(forceLayout);\n  registers.registerCoordinateSystem('graphView', {\n    dimensions: View.dimensions,\n    create: createView\n  });\n  // Register legacy focus actions\n  registers.registerAction({\n    type: 'focusNodeAdjacency',\n    event: 'focusNodeAdjacency',\n    update: 'series:focusNodeAdjacency'\n  }, noop);\n  registers.registerAction({\n    type: 'unfocusNodeAdjacency',\n    event: 'unfocusNodeAdjacency',\n    update: 'series:unfocusNodeAdjacency'\n  }, noop);\n  // Register roam action.\n  registers.registerAction(actionInfo, function (payload, ecModel, api) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      query: payload\n    }, function (seriesModel) {\n      var coordSys = seriesModel.coordinateSystem;\n      var res = updateCenterAndZoom(coordSys, payload, undefined, api);\n      seriesModel.setCenter && seriesModel.setCenter(res.center);\n      seriesModel.setZoom && seriesModel.setZoom(res.zoom);\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,IAAI,MAAM,qBAAqB;AACtC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,gBAAgB,MAAM,kBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,IAAIC,UAAU,GAAG;EACfC,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE,WAAW;EAClBC,MAAM,EAAE;AACV,CAAC;AACD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACV,SAAS,CAAC;EACtCS,SAAS,CAACE,mBAAmB,CAACV,gBAAgB,CAAC;EAC/CQ,SAAS,CAACG,iBAAiB,CAACpB,cAAc,CAAC;EAC3CiB,SAAS,CAACI,cAAc,CAACpB,cAAc,CAAC;EACxCgB,SAAS,CAACI,cAAc,CAACnB,UAAU,CAAC;EACpCe,SAAS,CAACK,cAAc,CAACnB,YAAY,CAAC;EACtCc,SAAS,CAACK,cAAc,CAACL,SAAS,CAACM,QAAQ,CAACC,MAAM,CAACC,iBAAiB,EAAErB,cAAc,CAAC;EACrFa,SAAS,CAACK,cAAc,CAACjB,WAAW,CAAC;EACrCY,SAAS,CAACS,wBAAwB,CAAC,WAAW,EAAE;IAC9CC,UAAU,EAAEpB,IAAI,CAACoB,UAAU;IAC3BC,MAAM,EAAEtB;EACV,CAAC,CAAC;EACF;EACAW,SAAS,CAACY,cAAc,CAAC;IACvBhB,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,oBAAoB;IAC3BC,MAAM,EAAE;EACV,CAAC,EAAEJ,IAAI,CAAC;EACRM,SAAS,CAACY,cAAc,CAAC;IACvBhB,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE;EACV,CAAC,EAAEJ,IAAI,CAAC;EACR;EACAM,SAAS,CAACY,cAAc,CAACjB,UAAU,EAAE,UAAUkB,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACpED,OAAO,CAACE,aAAa,CAAC;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAEL;IACT,CAAC,EAAE,UAAUM,WAAW,EAAE;MACxB,IAAIC,QAAQ,GAAGD,WAAW,CAACE,gBAAgB;MAC3C,IAAIC,GAAG,GAAG7B,mBAAmB,CAAC2B,QAAQ,EAAEP,OAAO,EAAEU,SAAS,EAAER,GAAG,CAAC;MAChEI,WAAW,CAACK,SAAS,IAAIL,WAAW,CAACK,SAAS,CAACF,GAAG,CAACG,MAAM,CAAC;MAC1DN,WAAW,CAACO,OAAO,IAAIP,WAAW,CAACO,OAAO,CAACJ,GAAG,CAACK,IAAI,CAAC;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}