{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from '../../extension.js';\nimport { install as installLegendPlain } from './installLegendPlain.js';\nimport ScrollableLegendModel from './ScrollableLegendModel.js';\nimport ScrollableLegendView from './ScrollableLegendView.js';\nimport installScrollableLegendAction from './scrollableLegendAction.js';\nexport function install(registers) {\n  use(installLegendPlain);\n  registers.registerComponentModel(ScrollableLegendModel);\n  registers.registerComponentView(ScrollableLegendView);\n  installScrollableLegendAction(registers);\n}", "map": {"version": 3, "names": ["use", "install", "installLegendPlain", "ScrollableLegendModel", "ScrollableLegendView", "installScrollableLegendAction", "registers", "registerComponentModel", "registerComponentView"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/legend/installLegendScroll.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from '../../extension.js';\nimport { install as installLegendPlain } from './installLegendPlain.js';\nimport ScrollableLegendModel from './ScrollableLegendModel.js';\nimport ScrollableLegendView from './ScrollableLegendView.js';\nimport installScrollableLegendAction from './scrollableLegendAction.js';\nexport function install(registers) {\n  use(installLegendPlain);\n  registers.registerComponentModel(ScrollableLegendModel);\n  registers.registerComponentView(ScrollableLegendView);\n  installScrollableLegendAction(registers);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,oBAAoB;AACxC,SAASC,OAAO,IAAIC,kBAAkB,QAAQ,yBAAyB;AACvE,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,OAAOC,oBAAoB,MAAM,2BAA2B;AAC5D,OAAOC,6BAA6B,MAAM,6BAA6B;AACvE,OAAO,SAASJ,OAAOA,CAACK,SAAS,EAAE;EACjCN,GAAG,CAACE,kBAAkB,CAAC;EACvBI,SAAS,CAACC,sBAAsB,CAACJ,qBAAqB,CAAC;EACvDG,SAAS,CAACE,qBAAqB,CAACJ,oBAAoB,CAAC;EACrDC,6BAA6B,CAACC,SAAS,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}