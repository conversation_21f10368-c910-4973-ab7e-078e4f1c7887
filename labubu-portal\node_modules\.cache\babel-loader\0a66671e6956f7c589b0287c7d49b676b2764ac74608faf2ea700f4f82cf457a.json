{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport TreeView from './TreeView.js';\nimport TreeSeriesModel from './TreeSeries.js';\nimport treeLayout from './treeLayout.js';\nimport treeVisual from './treeVisual.js';\nimport { installTreeAction } from './treeAction.js';\nexport function install(registers) {\n  registers.registerChartView(TreeView);\n  registers.registerSeriesModel(TreeSeriesModel);\n  registers.registerLayout(treeLayout);\n  registers.registerVisual(treeVisual);\n  installTreeAction(registers);\n}", "map": {"version": 3, "names": ["TreeView", "TreeSeriesModel", "treeLayout", "treeVisual", "installTreeAction", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "registerVisual"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/tree/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport TreeView from './TreeView.js';\nimport TreeSeriesModel from './TreeSeries.js';\nimport treeLayout from './treeLayout.js';\nimport treeVisual from './treeVisual.js';\nimport { installTreeAction } from './treeAction.js';\nexport function install(registers) {\n  registers.registerChartView(TreeView);\n  registers.registerSeriesModel(TreeSeriesModel);\n  registers.registerLayout(treeLayout);\n  registers.registerVisual(treeVisual);\n  installTreeAction(registers);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,eAAe,MAAM,iBAAiB;AAC7C,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACP,QAAQ,CAAC;EACrCM,SAAS,CAACE,mBAAmB,CAACP,eAAe,CAAC;EAC9CK,SAAS,CAACG,cAAc,CAACP,UAAU,CAAC;EACpCI,SAAS,CAACI,cAAc,CAACP,UAAU,CAAC;EACpCC,iBAAiB,CAACE,SAAS,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}