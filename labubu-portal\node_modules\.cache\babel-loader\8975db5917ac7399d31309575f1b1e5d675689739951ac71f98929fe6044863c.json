{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Fix for 钓鱼岛\n// let Region = require('../Region');\n// let zrUtil = require('zrender/lib/core/util');\n// let geoCoord = [126, 25];\nvar points = [[[123.45165252685547, 25.73527164402261], [123.49731445312499, 25.73527164402261], [123.49731445312499, 25.750734064600884], [123.45165252685547, 25.750734064600884], [123.45165252685547, 25.73527164402261]]];\nexport default function fixDiaoyuIsland(mapType, region) {\n  if (mapType === 'china' && region.name === '台湾') {\n    region.geometries.push({\n      type: 'polygon',\n      exterior: points[0]\n    });\n  }\n}", "map": {"version": 3, "names": ["points", "fixDiaoyuIsland", "mapType", "region", "name", "geometries", "push", "type", "exterior"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/coord/geo/fix/diaoyuIsland.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Fix for 钓鱼岛\n// let Region = require('../Region');\n// let zrUtil = require('zrender/lib/core/util');\n// let geoCoord = [126, 25];\nvar points = [[[123.45165252685547, 25.73527164402261], [123.49731445312499, 25.73527164402261], [123.49731445312499, 25.750734064600884], [123.45165252685547, 25.750734064600884], [123.45165252685547, 25.73527164402261]]];\nexport default function fixDiaoyuIsland(mapType, region) {\n  if (mapType === 'china' && region.name === '台湾') {\n    region.geometries.push({\n      type: 'polygon',\n      exterior: points[0]\n    });\n  }\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,MAAM,GAAG,CAAC,CAAC,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAC9N,eAAe,SAASC,eAAeA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACvD,IAAID,OAAO,KAAK,OAAO,IAAIC,MAAM,CAACC,IAAI,KAAK,IAAI,EAAE;IAC/CD,MAAM,CAACE,UAAU,CAACC,IAAI,CAAC;MACrBC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAER,MAAM,CAAC,CAAC;IACpB,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}