{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport LegendModel from './LegendModel.js';\nimport LegendView from './LegendView.js';\nimport legendFilter from './legendFilter.js';\nimport { installLegendAction } from './legendAction.js';\nexport function install(registers) {\n  registers.registerComponentModel(LegendModel);\n  registers.registerComponentView(LegendView);\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.SERIES_FILTER, legendFilter);\n  registers.registerSubTypeDefaulter('legend', function () {\n    return 'plain';\n  });\n  installLegendAction(registers);\n}", "map": {"version": 3, "names": ["LegendModel", "LegendView", "<PERSON><PERSON><PERSON>er", "installLegendAction", "install", "registers", "registerComponentModel", "registerComponentView", "registerProcessor", "PRIORITY", "PROCESSOR", "SERIES_FILTER", "registerSubTypeDefaulter"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/legend/installLegendPlain.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport LegendModel from './LegendModel.js';\nimport LegendView from './LegendView.js';\nimport legendFilter from './legendFilter.js';\nimport { installLegendAction } from './legendAction.js';\nexport function install(registers) {\n  registers.registerComponentModel(LegendModel);\n  registers.registerComponentView(LegendView);\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.SERIES_FILTER, legendFilter);\n  registers.registerSubTypeDefaulter('legend', function () {\n    return 'plain';\n  });\n  installLegendAction(registers);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,sBAAsB,CAACN,WAAW,CAAC;EAC7CK,SAAS,CAACE,qBAAqB,CAACN,UAAU,CAAC;EAC3CI,SAAS,CAACG,iBAAiB,CAACH,SAAS,CAACI,QAAQ,CAACC,SAAS,CAACC,aAAa,EAAET,YAAY,CAAC;EACrFG,SAAS,CAACO,wBAAwB,CAAC,QAAQ,EAAE,YAAY;IACvD,OAAO,OAAO;EAChB,CAAC,CAAC;EACFT,mBAAmB,CAACE,SAAS,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}