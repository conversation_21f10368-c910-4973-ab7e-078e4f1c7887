{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, isFunction, keys } from 'zrender/lib/core/util.js';\nvar SYMBOL_PROPS_WITH_CB = ['symbol', 'symbolSize', 'symbolRotate', 'symbolOffset'];\nvar SYMBOL_PROPS = SYMBOL_PROPS_WITH_CB.concat(['symbolKeepAspect']);\n// Encoding visual for all series include which is filtered for legend drawing\nvar seriesSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    if (seriesModel.legendIcon) {\n      data.setVisual('legendIcon', seriesModel.legendIcon);\n    }\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    var symbolOptions = {};\n    var symbolOptionsCb = {};\n    var hasCallback = false;\n    for (var i = 0; i < SYMBOL_PROPS_WITH_CB.length; i++) {\n      var symbolPropName = SYMBOL_PROPS_WITH_CB[i];\n      var val = seriesModel.get(symbolPropName);\n      if (isFunction(val)) {\n        hasCallback = true;\n        symbolOptionsCb[symbolPropName] = val;\n      } else {\n        symbolOptions[symbolPropName] = val;\n      }\n    }\n    symbolOptions.symbol = symbolOptions.symbol || seriesModel.defaultSymbol;\n    data.setVisual(extend({\n      legendIcon: seriesModel.legendIcon || symbolOptions.symbol,\n      symbolKeepAspect: seriesModel.get('symbolKeepAspect')\n    }, symbolOptions));\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var symbolPropsCb = keys(symbolOptionsCb);\n    function dataEach(data, idx) {\n      var rawValue = seriesModel.getRawValue(idx);\n      var params = seriesModel.getDataParams(idx);\n      for (var i = 0; i < symbolPropsCb.length; i++) {\n        var symbolPropName = symbolPropsCb[i];\n        data.setItemVisual(idx, symbolPropName, symbolOptionsCb[symbolPropName](rawValue, params));\n      }\n    }\n    return {\n      dataEach: hasCallback ? dataEach : null\n    };\n  }\n};\nvar dataSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    function dataEach(data, idx) {\n      var itemModel = data.getItemModel(idx);\n      for (var i = 0; i < SYMBOL_PROPS.length; i++) {\n        var symbolPropName = SYMBOL_PROPS[i];\n        var val = itemModel.getShallow(symbolPropName, true);\n        if (val != null) {\n          data.setItemVisual(idx, symbolPropName, val);\n        }\n      }\n    }\n    return {\n      dataEach: data.hasItemOption ? dataEach : null\n    };\n  }\n};\nexport { seriesSymbolTask, dataSymbolTask };", "map": {"version": 3, "names": ["extend", "isFunction", "keys", "SYMBOL_PROPS_WITH_CB", "SYMBOL_PROPS", "concat", "seriesSymbolTask", "createOnAllSeries", "performRawSeries", "reset", "seriesModel", "ecModel", "data", "getData", "legendIcon", "setVisual", "hasSymbolVisual", "symbolOptions", "symbolOptionsCb", "<PERSON><PERSON><PERSON><PERSON>", "i", "length", "symbolPropName", "val", "get", "symbol", "defaultSymbol", "symbolKeepAspect", "isSeriesFiltered", "symbolPropsCb", "dataEach", "idx", "rawValue", "getRawValue", "params", "getDataParams", "setItemVisual", "dataSymbolTask", "itemModel", "getItemModel", "getShallow", "hasItemOption"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/visual/symbol.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, isFunction, keys } from 'zrender/lib/core/util.js';\nvar SYMBOL_PROPS_WITH_CB = ['symbol', 'symbolSize', 'symbolRotate', 'symbolOffset'];\nvar SYMBOL_PROPS = SYMBOL_PROPS_WITH_CB.concat(['symbolKeepAspect']);\n// Encoding visual for all series include which is filtered for legend drawing\nvar seriesSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    if (seriesModel.legendIcon) {\n      data.setVisual('legendIcon', seriesModel.legendIcon);\n    }\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    var symbolOptions = {};\n    var symbolOptionsCb = {};\n    var hasCallback = false;\n    for (var i = 0; i < SYMBOL_PROPS_WITH_CB.length; i++) {\n      var symbolPropName = SYMBOL_PROPS_WITH_CB[i];\n      var val = seriesModel.get(symbolPropName);\n      if (isFunction(val)) {\n        hasCallback = true;\n        symbolOptionsCb[symbolPropName] = val;\n      } else {\n        symbolOptions[symbolPropName] = val;\n      }\n    }\n    symbolOptions.symbol = symbolOptions.symbol || seriesModel.defaultSymbol;\n    data.setVisual(extend({\n      legendIcon: seriesModel.legendIcon || symbolOptions.symbol,\n      symbolKeepAspect: seriesModel.get('symbolKeepAspect')\n    }, symbolOptions));\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var symbolPropsCb = keys(symbolOptionsCb);\n    function dataEach(data, idx) {\n      var rawValue = seriesModel.getRawValue(idx);\n      var params = seriesModel.getDataParams(idx);\n      for (var i = 0; i < symbolPropsCb.length; i++) {\n        var symbolPropName = symbolPropsCb[i];\n        data.setItemVisual(idx, symbolPropName, symbolOptionsCb[symbolPropName](rawValue, params));\n      }\n    }\n    return {\n      dataEach: hasCallback ? dataEach : null\n    };\n  }\n};\nvar dataSymbolTask = {\n  createOnAllSeries: true,\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    if (!seriesModel.hasSymbolVisual) {\n      return;\n    }\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var data = seriesModel.getData();\n    function dataEach(data, idx) {\n      var itemModel = data.getItemModel(idx);\n      for (var i = 0; i < SYMBOL_PROPS.length; i++) {\n        var symbolPropName = SYMBOL_PROPS[i];\n        var val = itemModel.getShallow(symbolPropName, true);\n        if (val != null) {\n          data.setItemVisual(idx, symbolPropName, val);\n        }\n      }\n    }\n    return {\n      dataEach: data.hasItemOption ? dataEach : null\n    };\n  }\n};\nexport { seriesSymbolTask, dataSymbolTask };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,UAAU,EAAEC,IAAI,QAAQ,0BAA0B;AACnE,IAAIC,oBAAoB,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;AACnF,IAAIC,YAAY,GAAGD,oBAAoB,CAACE,MAAM,CAAC,CAAC,kBAAkB,CAAC,CAAC;AACpE;AACA,IAAIC,gBAAgB,GAAG;EACrBC,iBAAiB,EAAE,IAAI;EACvB;EACAC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAE;IACrC,IAAIC,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;IAChC,IAAIH,WAAW,CAACI,UAAU,EAAE;MAC1BF,IAAI,CAACG,SAAS,CAAC,YAAY,EAAEL,WAAW,CAACI,UAAU,CAAC;IACtD;IACA,IAAI,CAACJ,WAAW,CAACM,eAAe,EAAE;MAChC;IACF;IACA,IAAIC,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIC,eAAe,GAAG,CAAC,CAAC;IACxB,IAAIC,WAAW,GAAG,KAAK;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,oBAAoB,CAACkB,MAAM,EAAED,CAAC,EAAE,EAAE;MACpD,IAAIE,cAAc,GAAGnB,oBAAoB,CAACiB,CAAC,CAAC;MAC5C,IAAIG,GAAG,GAAGb,WAAW,CAACc,GAAG,CAACF,cAAc,CAAC;MACzC,IAAIrB,UAAU,CAACsB,GAAG,CAAC,EAAE;QACnBJ,WAAW,GAAG,IAAI;QAClBD,eAAe,CAACI,cAAc,CAAC,GAAGC,GAAG;MACvC,CAAC,MAAM;QACLN,aAAa,CAACK,cAAc,CAAC,GAAGC,GAAG;MACrC;IACF;IACAN,aAAa,CAACQ,MAAM,GAAGR,aAAa,CAACQ,MAAM,IAAIf,WAAW,CAACgB,aAAa;IACxEd,IAAI,CAACG,SAAS,CAACf,MAAM,CAAC;MACpBc,UAAU,EAAEJ,WAAW,CAACI,UAAU,IAAIG,aAAa,CAACQ,MAAM;MAC1DE,gBAAgB,EAAEjB,WAAW,CAACc,GAAG,CAAC,kBAAkB;IACtD,CAAC,EAAEP,aAAa,CAAC,CAAC;IAClB;IACA,IAAIN,OAAO,CAACiB,gBAAgB,CAAClB,WAAW,CAAC,EAAE;MACzC;IACF;IACA,IAAImB,aAAa,GAAG3B,IAAI,CAACgB,eAAe,CAAC;IACzC,SAASY,QAAQA,CAAClB,IAAI,EAAEmB,GAAG,EAAE;MAC3B,IAAIC,QAAQ,GAAGtB,WAAW,CAACuB,WAAW,CAACF,GAAG,CAAC;MAC3C,IAAIG,MAAM,GAAGxB,WAAW,CAACyB,aAAa,CAACJ,GAAG,CAAC;MAC3C,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,aAAa,CAACR,MAAM,EAAED,CAAC,EAAE,EAAE;QAC7C,IAAIE,cAAc,GAAGO,aAAa,CAACT,CAAC,CAAC;QACrCR,IAAI,CAACwB,aAAa,CAACL,GAAG,EAAET,cAAc,EAAEJ,eAAe,CAACI,cAAc,CAAC,CAACU,QAAQ,EAAEE,MAAM,CAAC,CAAC;MAC5F;IACF;IACA,OAAO;MACLJ,QAAQ,EAAEX,WAAW,GAAGW,QAAQ,GAAG;IACrC,CAAC;EACH;AACF,CAAC;AACD,IAAIO,cAAc,GAAG;EACnB9B,iBAAiB,EAAE,IAAI;EACvB;EACAC,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAE;IACrC,IAAI,CAACD,WAAW,CAACM,eAAe,EAAE;MAChC;IACF;IACA;IACA,IAAIL,OAAO,CAACiB,gBAAgB,CAAClB,WAAW,CAAC,EAAE;MACzC;IACF;IACA,IAAIE,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;IAChC,SAASiB,QAAQA,CAAClB,IAAI,EAAEmB,GAAG,EAAE;MAC3B,IAAIO,SAAS,GAAG1B,IAAI,CAAC2B,YAAY,CAACR,GAAG,CAAC;MACtC,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,YAAY,CAACiB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5C,IAAIE,cAAc,GAAGlB,YAAY,CAACgB,CAAC,CAAC;QACpC,IAAIG,GAAG,GAAGe,SAAS,CAACE,UAAU,CAAClB,cAAc,EAAE,IAAI,CAAC;QACpD,IAAIC,GAAG,IAAI,IAAI,EAAE;UACfX,IAAI,CAACwB,aAAa,CAACL,GAAG,EAAET,cAAc,EAAEC,GAAG,CAAC;QAC9C;MACF;IACF;IACA,OAAO;MACLO,QAAQ,EAAElB,IAAI,CAAC6B,aAAa,GAAGX,QAAQ,GAAG;IAC5C,CAAC;EACH;AACF,CAAC;AACD,SAASxB,gBAAgB,EAAE+B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}