{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../util/model.js';\nimport { makeLabelFormatter, getOptionCategoryInterval, shouldShowAllLabels } from './axisHelper.js';\nvar inner = makeInner();\nfunction tickValuesToNumbers(axis, values) {\n  var nums = zrUtil.map(values, function (val) {\n    return axis.scale.parse(val);\n  });\n  if (axis.type === 'time' && nums.length > 0) {\n    // Time axis needs duplicate first/last tick (see TimeScale.getTicks())\n    // The first and last tick/label don't get drawn\n    nums.sort();\n    nums.unshift(nums[0]);\n    nums.push(nums[nums.length - 1]);\n  }\n  return nums;\n}\nexport function createAxisLabels(axis) {\n  var custom = axis.getLabelModel().get('customValues');\n  if (custom) {\n    var labelFormatter_1 = makeLabelFormatter(axis);\n    var extent_1 = axis.scale.getExtent();\n    var tickNumbers = tickValuesToNumbers(axis, custom);\n    var ticks = zrUtil.filter(tickNumbers, function (val) {\n      return val >= extent_1[0] && val <= extent_1[1];\n    });\n    return {\n      labels: zrUtil.map(ticks, function (numval) {\n        var tick = {\n          value: numval\n        };\n        return {\n          formattedLabel: labelFormatter_1(tick),\n          rawLabel: axis.scale.getLabel(tick),\n          tickValue: numval\n        };\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryLabels(axis) : makeRealNumberLabels(axis);\n}\n/**\r\n * @param {module:echats/coord/Axis} axis\r\n * @param {module:echarts/model/Model} tickModel For example, can be axisTick, splitLine, splitArea.\r\n * @return {Object} {\r\n *     ticks: Array.<number>\r\n *     tickCategoryInterval: number\r\n * }\r\n */\nexport function createAxisTicks(axis, tickModel) {\n  var custom = axis.getTickModel().get('customValues');\n  if (custom) {\n    var extent_2 = axis.scale.getExtent();\n    var tickNumbers = tickValuesToNumbers(axis, custom);\n    return {\n      ticks: zrUtil.filter(tickNumbers, function (val) {\n        return val >= extent_2[0] && val <= extent_2[1];\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryTicks(axis, tickModel) : {\n    ticks: zrUtil.map(axis.scale.getTicks(), function (tick) {\n      return tick.value;\n    })\n  };\n}\nfunction makeCategoryLabels(axis) {\n  var labelModel = axis.getLabelModel();\n  var result = makeCategoryLabelsActually(axis, labelModel);\n  return !labelModel.get('show') || axis.scale.isBlank() ? {\n    labels: [],\n    labelCategoryInterval: result.labelCategoryInterval\n  } : result;\n}\nfunction makeCategoryLabelsActually(axis, labelModel) {\n  var labelsCache = getListCache(axis, 'labels');\n  var optionLabelInterval = getOptionCategoryInterval(labelModel);\n  var result = listCacheGet(labelsCache, optionLabelInterval);\n  if (result) {\n    return result;\n  }\n  var labels;\n  var numericLabelInterval;\n  if (zrUtil.isFunction(optionLabelInterval)) {\n    labels = makeLabelsByCustomizedCategoryInterval(axis, optionLabelInterval);\n  } else {\n    numericLabelInterval = optionLabelInterval === 'auto' ? makeAutoCategoryInterval(axis) : optionLabelInterval;\n    labels = makeLabelsByNumericCategoryInterval(axis, numericLabelInterval);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(labelsCache, optionLabelInterval, {\n    labels: labels,\n    labelCategoryInterval: numericLabelInterval\n  });\n}\nfunction makeCategoryTicks(axis, tickModel) {\n  var ticksCache = getListCache(axis, 'ticks');\n  var optionTickInterval = getOptionCategoryInterval(tickModel);\n  var result = listCacheGet(ticksCache, optionTickInterval);\n  if (result) {\n    return result;\n  }\n  var ticks;\n  var tickCategoryInterval;\n  // Optimize for the case that large category data and no label displayed,\n  // we should not return all ticks.\n  if (!tickModel.get('show') || axis.scale.isBlank()) {\n    ticks = [];\n  }\n  if (zrUtil.isFunction(optionTickInterval)) {\n    ticks = makeLabelsByCustomizedCategoryInterval(axis, optionTickInterval, true);\n  }\n  // Always use label interval by default despite label show. Consider this\n  // scenario, Use multiple grid with the xAxis sync, and only one xAxis shows\n  // labels. `splitLine` and `axisTick` should be consistent in this case.\n  else if (optionTickInterval === 'auto') {\n    var labelsResult = makeCategoryLabelsActually(axis, axis.getLabelModel());\n    tickCategoryInterval = labelsResult.labelCategoryInterval;\n    ticks = zrUtil.map(labelsResult.labels, function (labelItem) {\n      return labelItem.tickValue;\n    });\n  } else {\n    tickCategoryInterval = optionTickInterval;\n    ticks = makeLabelsByNumericCategoryInterval(axis, tickCategoryInterval, true);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(ticksCache, optionTickInterval, {\n    ticks: ticks,\n    tickCategoryInterval: tickCategoryInterval\n  });\n}\nfunction makeRealNumberLabels(axis) {\n  var ticks = axis.scale.getTicks();\n  var labelFormatter = makeLabelFormatter(axis);\n  return {\n    labels: zrUtil.map(ticks, function (tick, idx) {\n      return {\n        level: tick.level,\n        formattedLabel: labelFormatter(tick, idx),\n        rawLabel: axis.scale.getLabel(tick),\n        tickValue: tick.value\n      };\n    })\n  };\n}\nfunction getListCache(axis, prop) {\n  // Because key can be a function, and cache size always is small, we use array cache.\n  return inner(axis)[prop] || (inner(axis)[prop] = []);\n}\nfunction listCacheGet(cache, key) {\n  for (var i = 0; i < cache.length; i++) {\n    if (cache[i].key === key) {\n      return cache[i].value;\n    }\n  }\n}\nfunction listCacheSet(cache, key, value) {\n  cache.push({\n    key: key,\n    value: value\n  });\n  return value;\n}\nfunction makeAutoCategoryInterval(axis) {\n  var result = inner(axis).autoInterval;\n  return result != null ? result : inner(axis).autoInterval = axis.calculateCategoryInterval();\n}\n/**\r\n * Calculate interval for category axis ticks and labels.\r\n * To get precise result, at least one of `getRotate` and `isHorizontal`\r\n * should be implemented in axis.\r\n */\nexport function calculateCategoryInterval(axis) {\n  var params = fetchAutoCategoryIntervalCalculationParams(axis);\n  var labelFormatter = makeLabelFormatter(axis);\n  var rotation = (params.axisRotate - params.labelRotate) / 180 * Math.PI;\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  // Providing this method is for optimization:\n  // avoid generating a long array by `getTicks`\n  // in large category data case.\n  var tickCount = ordinalScale.count();\n  if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n    return 0;\n  }\n  var step = 1;\n  // Simple optimization. Empirical value: tick count should less than 40.\n  if (tickCount > 40) {\n    step = Math.max(1, Math.floor(tickCount / 40));\n  }\n  var tickValue = ordinalExtent[0];\n  var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n  var unitW = Math.abs(unitSpan * Math.cos(rotation));\n  var unitH = Math.abs(unitSpan * Math.sin(rotation));\n  var maxW = 0;\n  var maxH = 0;\n  // Caution: Performance sensitive for large category data.\n  // Consider dataZoom, we should make appropriate step to avoid O(n) loop.\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    var width = 0;\n    var height = 0;\n    // Not precise, do not consider align and vertical align\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(labelFormatter({\n      value: tickValue\n    }), params.font, 'center', 'top');\n    // Magic number\n    width = rect.width * 1.3;\n    height = rect.height * 1.3;\n    // Min size, void long loop.\n    maxW = Math.max(maxW, width, 7);\n    maxH = Math.max(maxH, height, 7);\n  }\n  var dw = maxW / unitW;\n  var dh = maxH / unitH;\n  // 0/0 is NaN, 1/0 is Infinity.\n  isNaN(dw) && (dw = Infinity);\n  isNaN(dh) && (dh = Infinity);\n  var interval = Math.max(0, Math.floor(Math.min(dw, dh)));\n  var cache = inner(axis.model);\n  var axisExtent = axis.getExtent();\n  var lastAutoInterval = cache.lastAutoInterval;\n  var lastTickCount = cache.lastTickCount;\n  // Use cache to keep interval stable while moving zoom window,\n  // otherwise the calculated interval might jitter when the zoom\n  // window size is close to the interval-changing size.\n  // For example, if all of the axis labels are `a, b, c, d, e, f, g`.\n  // The jitter will cause that sometimes the displayed labels are\n  // `a, d, g` (interval: 2) sometimes `a, c, e`(interval: 1).\n  if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n  // Always choose the bigger one, otherwise the critical\n  // point is not the same when zooming in or zooming out.\n  && lastAutoInterval > interval\n  // If the axis change is caused by chart resize, the cache should not\n  // be used. Otherwise some hidden labels might not be shown again.\n  && cache.axisExtent0 === axisExtent[0] && cache.axisExtent1 === axisExtent[1]) {\n    interval = lastAutoInterval;\n  }\n  // Only update cache if cache not used, otherwise the\n  // changing of interval is too insensitive.\n  else {\n    cache.lastTickCount = tickCount;\n    cache.lastAutoInterval = interval;\n    cache.axisExtent0 = axisExtent[0];\n    cache.axisExtent1 = axisExtent[1];\n  }\n  return interval;\n}\nfunction fetchAutoCategoryIntervalCalculationParams(axis) {\n  var labelModel = axis.getLabelModel();\n  return {\n    axisRotate: axis.getRotate ? axis.getRotate() : axis.isHorizontal && !axis.isHorizontal() ? 90 : 0,\n    labelRotate: labelModel.get('rotate') || 0,\n    font: labelModel.getFont()\n  };\n}\nfunction makeLabelsByNumericCategoryInterval(axis, categoryInterval, onlyTick) {\n  var labelFormatter = makeLabelFormatter(axis);\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  var labelModel = axis.getLabelModel();\n  var result = [];\n  // TODO: axisType: ordinalTime, pick the tick from each month/day/year/...\n  var step = Math.max((categoryInterval || 0) + 1, 1);\n  var startTick = ordinalExtent[0];\n  var tickCount = ordinalScale.count();\n  // Calculate start tick based on zero if possible to keep label consistent\n  // while zooming and moving while interval > 0. Otherwise the selection\n  // of displayable ticks and symbols probably keep changing.\n  // 3 is empirical value.\n  if (startTick !== 0 && step > 1 && tickCount / step > 2) {\n    startTick = Math.round(Math.ceil(startTick / step) * step);\n  }\n  // (1) Only add min max label here but leave overlap checking\n  // to render stage, which also ensure the returned list\n  // suitable for splitLine and splitArea rendering.\n  // (2) Scales except category always contain min max label so\n  // do not need to perform this process.\n  var showAllLabel = shouldShowAllLabels(axis);\n  var includeMinLabel = labelModel.get('showMinLabel') || showAllLabel;\n  var includeMaxLabel = labelModel.get('showMaxLabel') || showAllLabel;\n  if (includeMinLabel && startTick !== ordinalExtent[0]) {\n    addItem(ordinalExtent[0]);\n  }\n  // Optimize: avoid generating large array by `ordinalScale.getTicks()`.\n  var tickValue = startTick;\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    addItem(tickValue);\n  }\n  if (includeMaxLabel && tickValue - step !== ordinalExtent[1]) {\n    addItem(ordinalExtent[1]);\n  }\n  function addItem(tickValue) {\n    var tickObj = {\n      value: tickValue\n    };\n    result.push(onlyTick ? tickValue : {\n      formattedLabel: labelFormatter(tickObj),\n      rawLabel: ordinalScale.getLabel(tickObj),\n      tickValue: tickValue\n    });\n  }\n  return result;\n}\nfunction makeLabelsByCustomizedCategoryInterval(axis, categoryInterval, onlyTick) {\n  var ordinalScale = axis.scale;\n  var labelFormatter = makeLabelFormatter(axis);\n  var result = [];\n  zrUtil.each(ordinalScale.getTicks(), function (tick) {\n    var rawLabel = ordinalScale.getLabel(tick);\n    var tickValue = tick.value;\n    if (categoryInterval(tick.value, rawLabel)) {\n      result.push(onlyTick ? tickValue : {\n        formattedLabel: labelFormatter(tick),\n        rawLabel: rawLabel,\n        tickValue: tickValue\n      });\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["zrUtil", "textContain", "makeInner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getOptionCategoryInterval", "shouldShowAllLabels", "inner", "tickValuesToNumbers", "axis", "values", "nums", "map", "val", "scale", "parse", "type", "length", "sort", "unshift", "push", "createAxisLabels", "custom", "getLabelModel", "get", "labelFormatter_1", "extent_1", "getExtent", "tickNumbers", "ticks", "filter", "labels", "numval", "tick", "value", "formattedLabel", "rawLabel", "get<PERSON><PERSON><PERSON>", "tickValue", "makeCategoryLabels", "makeRealNumberLabels", "createAxisTicks", "tickModel", "getTickModel", "extent_2", "makeCategoryTicks", "getTicks", "labelModel", "result", "makeCategoryLabelsActually", "isBlank", "labelCategoryInterval", "labelsCache", "getListCache", "optionLabelInterval", "listCacheGet", "numericLabelInterval", "isFunction", "makeLabelsByCustomizedCategoryInterval", "makeAutoCategoryInterval", "makeLabelsByNumericCategoryInterval", "listCacheSet", "ticksCache", "optionTickInterval", "tickCategoryInterval", "labelsResult", "labelItem", "labelFormatter", "idx", "level", "prop", "cache", "key", "i", "autoInterval", "calculateCategoryInterval", "params", "fetchAutoCategoryIntervalCalculationParams", "rotation", "axisRotate", "labelRotate", "Math", "PI", "ordinalScale", "ordinalExtent", "tickCount", "count", "step", "max", "floor", "unitSpan", "dataToCoord", "unitW", "abs", "cos", "unitH", "sin", "maxW", "maxH", "width", "height", "rect", "getBoundingRect", "font", "dw", "dh", "isNaN", "Infinity", "interval", "min", "model", "axisExtent", "lastAutoInterval", "lastTickCount", "axisExtent0", "axisExtent1", "getRotate", "isHorizontal", "getFont", "categoryInterval", "onlyTick", "startTick", "round", "ceil", "showAllLabel", "includeMinLabel", "includeMaxLabel", "addItem", "tickObj", "each"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/coord/axisTickLabelBuilder.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../util/model.js';\nimport { makeLabelFormatter, getOptionCategoryInterval, shouldShowAllLabels } from './axisHelper.js';\nvar inner = makeInner();\nfunction tickValuesToNumbers(axis, values) {\n  var nums = zrUtil.map(values, function (val) {\n    return axis.scale.parse(val);\n  });\n  if (axis.type === 'time' && nums.length > 0) {\n    // Time axis needs duplicate first/last tick (see TimeScale.getTicks())\n    // The first and last tick/label don't get drawn\n    nums.sort();\n    nums.unshift(nums[0]);\n    nums.push(nums[nums.length - 1]);\n  }\n  return nums;\n}\nexport function createAxisLabels(axis) {\n  var custom = axis.getLabelModel().get('customValues');\n  if (custom) {\n    var labelFormatter_1 = makeLabelFormatter(axis);\n    var extent_1 = axis.scale.getExtent();\n    var tickNumbers = tickValuesToNumbers(axis, custom);\n    var ticks = zrUtil.filter(tickNumbers, function (val) {\n      return val >= extent_1[0] && val <= extent_1[1];\n    });\n    return {\n      labels: zrUtil.map(ticks, function (numval) {\n        var tick = {\n          value: numval\n        };\n        return {\n          formattedLabel: labelFormatter_1(tick),\n          rawLabel: axis.scale.getLabel(tick),\n          tickValue: numval\n        };\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryLabels(axis) : makeRealNumberLabels(axis);\n}\n/**\r\n * @param {module:echats/coord/Axis} axis\r\n * @param {module:echarts/model/Model} tickModel For example, can be axisTick, splitLine, splitArea.\r\n * @return {Object} {\r\n *     ticks: Array.<number>\r\n *     tickCategoryInterval: number\r\n * }\r\n */\nexport function createAxisTicks(axis, tickModel) {\n  var custom = axis.getTickModel().get('customValues');\n  if (custom) {\n    var extent_2 = axis.scale.getExtent();\n    var tickNumbers = tickValuesToNumbers(axis, custom);\n    return {\n      ticks: zrUtil.filter(tickNumbers, function (val) {\n        return val >= extent_2[0] && val <= extent_2[1];\n      })\n    };\n  }\n  // Only ordinal scale support tick interval\n  return axis.type === 'category' ? makeCategoryTicks(axis, tickModel) : {\n    ticks: zrUtil.map(axis.scale.getTicks(), function (tick) {\n      return tick.value;\n    })\n  };\n}\nfunction makeCategoryLabels(axis) {\n  var labelModel = axis.getLabelModel();\n  var result = makeCategoryLabelsActually(axis, labelModel);\n  return !labelModel.get('show') || axis.scale.isBlank() ? {\n    labels: [],\n    labelCategoryInterval: result.labelCategoryInterval\n  } : result;\n}\nfunction makeCategoryLabelsActually(axis, labelModel) {\n  var labelsCache = getListCache(axis, 'labels');\n  var optionLabelInterval = getOptionCategoryInterval(labelModel);\n  var result = listCacheGet(labelsCache, optionLabelInterval);\n  if (result) {\n    return result;\n  }\n  var labels;\n  var numericLabelInterval;\n  if (zrUtil.isFunction(optionLabelInterval)) {\n    labels = makeLabelsByCustomizedCategoryInterval(axis, optionLabelInterval);\n  } else {\n    numericLabelInterval = optionLabelInterval === 'auto' ? makeAutoCategoryInterval(axis) : optionLabelInterval;\n    labels = makeLabelsByNumericCategoryInterval(axis, numericLabelInterval);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(labelsCache, optionLabelInterval, {\n    labels: labels,\n    labelCategoryInterval: numericLabelInterval\n  });\n}\nfunction makeCategoryTicks(axis, tickModel) {\n  var ticksCache = getListCache(axis, 'ticks');\n  var optionTickInterval = getOptionCategoryInterval(tickModel);\n  var result = listCacheGet(ticksCache, optionTickInterval);\n  if (result) {\n    return result;\n  }\n  var ticks;\n  var tickCategoryInterval;\n  // Optimize for the case that large category data and no label displayed,\n  // we should not return all ticks.\n  if (!tickModel.get('show') || axis.scale.isBlank()) {\n    ticks = [];\n  }\n  if (zrUtil.isFunction(optionTickInterval)) {\n    ticks = makeLabelsByCustomizedCategoryInterval(axis, optionTickInterval, true);\n  }\n  // Always use label interval by default despite label show. Consider this\n  // scenario, Use multiple grid with the xAxis sync, and only one xAxis shows\n  // labels. `splitLine` and `axisTick` should be consistent in this case.\n  else if (optionTickInterval === 'auto') {\n    var labelsResult = makeCategoryLabelsActually(axis, axis.getLabelModel());\n    tickCategoryInterval = labelsResult.labelCategoryInterval;\n    ticks = zrUtil.map(labelsResult.labels, function (labelItem) {\n      return labelItem.tickValue;\n    });\n  } else {\n    tickCategoryInterval = optionTickInterval;\n    ticks = makeLabelsByNumericCategoryInterval(axis, tickCategoryInterval, true);\n  }\n  // Cache to avoid calling interval function repeatedly.\n  return listCacheSet(ticksCache, optionTickInterval, {\n    ticks: ticks,\n    tickCategoryInterval: tickCategoryInterval\n  });\n}\nfunction makeRealNumberLabels(axis) {\n  var ticks = axis.scale.getTicks();\n  var labelFormatter = makeLabelFormatter(axis);\n  return {\n    labels: zrUtil.map(ticks, function (tick, idx) {\n      return {\n        level: tick.level,\n        formattedLabel: labelFormatter(tick, idx),\n        rawLabel: axis.scale.getLabel(tick),\n        tickValue: tick.value\n      };\n    })\n  };\n}\nfunction getListCache(axis, prop) {\n  // Because key can be a function, and cache size always is small, we use array cache.\n  return inner(axis)[prop] || (inner(axis)[prop] = []);\n}\nfunction listCacheGet(cache, key) {\n  for (var i = 0; i < cache.length; i++) {\n    if (cache[i].key === key) {\n      return cache[i].value;\n    }\n  }\n}\nfunction listCacheSet(cache, key, value) {\n  cache.push({\n    key: key,\n    value: value\n  });\n  return value;\n}\nfunction makeAutoCategoryInterval(axis) {\n  var result = inner(axis).autoInterval;\n  return result != null ? result : inner(axis).autoInterval = axis.calculateCategoryInterval();\n}\n/**\r\n * Calculate interval for category axis ticks and labels.\r\n * To get precise result, at least one of `getRotate` and `isHorizontal`\r\n * should be implemented in axis.\r\n */\nexport function calculateCategoryInterval(axis) {\n  var params = fetchAutoCategoryIntervalCalculationParams(axis);\n  var labelFormatter = makeLabelFormatter(axis);\n  var rotation = (params.axisRotate - params.labelRotate) / 180 * Math.PI;\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  // Providing this method is for optimization:\n  // avoid generating a long array by `getTicks`\n  // in large category data case.\n  var tickCount = ordinalScale.count();\n  if (ordinalExtent[1] - ordinalExtent[0] < 1) {\n    return 0;\n  }\n  var step = 1;\n  // Simple optimization. Empirical value: tick count should less than 40.\n  if (tickCount > 40) {\n    step = Math.max(1, Math.floor(tickCount / 40));\n  }\n  var tickValue = ordinalExtent[0];\n  var unitSpan = axis.dataToCoord(tickValue + 1) - axis.dataToCoord(tickValue);\n  var unitW = Math.abs(unitSpan * Math.cos(rotation));\n  var unitH = Math.abs(unitSpan * Math.sin(rotation));\n  var maxW = 0;\n  var maxH = 0;\n  // Caution: Performance sensitive for large category data.\n  // Consider dataZoom, we should make appropriate step to avoid O(n) loop.\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    var width = 0;\n    var height = 0;\n    // Not precise, do not consider align and vertical align\n    // and each distance from axis line yet.\n    var rect = textContain.getBoundingRect(labelFormatter({\n      value: tickValue\n    }), params.font, 'center', 'top');\n    // Magic number\n    width = rect.width * 1.3;\n    height = rect.height * 1.3;\n    // Min size, void long loop.\n    maxW = Math.max(maxW, width, 7);\n    maxH = Math.max(maxH, height, 7);\n  }\n  var dw = maxW / unitW;\n  var dh = maxH / unitH;\n  // 0/0 is NaN, 1/0 is Infinity.\n  isNaN(dw) && (dw = Infinity);\n  isNaN(dh) && (dh = Infinity);\n  var interval = Math.max(0, Math.floor(Math.min(dw, dh)));\n  var cache = inner(axis.model);\n  var axisExtent = axis.getExtent();\n  var lastAutoInterval = cache.lastAutoInterval;\n  var lastTickCount = cache.lastTickCount;\n  // Use cache to keep interval stable while moving zoom window,\n  // otherwise the calculated interval might jitter when the zoom\n  // window size is close to the interval-changing size.\n  // For example, if all of the axis labels are `a, b, c, d, e, f, g`.\n  // The jitter will cause that sometimes the displayed labels are\n  // `a, d, g` (interval: 2) sometimes `a, c, e`(interval: 1).\n  if (lastAutoInterval != null && lastTickCount != null && Math.abs(lastAutoInterval - interval) <= 1 && Math.abs(lastTickCount - tickCount) <= 1\n  // Always choose the bigger one, otherwise the critical\n  // point is not the same when zooming in or zooming out.\n  && lastAutoInterval > interval\n  // If the axis change is caused by chart resize, the cache should not\n  // be used. Otherwise some hidden labels might not be shown again.\n  && cache.axisExtent0 === axisExtent[0] && cache.axisExtent1 === axisExtent[1]) {\n    interval = lastAutoInterval;\n  }\n  // Only update cache if cache not used, otherwise the\n  // changing of interval is too insensitive.\n  else {\n    cache.lastTickCount = tickCount;\n    cache.lastAutoInterval = interval;\n    cache.axisExtent0 = axisExtent[0];\n    cache.axisExtent1 = axisExtent[1];\n  }\n  return interval;\n}\nfunction fetchAutoCategoryIntervalCalculationParams(axis) {\n  var labelModel = axis.getLabelModel();\n  return {\n    axisRotate: axis.getRotate ? axis.getRotate() : axis.isHorizontal && !axis.isHorizontal() ? 90 : 0,\n    labelRotate: labelModel.get('rotate') || 0,\n    font: labelModel.getFont()\n  };\n}\nfunction makeLabelsByNumericCategoryInterval(axis, categoryInterval, onlyTick) {\n  var labelFormatter = makeLabelFormatter(axis);\n  var ordinalScale = axis.scale;\n  var ordinalExtent = ordinalScale.getExtent();\n  var labelModel = axis.getLabelModel();\n  var result = [];\n  // TODO: axisType: ordinalTime, pick the tick from each month/day/year/...\n  var step = Math.max((categoryInterval || 0) + 1, 1);\n  var startTick = ordinalExtent[0];\n  var tickCount = ordinalScale.count();\n  // Calculate start tick based on zero if possible to keep label consistent\n  // while zooming and moving while interval > 0. Otherwise the selection\n  // of displayable ticks and symbols probably keep changing.\n  // 3 is empirical value.\n  if (startTick !== 0 && step > 1 && tickCount / step > 2) {\n    startTick = Math.round(Math.ceil(startTick / step) * step);\n  }\n  // (1) Only add min max label here but leave overlap checking\n  // to render stage, which also ensure the returned list\n  // suitable for splitLine and splitArea rendering.\n  // (2) Scales except category always contain min max label so\n  // do not need to perform this process.\n  var showAllLabel = shouldShowAllLabels(axis);\n  var includeMinLabel = labelModel.get('showMinLabel') || showAllLabel;\n  var includeMaxLabel = labelModel.get('showMaxLabel') || showAllLabel;\n  if (includeMinLabel && startTick !== ordinalExtent[0]) {\n    addItem(ordinalExtent[0]);\n  }\n  // Optimize: avoid generating large array by `ordinalScale.getTicks()`.\n  var tickValue = startTick;\n  for (; tickValue <= ordinalExtent[1]; tickValue += step) {\n    addItem(tickValue);\n  }\n  if (includeMaxLabel && tickValue - step !== ordinalExtent[1]) {\n    addItem(ordinalExtent[1]);\n  }\n  function addItem(tickValue) {\n    var tickObj = {\n      value: tickValue\n    };\n    result.push(onlyTick ? tickValue : {\n      formattedLabel: labelFormatter(tickObj),\n      rawLabel: ordinalScale.getLabel(tickObj),\n      tickValue: tickValue\n    });\n  }\n  return result;\n}\nfunction makeLabelsByCustomizedCategoryInterval(axis, categoryInterval, onlyTick) {\n  var ordinalScale = axis.scale;\n  var labelFormatter = makeLabelFormatter(axis);\n  var result = [];\n  zrUtil.each(ordinalScale.getTicks(), function (tick) {\n    var rawLabel = ordinalScale.getLabel(tick);\n    var tickValue = tick.value;\n    if (categoryInterval(tick.value, rawLabel)) {\n      result.push(onlyTick ? tickValue : {\n        formattedLabel: labelFormatter(tick),\n        rawLabel: rawLabel,\n        tickValue: tickValue\n      });\n    }\n  });\n  return result;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,WAAW,MAAM,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,kBAAkB,EAAEC,yBAAyB,EAAEC,mBAAmB,QAAQ,iBAAiB;AACpG,IAAIC,KAAK,GAAGJ,SAAS,CAAC,CAAC;AACvB,SAASK,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACzC,IAAIC,IAAI,GAAGV,MAAM,CAACW,GAAG,CAACF,MAAM,EAAE,UAAUG,GAAG,EAAE;IAC3C,OAAOJ,IAAI,CAACK,KAAK,CAACC,KAAK,CAACF,GAAG,CAAC;EAC9B,CAAC,CAAC;EACF,IAAIJ,IAAI,CAACO,IAAI,KAAK,MAAM,IAAIL,IAAI,CAACM,MAAM,GAAG,CAAC,EAAE;IAC3C;IACA;IACAN,IAAI,CAACO,IAAI,CAAC,CAAC;IACXP,IAAI,CAACQ,OAAO,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;IACrBA,IAAI,CAACS,IAAI,CAACT,IAAI,CAACA,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC;EAClC;EACA,OAAON,IAAI;AACb;AACA,OAAO,SAASU,gBAAgBA,CAACZ,IAAI,EAAE;EACrC,IAAIa,MAAM,GAAGb,IAAI,CAACc,aAAa,CAAC,CAAC,CAACC,GAAG,CAAC,cAAc,CAAC;EACrD,IAAIF,MAAM,EAAE;IACV,IAAIG,gBAAgB,GAAGrB,kBAAkB,CAACK,IAAI,CAAC;IAC/C,IAAIiB,QAAQ,GAAGjB,IAAI,CAACK,KAAK,CAACa,SAAS,CAAC,CAAC;IACrC,IAAIC,WAAW,GAAGpB,mBAAmB,CAACC,IAAI,EAAEa,MAAM,CAAC;IACnD,IAAIO,KAAK,GAAG5B,MAAM,CAAC6B,MAAM,CAACF,WAAW,EAAE,UAAUf,GAAG,EAAE;MACpD,OAAOA,GAAG,IAAIa,QAAQ,CAAC,CAAC,CAAC,IAAIb,GAAG,IAAIa,QAAQ,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC;IACF,OAAO;MACLK,MAAM,EAAE9B,MAAM,CAACW,GAAG,CAACiB,KAAK,EAAE,UAAUG,MAAM,EAAE;QAC1C,IAAIC,IAAI,GAAG;UACTC,KAAK,EAAEF;QACT,CAAC;QACD,OAAO;UACLG,cAAc,EAAEV,gBAAgB,CAACQ,IAAI,CAAC;UACtCG,QAAQ,EAAE3B,IAAI,CAACK,KAAK,CAACuB,QAAQ,CAACJ,IAAI,CAAC;UACnCK,SAAS,EAAEN;QACb,CAAC;MACH,CAAC;IACH,CAAC;EACH;EACA;EACA,OAAOvB,IAAI,CAACO,IAAI,KAAK,UAAU,GAAGuB,kBAAkB,CAAC9B,IAAI,CAAC,GAAG+B,oBAAoB,CAAC/B,IAAI,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgC,eAAeA,CAAChC,IAAI,EAAEiC,SAAS,EAAE;EAC/C,IAAIpB,MAAM,GAAGb,IAAI,CAACkC,YAAY,CAAC,CAAC,CAACnB,GAAG,CAAC,cAAc,CAAC;EACpD,IAAIF,MAAM,EAAE;IACV,IAAIsB,QAAQ,GAAGnC,IAAI,CAACK,KAAK,CAACa,SAAS,CAAC,CAAC;IACrC,IAAIC,WAAW,GAAGpB,mBAAmB,CAACC,IAAI,EAAEa,MAAM,CAAC;IACnD,OAAO;MACLO,KAAK,EAAE5B,MAAM,CAAC6B,MAAM,CAACF,WAAW,EAAE,UAAUf,GAAG,EAAE;QAC/C,OAAOA,GAAG,IAAI+B,QAAQ,CAAC,CAAC,CAAC,IAAI/B,GAAG,IAAI+B,QAAQ,CAAC,CAAC,CAAC;MACjD,CAAC;IACH,CAAC;EACH;EACA;EACA,OAAOnC,IAAI,CAACO,IAAI,KAAK,UAAU,GAAG6B,iBAAiB,CAACpC,IAAI,EAAEiC,SAAS,CAAC,GAAG;IACrEb,KAAK,EAAE5B,MAAM,CAACW,GAAG,CAACH,IAAI,CAACK,KAAK,CAACgC,QAAQ,CAAC,CAAC,EAAE,UAAUb,IAAI,EAAE;MACvD,OAAOA,IAAI,CAACC,KAAK;IACnB,CAAC;EACH,CAAC;AACH;AACA,SAASK,kBAAkBA,CAAC9B,IAAI,EAAE;EAChC,IAAIsC,UAAU,GAAGtC,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,IAAIyB,MAAM,GAAGC,0BAA0B,CAACxC,IAAI,EAAEsC,UAAU,CAAC;EACzD,OAAO,CAACA,UAAU,CAACvB,GAAG,CAAC,MAAM,CAAC,IAAIf,IAAI,CAACK,KAAK,CAACoC,OAAO,CAAC,CAAC,GAAG;IACvDnB,MAAM,EAAE,EAAE;IACVoB,qBAAqB,EAAEH,MAAM,CAACG;EAChC,CAAC,GAAGH,MAAM;AACZ;AACA,SAASC,0BAA0BA,CAACxC,IAAI,EAAEsC,UAAU,EAAE;EACpD,IAAIK,WAAW,GAAGC,YAAY,CAAC5C,IAAI,EAAE,QAAQ,CAAC;EAC9C,IAAI6C,mBAAmB,GAAGjD,yBAAyB,CAAC0C,UAAU,CAAC;EAC/D,IAAIC,MAAM,GAAGO,YAAY,CAACH,WAAW,EAAEE,mBAAmB,CAAC;EAC3D,IAAIN,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAIjB,MAAM;EACV,IAAIyB,oBAAoB;EACxB,IAAIvD,MAAM,CAACwD,UAAU,CAACH,mBAAmB,CAAC,EAAE;IAC1CvB,MAAM,GAAG2B,sCAAsC,CAACjD,IAAI,EAAE6C,mBAAmB,CAAC;EAC5E,CAAC,MAAM;IACLE,oBAAoB,GAAGF,mBAAmB,KAAK,MAAM,GAAGK,wBAAwB,CAAClD,IAAI,CAAC,GAAG6C,mBAAmB;IAC5GvB,MAAM,GAAG6B,mCAAmC,CAACnD,IAAI,EAAE+C,oBAAoB,CAAC;EAC1E;EACA;EACA,OAAOK,YAAY,CAACT,WAAW,EAAEE,mBAAmB,EAAE;IACpDvB,MAAM,EAAEA,MAAM;IACdoB,qBAAqB,EAAEK;EACzB,CAAC,CAAC;AACJ;AACA,SAASX,iBAAiBA,CAACpC,IAAI,EAAEiC,SAAS,EAAE;EAC1C,IAAIoB,UAAU,GAAGT,YAAY,CAAC5C,IAAI,EAAE,OAAO,CAAC;EAC5C,IAAIsD,kBAAkB,GAAG1D,yBAAyB,CAACqC,SAAS,CAAC;EAC7D,IAAIM,MAAM,GAAGO,YAAY,CAACO,UAAU,EAAEC,kBAAkB,CAAC;EACzD,IAAIf,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAInB,KAAK;EACT,IAAImC,oBAAoB;EACxB;EACA;EACA,IAAI,CAACtB,SAAS,CAAClB,GAAG,CAAC,MAAM,CAAC,IAAIf,IAAI,CAACK,KAAK,CAACoC,OAAO,CAAC,CAAC,EAAE;IAClDrB,KAAK,GAAG,EAAE;EACZ;EACA,IAAI5B,MAAM,CAACwD,UAAU,CAACM,kBAAkB,CAAC,EAAE;IACzClC,KAAK,GAAG6B,sCAAsC,CAACjD,IAAI,EAAEsD,kBAAkB,EAAE,IAAI,CAAC;EAChF;EACA;EACA;EACA;EAAA,KACK,IAAIA,kBAAkB,KAAK,MAAM,EAAE;IACtC,IAAIE,YAAY,GAAGhB,0BAA0B,CAACxC,IAAI,EAAEA,IAAI,CAACc,aAAa,CAAC,CAAC,CAAC;IACzEyC,oBAAoB,GAAGC,YAAY,CAACd,qBAAqB;IACzDtB,KAAK,GAAG5B,MAAM,CAACW,GAAG,CAACqD,YAAY,CAAClC,MAAM,EAAE,UAAUmC,SAAS,EAAE;MAC3D,OAAOA,SAAS,CAAC5B,SAAS;IAC5B,CAAC,CAAC;EACJ,CAAC,MAAM;IACL0B,oBAAoB,GAAGD,kBAAkB;IACzClC,KAAK,GAAG+B,mCAAmC,CAACnD,IAAI,EAAEuD,oBAAoB,EAAE,IAAI,CAAC;EAC/E;EACA;EACA,OAAOH,YAAY,CAACC,UAAU,EAAEC,kBAAkB,EAAE;IAClDlC,KAAK,EAAEA,KAAK;IACZmC,oBAAoB,EAAEA;EACxB,CAAC,CAAC;AACJ;AACA,SAASxB,oBAAoBA,CAAC/B,IAAI,EAAE;EAClC,IAAIoB,KAAK,GAAGpB,IAAI,CAACK,KAAK,CAACgC,QAAQ,CAAC,CAAC;EACjC,IAAIqB,cAAc,GAAG/D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,OAAO;IACLsB,MAAM,EAAE9B,MAAM,CAACW,GAAG,CAACiB,KAAK,EAAE,UAAUI,IAAI,EAAEmC,GAAG,EAAE;MAC7C,OAAO;QACLC,KAAK,EAAEpC,IAAI,CAACoC,KAAK;QACjBlC,cAAc,EAAEgC,cAAc,CAAClC,IAAI,EAAEmC,GAAG,CAAC;QACzChC,QAAQ,EAAE3B,IAAI,CAACK,KAAK,CAACuB,QAAQ,CAACJ,IAAI,CAAC;QACnCK,SAAS,EAAEL,IAAI,CAACC;MAClB,CAAC;IACH,CAAC;EACH,CAAC;AACH;AACA,SAASmB,YAAYA,CAAC5C,IAAI,EAAE6D,IAAI,EAAE;EAChC;EACA,OAAO/D,KAAK,CAACE,IAAI,CAAC,CAAC6D,IAAI,CAAC,KAAK/D,KAAK,CAACE,IAAI,CAAC,CAAC6D,IAAI,CAAC,GAAG,EAAE,CAAC;AACtD;AACA,SAASf,YAAYA,CAACgB,KAAK,EAAEC,GAAG,EAAE;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACtD,MAAM,EAAEwD,CAAC,EAAE,EAAE;IACrC,IAAIF,KAAK,CAACE,CAAC,CAAC,CAACD,GAAG,KAAKA,GAAG,EAAE;MACxB,OAAOD,KAAK,CAACE,CAAC,CAAC,CAACvC,KAAK;IACvB;EACF;AACF;AACA,SAAS2B,YAAYA,CAACU,KAAK,EAAEC,GAAG,EAAEtC,KAAK,EAAE;EACvCqC,KAAK,CAACnD,IAAI,CAAC;IACToD,GAAG,EAAEA,GAAG;IACRtC,KAAK,EAAEA;EACT,CAAC,CAAC;EACF,OAAOA,KAAK;AACd;AACA,SAASyB,wBAAwBA,CAAClD,IAAI,EAAE;EACtC,IAAIuC,MAAM,GAAGzC,KAAK,CAACE,IAAI,CAAC,CAACiE,YAAY;EACrC,OAAO1B,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGzC,KAAK,CAACE,IAAI,CAAC,CAACiE,YAAY,GAAGjE,IAAI,CAACkE,yBAAyB,CAAC,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,yBAAyBA,CAAClE,IAAI,EAAE;EAC9C,IAAImE,MAAM,GAAGC,0CAA0C,CAACpE,IAAI,CAAC;EAC7D,IAAI0D,cAAc,GAAG/D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAIqE,QAAQ,GAAG,CAACF,MAAM,CAACG,UAAU,GAAGH,MAAM,CAACI,WAAW,IAAI,GAAG,GAAGC,IAAI,CAACC,EAAE;EACvE,IAAIC,YAAY,GAAG1E,IAAI,CAACK,KAAK;EAC7B,IAAIsE,aAAa,GAAGD,YAAY,CAACxD,SAAS,CAAC,CAAC;EAC5C;EACA;EACA;EACA,IAAI0D,SAAS,GAAGF,YAAY,CAACG,KAAK,CAAC,CAAC;EACpC,IAAIF,aAAa,CAAC,CAAC,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;IAC3C,OAAO,CAAC;EACV;EACA,IAAIG,IAAI,GAAG,CAAC;EACZ;EACA,IAAIF,SAAS,GAAG,EAAE,EAAE;IAClBE,IAAI,GAAGN,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEP,IAAI,CAACQ,KAAK,CAACJ,SAAS,GAAG,EAAE,CAAC,CAAC;EAChD;EACA,IAAI/C,SAAS,GAAG8C,aAAa,CAAC,CAAC,CAAC;EAChC,IAAIM,QAAQ,GAAGjF,IAAI,CAACkF,WAAW,CAACrD,SAAS,GAAG,CAAC,CAAC,GAAG7B,IAAI,CAACkF,WAAW,CAACrD,SAAS,CAAC;EAC5E,IAAIsD,KAAK,GAAGX,IAAI,CAACY,GAAG,CAACH,QAAQ,GAAGT,IAAI,CAACa,GAAG,CAAChB,QAAQ,CAAC,CAAC;EACnD,IAAIiB,KAAK,GAAGd,IAAI,CAACY,GAAG,CAACH,QAAQ,GAAGT,IAAI,CAACe,GAAG,CAAClB,QAAQ,CAAC,CAAC;EACnD,IAAImB,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA;EACA,OAAO5D,SAAS,IAAI8C,aAAa,CAAC,CAAC,CAAC,EAAE9C,SAAS,IAAIiD,IAAI,EAAE;IACvD,IAAIY,KAAK,GAAG,CAAC;IACb,IAAIC,MAAM,GAAG,CAAC;IACd;IACA;IACA,IAAIC,IAAI,GAAGnG,WAAW,CAACoG,eAAe,CAACnC,cAAc,CAAC;MACpDjC,KAAK,EAAEI;IACT,CAAC,CAAC,EAAEsC,MAAM,CAAC2B,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC;IACjC;IACAJ,KAAK,GAAGE,IAAI,CAACF,KAAK,GAAG,GAAG;IACxBC,MAAM,GAAGC,IAAI,CAACD,MAAM,GAAG,GAAG;IAC1B;IACAH,IAAI,GAAGhB,IAAI,CAACO,GAAG,CAACS,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC;IAC/BD,IAAI,GAAGjB,IAAI,CAACO,GAAG,CAACU,IAAI,EAAEE,MAAM,EAAE,CAAC,CAAC;EAClC;EACA,IAAII,EAAE,GAAGP,IAAI,GAAGL,KAAK;EACrB,IAAIa,EAAE,GAAGP,IAAI,GAAGH,KAAK;EACrB;EACAW,KAAK,CAACF,EAAE,CAAC,KAAKA,EAAE,GAAGG,QAAQ,CAAC;EAC5BD,KAAK,CAACD,EAAE,CAAC,KAAKA,EAAE,GAAGE,QAAQ,CAAC;EAC5B,IAAIC,QAAQ,GAAG3B,IAAI,CAACO,GAAG,CAAC,CAAC,EAAEP,IAAI,CAACQ,KAAK,CAACR,IAAI,CAAC4B,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;EACxD,IAAIlC,KAAK,GAAGhE,KAAK,CAACE,IAAI,CAACqG,KAAK,CAAC;EAC7B,IAAIC,UAAU,GAAGtG,IAAI,CAACkB,SAAS,CAAC,CAAC;EACjC,IAAIqF,gBAAgB,GAAGzC,KAAK,CAACyC,gBAAgB;EAC7C,IAAIC,aAAa,GAAG1C,KAAK,CAAC0C,aAAa;EACvC;EACA;EACA;EACA;EACA;EACA;EACA,IAAID,gBAAgB,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,IAAIhC,IAAI,CAACY,GAAG,CAACmB,gBAAgB,GAAGJ,QAAQ,CAAC,IAAI,CAAC,IAAI3B,IAAI,CAACY,GAAG,CAACoB,aAAa,GAAG5B,SAAS,CAAC,IAAI;EAC9I;EACA;EAAA,GACG2B,gBAAgB,GAAGJ;EACtB;EACA;EAAA,GACGrC,KAAK,CAAC2C,WAAW,KAAKH,UAAU,CAAC,CAAC,CAAC,IAAIxC,KAAK,CAAC4C,WAAW,KAAKJ,UAAU,CAAC,CAAC,CAAC,EAAE;IAC7EH,QAAQ,GAAGI,gBAAgB;EAC7B;EACA;EACA;EAAA,KACK;IACHzC,KAAK,CAAC0C,aAAa,GAAG5B,SAAS;IAC/Bd,KAAK,CAACyC,gBAAgB,GAAGJ,QAAQ;IACjCrC,KAAK,CAAC2C,WAAW,GAAGH,UAAU,CAAC,CAAC,CAAC;IACjCxC,KAAK,CAAC4C,WAAW,GAAGJ,UAAU,CAAC,CAAC,CAAC;EACnC;EACA,OAAOH,QAAQ;AACjB;AACA,SAAS/B,0CAA0CA,CAACpE,IAAI,EAAE;EACxD,IAAIsC,UAAU,GAAGtC,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,OAAO;IACLwD,UAAU,EAAEtE,IAAI,CAAC2G,SAAS,GAAG3G,IAAI,CAAC2G,SAAS,CAAC,CAAC,GAAG3G,IAAI,CAAC4G,YAAY,IAAI,CAAC5G,IAAI,CAAC4G,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IAClGrC,WAAW,EAAEjC,UAAU,CAACvB,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC1C+E,IAAI,EAAExD,UAAU,CAACuE,OAAO,CAAC;EAC3B,CAAC;AACH;AACA,SAAS1D,mCAAmCA,CAACnD,IAAI,EAAE8G,gBAAgB,EAAEC,QAAQ,EAAE;EAC7E,IAAIrD,cAAc,GAAG/D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAI0E,YAAY,GAAG1E,IAAI,CAACK,KAAK;EAC7B,IAAIsE,aAAa,GAAGD,YAAY,CAACxD,SAAS,CAAC,CAAC;EAC5C,IAAIoB,UAAU,GAAGtC,IAAI,CAACc,aAAa,CAAC,CAAC;EACrC,IAAIyB,MAAM,GAAG,EAAE;EACf;EACA,IAAIuC,IAAI,GAAGN,IAAI,CAACO,GAAG,CAAC,CAAC+B,gBAAgB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACnD,IAAIE,SAAS,GAAGrC,aAAa,CAAC,CAAC,CAAC;EAChC,IAAIC,SAAS,GAAGF,YAAY,CAACG,KAAK,CAAC,CAAC;EACpC;EACA;EACA;EACA;EACA,IAAImC,SAAS,KAAK,CAAC,IAAIlC,IAAI,GAAG,CAAC,IAAIF,SAAS,GAAGE,IAAI,GAAG,CAAC,EAAE;IACvDkC,SAAS,GAAGxC,IAAI,CAACyC,KAAK,CAACzC,IAAI,CAAC0C,IAAI,CAACF,SAAS,GAAGlC,IAAI,CAAC,GAAGA,IAAI,CAAC;EAC5D;EACA;EACA;EACA;EACA;EACA;EACA,IAAIqC,YAAY,GAAGtH,mBAAmB,CAACG,IAAI,CAAC;EAC5C,IAAIoH,eAAe,GAAG9E,UAAU,CAACvB,GAAG,CAAC,cAAc,CAAC,IAAIoG,YAAY;EACpE,IAAIE,eAAe,GAAG/E,UAAU,CAACvB,GAAG,CAAC,cAAc,CAAC,IAAIoG,YAAY;EACpE,IAAIC,eAAe,IAAIJ,SAAS,KAAKrC,aAAa,CAAC,CAAC,CAAC,EAAE;IACrD2C,OAAO,CAAC3C,aAAa,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA;EACA,IAAI9C,SAAS,GAAGmF,SAAS;EACzB,OAAOnF,SAAS,IAAI8C,aAAa,CAAC,CAAC,CAAC,EAAE9C,SAAS,IAAIiD,IAAI,EAAE;IACvDwC,OAAO,CAACzF,SAAS,CAAC;EACpB;EACA,IAAIwF,eAAe,IAAIxF,SAAS,GAAGiD,IAAI,KAAKH,aAAa,CAAC,CAAC,CAAC,EAAE;IAC5D2C,OAAO,CAAC3C,aAAa,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,SAAS2C,OAAOA,CAACzF,SAAS,EAAE;IAC1B,IAAI0F,OAAO,GAAG;MACZ9F,KAAK,EAAEI;IACT,CAAC;IACDU,MAAM,CAAC5B,IAAI,CAACoG,QAAQ,GAAGlF,SAAS,GAAG;MACjCH,cAAc,EAAEgC,cAAc,CAAC6D,OAAO,CAAC;MACvC5F,QAAQ,EAAE+C,YAAY,CAAC9C,QAAQ,CAAC2F,OAAO,CAAC;MACxC1F,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EACA,OAAOU,MAAM;AACf;AACA,SAASU,sCAAsCA,CAACjD,IAAI,EAAE8G,gBAAgB,EAAEC,QAAQ,EAAE;EAChF,IAAIrC,YAAY,GAAG1E,IAAI,CAACK,KAAK;EAC7B,IAAIqD,cAAc,GAAG/D,kBAAkB,CAACK,IAAI,CAAC;EAC7C,IAAIuC,MAAM,GAAG,EAAE;EACf/C,MAAM,CAACgI,IAAI,CAAC9C,YAAY,CAACrC,QAAQ,CAAC,CAAC,EAAE,UAAUb,IAAI,EAAE;IACnD,IAAIG,QAAQ,GAAG+C,YAAY,CAAC9C,QAAQ,CAACJ,IAAI,CAAC;IAC1C,IAAIK,SAAS,GAAGL,IAAI,CAACC,KAAK;IAC1B,IAAIqF,gBAAgB,CAACtF,IAAI,CAACC,KAAK,EAAEE,QAAQ,CAAC,EAAE;MAC1CY,MAAM,CAAC5B,IAAI,CAACoG,QAAQ,GAAGlF,SAAS,GAAG;QACjCH,cAAc,EAAEgC,cAAc,CAAClC,IAAI,CAAC;QACpCG,QAAQ,EAAEA,QAAQ;QAClBE,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOU,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}