{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\nfunction EffectCards(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    cardsEffect: {\n      slideShadows: true,\n      rotate: true,\n      perSlideRotate: 2,\n      perSlideOffset: 8\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides,\n      activeIndex,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.cardsEffect;\n    const {\n      startTranslate,\n      isTouched\n    } = swiper.touchEventsData;\n    const currentTranslate = rtl ? -swiper.translate : swiper.translate;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideProgress, -4), 4);\n      let offset = slideEl.swiperSlideOffset;\n      if (swiper.params.centeredSlides && !swiper.params.cssMode) {\n        swiper.wrapperEl.style.transform = `translateX(${swiper.minTranslate()}px)`;\n      }\n      if (swiper.params.centeredSlides && swiper.params.cssMode) {\n        offset -= slides[0].swiperSlideOffset;\n      }\n      let tX = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let tY = 0;\n      const tZ = -100 * Math.abs(progress);\n      let scale = 1;\n      let rotate = -params.perSlideRotate * progress;\n      let tXAdd = params.perSlideOffset - Math.abs(progress) * 0.75;\n      const slideIndex = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.from + i : i;\n      const isSwipeToNext = (slideIndex === activeIndex || slideIndex === activeIndex - 1) && progress > 0 && progress < 1 && (isTouched || swiper.params.cssMode) && currentTranslate < startTranslate;\n      const isSwipeToPrev = (slideIndex === activeIndex || slideIndex === activeIndex + 1) && progress < 0 && progress > -1 && (isTouched || swiper.params.cssMode) && currentTranslate > startTranslate;\n      if (isSwipeToNext || isSwipeToPrev) {\n        const subProgress = (1 - Math.abs((Math.abs(progress) - 0.5) / 0.5)) ** 0.5;\n        rotate += -28 * progress * subProgress;\n        scale += -0.5 * subProgress;\n        tXAdd += 96 * subProgress;\n        tY = `${-25 * subProgress * Math.abs(progress)}%`;\n      }\n      if (progress < 0) {\n        // next\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (${tXAdd * Math.abs(progress)}%))`;\n      } else if (progress > 0) {\n        // prev\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (-${tXAdd * Math.abs(progress)}%))`;\n      } else {\n        tX = `${tX}px`;\n      }\n      if (!swiper.isHorizontal()) {\n        const prevY = tY;\n        tY = tX;\n        tX = prevY;\n      }\n      const scaleString = progress < 0 ? `${1 + (1 - scale) * progress}` : `${1 - (1 - scale) * progress}`;\n\n      /* eslint-disable */\n      const transform = `\n        translate3d(${tX}, ${tY}, ${tZ}px)\n        rotateZ(${params.rotate ? rtl ? -rotate : rotate : 0}deg)\n        scale(${scaleString})\n      `;\n      /* eslint-enable */\n\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl) {\n          shadowEl = createShadow('cards', slideEl);\n        }\n        if (shadowEl) shadowEl.style.opacity = Math.min(Math.max((Math.abs(progress) - 0.5) / 0.5, 0), 1);\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'cards',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      _loopSwapReset: false,\n      watchSlidesProgress: true,\n      loopAdditionalSlides: swiper.params.cardsEffect.rotate ? 3 : 2,\n      centeredSlides: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\nexport { EffectCards as default };", "map": {"version": 3, "names": ["c", "createShadow", "e", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "g", "getSlideTransformEl", "EffectCards", "_ref", "swiper", "extendParams", "on", "cardsEffect", "slideShadows", "rotate", "perSlideRotate", "perSlideOffset", "setTranslate", "slides", "activeIndex", "rtlTranslate", "rtl", "params", "startTranslate", "isTouched", "touchEventsData", "currentTranslate", "translate", "i", "length", "slideEl", "slideProgress", "progress", "Math", "min", "max", "offset", "swiperSlideOffset", "centeredSlides", "cssMode", "wrapperEl", "style", "transform", "minTranslate", "tX", "tY", "tZ", "abs", "scale", "tXAdd", "slideIndex", "virtual", "enabled", "from", "isSwipeToNext", "isSwipeToPrev", "subProgress", "isHorizontal", "prevY", "scaleString", "shadowEl", "querySelector", "opacity", "zIndex", "round", "targetEl", "setTransition", "duration", "transformElements", "map", "for<PERSON>ach", "el", "transitionDuration", "querySelectorAll", "effect", "perspective", "overwriteParams", "_loopSwapReset", "watchSlidesProgress", "loopAdditionalSlides", "virtualTranslate", "default"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/swiper/modules/effect-cards.mjs"], "sourcesContent": ["import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\n\nfunction EffectCards(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    cardsEffect: {\n      slideShadows: true,\n      rotate: true,\n      perSlideRotate: 2,\n      perSlideOffset: 8\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides,\n      activeIndex,\n      rtlTranslate: rtl\n    } = swiper;\n    const params = swiper.params.cardsEffect;\n    const {\n      startTranslate,\n      isTouched\n    } = swiper.touchEventsData;\n    const currentTranslate = rtl ? -swiper.translate : swiper.translate;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = slides[i];\n      const slideProgress = slideEl.progress;\n      const progress = Math.min(Math.max(slideProgress, -4), 4);\n      let offset = slideEl.swiperSlideOffset;\n      if (swiper.params.centeredSlides && !swiper.params.cssMode) {\n        swiper.wrapperEl.style.transform = `translateX(${swiper.minTranslate()}px)`;\n      }\n      if (swiper.params.centeredSlides && swiper.params.cssMode) {\n        offset -= slides[0].swiperSlideOffset;\n      }\n      let tX = swiper.params.cssMode ? -offset - swiper.translate : -offset;\n      let tY = 0;\n      const tZ = -100 * Math.abs(progress);\n      let scale = 1;\n      let rotate = -params.perSlideRotate * progress;\n      let tXAdd = params.perSlideOffset - Math.abs(progress) * 0.75;\n      const slideIndex = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.from + i : i;\n      const isSwipeToNext = (slideIndex === activeIndex || slideIndex === activeIndex - 1) && progress > 0 && progress < 1 && (isTouched || swiper.params.cssMode) && currentTranslate < startTranslate;\n      const isSwipeToPrev = (slideIndex === activeIndex || slideIndex === activeIndex + 1) && progress < 0 && progress > -1 && (isTouched || swiper.params.cssMode) && currentTranslate > startTranslate;\n      if (isSwipeToNext || isSwipeToPrev) {\n        const subProgress = (1 - Math.abs((Math.abs(progress) - 0.5) / 0.5)) ** 0.5;\n        rotate += -28 * progress * subProgress;\n        scale += -0.5 * subProgress;\n        tXAdd += 96 * subProgress;\n        tY = `${-25 * subProgress * Math.abs(progress)}%`;\n      }\n      if (progress < 0) {\n        // next\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (${tXAdd * Math.abs(progress)}%))`;\n      } else if (progress > 0) {\n        // prev\n        tX = `calc(${tX}px ${rtl ? '-' : '+'} (-${tXAdd * Math.abs(progress)}%))`;\n      } else {\n        tX = `${tX}px`;\n      }\n      if (!swiper.isHorizontal()) {\n        const prevY = tY;\n        tY = tX;\n        tX = prevY;\n      }\n      const scaleString = progress < 0 ? `${1 + (1 - scale) * progress}` : `${1 - (1 - scale) * progress}`;\n\n      /* eslint-disable */\n      const transform = `\n        translate3d(${tX}, ${tY}, ${tZ}px)\n        rotateZ(${params.rotate ? rtl ? -rotate : rotate : 0}deg)\n        scale(${scaleString})\n      `;\n      /* eslint-enable */\n\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowEl = slideEl.querySelector('.swiper-slide-shadow');\n        if (!shadowEl) {\n          shadowEl = createShadow('cards', slideEl);\n        }\n        if (shadowEl) shadowEl.style.opacity = Math.min(Math.max((Math.abs(progress) - 0.5) / 0.5, 0), 1);\n      }\n      slideEl.style.zIndex = -Math.abs(Math.round(slideProgress)) + slides.length;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = transform;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements\n    });\n  };\n  effectInit({\n    effect: 'cards',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      _loopSwapReset: false,\n      watchSlidesProgress: true,\n      loopAdditionalSlides: swiper.params.cardsEffect.rotate ? 3 : 2,\n      centeredSlides: true,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectCards as default };\n"], "mappings": ";;;AAAA,SAASA,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,UAAU,QAAQ,2BAA2B;AAC3D,SAASD,CAAC,IAAIE,YAAY,QAAQ,6BAA6B;AAC/D,SAASF,CAAC,IAAIG,0BAA0B,QAAQ,6CAA6C;AAC7F,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,qBAAqB;AAE9D,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,WAAW,EAAE;MACXC,YAAY,EAAE,IAAI;MAClBC,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE,CAAC;MACjBC,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJC,MAAM;MACNC,WAAW;MACXC,YAAY,EAAEC;IAChB,CAAC,GAAGZ,MAAM;IACV,MAAMa,MAAM,GAAGb,MAAM,CAACa,MAAM,CAACV,WAAW;IACxC,MAAM;MACJW,cAAc;MACdC;IACF,CAAC,GAAGf,MAAM,CAACgB,eAAe;IAC1B,MAAMC,gBAAgB,GAAGL,GAAG,GAAG,CAACZ,MAAM,CAACkB,SAAS,GAAGlB,MAAM,CAACkB,SAAS;IACnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,CAACW,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAME,OAAO,GAAGZ,MAAM,CAACU,CAAC,CAAC;MACzB,MAAMG,aAAa,GAAGD,OAAO,CAACE,QAAQ;MACtC,MAAMA,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACJ,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACzD,IAAIK,MAAM,GAAGN,OAAO,CAACO,iBAAiB;MACtC,IAAI5B,MAAM,CAACa,MAAM,CAACgB,cAAc,IAAI,CAAC7B,MAAM,CAACa,MAAM,CAACiB,OAAO,EAAE;QAC1D9B,MAAM,CAAC+B,SAAS,CAACC,KAAK,CAACC,SAAS,GAAG,cAAcjC,MAAM,CAACkC,YAAY,CAAC,CAAC,KAAK;MAC7E;MACA,IAAIlC,MAAM,CAACa,MAAM,CAACgB,cAAc,IAAI7B,MAAM,CAACa,MAAM,CAACiB,OAAO,EAAE;QACzDH,MAAM,IAAIlB,MAAM,CAAC,CAAC,CAAC,CAACmB,iBAAiB;MACvC;MACA,IAAIO,EAAE,GAAGnC,MAAM,CAACa,MAAM,CAACiB,OAAO,GAAG,CAACH,MAAM,GAAG3B,MAAM,CAACkB,SAAS,GAAG,CAACS,MAAM;MACrE,IAAIS,EAAE,GAAG,CAAC;MACV,MAAMC,EAAE,GAAG,CAAC,GAAG,GAAGb,IAAI,CAACc,GAAG,CAACf,QAAQ,CAAC;MACpC,IAAIgB,KAAK,GAAG,CAAC;MACb,IAAIlC,MAAM,GAAG,CAACQ,MAAM,CAACP,cAAc,GAAGiB,QAAQ;MAC9C,IAAIiB,KAAK,GAAG3B,MAAM,CAACN,cAAc,GAAGiB,IAAI,CAACc,GAAG,CAACf,QAAQ,CAAC,GAAG,IAAI;MAC7D,MAAMkB,UAAU,GAAGzC,MAAM,CAAC0C,OAAO,IAAI1C,MAAM,CAACa,MAAM,CAAC6B,OAAO,CAACC,OAAO,GAAG3C,MAAM,CAAC0C,OAAO,CAACE,IAAI,GAAGzB,CAAC,GAAGA,CAAC;MAChG,MAAM0B,aAAa,GAAG,CAACJ,UAAU,KAAK/B,WAAW,IAAI+B,UAAU,KAAK/B,WAAW,GAAG,CAAC,KAAKa,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,KAAKR,SAAS,IAAIf,MAAM,CAACa,MAAM,CAACiB,OAAO,CAAC,IAAIb,gBAAgB,GAAGH,cAAc;MACjM,MAAMgC,aAAa,GAAG,CAACL,UAAU,KAAK/B,WAAW,IAAI+B,UAAU,KAAK/B,WAAW,GAAG,CAAC,KAAKa,QAAQ,GAAG,CAAC,IAAIA,QAAQ,GAAG,CAAC,CAAC,KAAKR,SAAS,IAAIf,MAAM,CAACa,MAAM,CAACiB,OAAO,CAAC,IAAIb,gBAAgB,GAAGH,cAAc;MAClM,IAAI+B,aAAa,IAAIC,aAAa,EAAE;QAClC,MAAMC,WAAW,GAAG,CAAC,CAAC,GAAGvB,IAAI,CAACc,GAAG,CAAC,CAACd,IAAI,CAACc,GAAG,CAACf,QAAQ,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,GAAG;QAC3ElB,MAAM,IAAI,CAAC,EAAE,GAAGkB,QAAQ,GAAGwB,WAAW;QACtCR,KAAK,IAAI,CAAC,GAAG,GAAGQ,WAAW;QAC3BP,KAAK,IAAI,EAAE,GAAGO,WAAW;QACzBX,EAAE,GAAG,GAAG,CAAC,EAAE,GAAGW,WAAW,GAAGvB,IAAI,CAACc,GAAG,CAACf,QAAQ,CAAC,GAAG;MACnD;MACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;QAChB;QACAY,EAAE,GAAG,QAAQA,EAAE,MAAMvB,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK4B,KAAK,GAAGhB,IAAI,CAACc,GAAG,CAACf,QAAQ,CAAC,KAAK;MAC1E,CAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;QACvB;QACAY,EAAE,GAAG,QAAQA,EAAE,MAAMvB,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM4B,KAAK,GAAGhB,IAAI,CAACc,GAAG,CAACf,QAAQ,CAAC,KAAK;MAC3E,CAAC,MAAM;QACLY,EAAE,GAAG,GAAGA,EAAE,IAAI;MAChB;MACA,IAAI,CAACnC,MAAM,CAACgD,YAAY,CAAC,CAAC,EAAE;QAC1B,MAAMC,KAAK,GAAGb,EAAE;QAChBA,EAAE,GAAGD,EAAE;QACPA,EAAE,GAAGc,KAAK;MACZ;MACA,MAAMC,WAAW,GAAG3B,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGgB,KAAK,IAAIhB,QAAQ,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGgB,KAAK,IAAIhB,QAAQ,EAAE;;MAEpG;MACA,MAAMU,SAAS,GAAG;AACxB,sBAAsBE,EAAE,KAAKC,EAAE,KAAKC,EAAE;AACtC,kBAAkBxB,MAAM,CAACR,MAAM,GAAGO,GAAG,GAAG,CAACP,MAAM,GAAGA,MAAM,GAAG,CAAC;AAC5D,gBAAgB6C,WAAW;AAC3B,OAAO;MACD;;MAEA,IAAIrC,MAAM,CAACT,YAAY,EAAE;QACvB;QACA,IAAI+C,QAAQ,GAAG9B,OAAO,CAAC+B,aAAa,CAAC,sBAAsB,CAAC;QAC5D,IAAI,CAACD,QAAQ,EAAE;UACbA,QAAQ,GAAG5D,YAAY,CAAC,OAAO,EAAE8B,OAAO,CAAC;QAC3C;QACA,IAAI8B,QAAQ,EAAEA,QAAQ,CAACnB,KAAK,CAACqB,OAAO,GAAG7B,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,CAACF,IAAI,CAACc,GAAG,CAACf,QAAQ,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACnG;MACAF,OAAO,CAACW,KAAK,CAACsB,MAAM,GAAG,CAAC9B,IAAI,CAACc,GAAG,CAACd,IAAI,CAAC+B,KAAK,CAACjC,aAAa,CAAC,CAAC,GAAGb,MAAM,CAACW,MAAM;MAC3E,MAAMoC,QAAQ,GAAG9D,YAAY,CAACmB,MAAM,EAAEQ,OAAO,CAAC;MAC9CmC,QAAQ,CAACxB,KAAK,CAACC,SAAS,GAAGA,SAAS;IACtC;EACF,CAAC;EACD,MAAMwB,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAG3D,MAAM,CAACS,MAAM,CAACmD,GAAG,CAACvC,OAAO,IAAIxB,mBAAmB,CAACwB,OAAO,CAAC,CAAC;IACpFsC,iBAAiB,CAACE,OAAO,CAACC,EAAE,IAAI;MAC9BA,EAAE,CAAC9B,KAAK,CAAC+B,kBAAkB,GAAG,GAAGL,QAAQ,IAAI;MAC7CI,EAAE,CAACE,gBAAgB,CAAC,sBAAsB,CAAC,CAACH,OAAO,CAACV,QAAQ,IAAI;QAC9DA,QAAQ,CAACnB,KAAK,CAAC+B,kBAAkB,GAAG,GAAGL,QAAQ,IAAI;MACrD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF/D,0BAA0B,CAAC;MACzBK,MAAM;MACN0D,QAAQ;MACRC;IACF,CAAC,CAAC;EACJ,CAAC;EACDlE,UAAU,CAAC;IACTwE,MAAM,EAAE,OAAO;IACfjE,MAAM;IACNE,EAAE;IACFM,YAAY;IACZiD,aAAa;IACbS,WAAW,EAAEA,CAAA,KAAM,IAAI;IACvBC,eAAe,EAAEA,CAAA,MAAO;MACtBC,cAAc,EAAE,KAAK;MACrBC,mBAAmB,EAAE,IAAI;MACzBC,oBAAoB,EAAEtE,MAAM,CAACa,MAAM,CAACV,WAAW,CAACE,MAAM,GAAG,CAAC,GAAG,CAAC;MAC9DwB,cAAc,EAAE,IAAI;MACpB0C,gBAAgB,EAAE,CAACvE,MAAM,CAACa,MAAM,CAACiB;IACnC,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAAShC,WAAW,IAAI0E,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}