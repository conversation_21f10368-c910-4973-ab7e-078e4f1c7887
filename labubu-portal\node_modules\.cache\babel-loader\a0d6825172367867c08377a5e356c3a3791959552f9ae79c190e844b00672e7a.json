{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Geo, { geo2DDimensions } from './Geo.js';\nimport * as layout from '../../util/layout.js';\nimport * as numberUtil from '../../util/number.js';\nimport geoSourceManager from './geoSourceManager.js';\nimport * as vector from 'zrender/lib/core/vector.js';\n/**\r\n * Resize method bound to the geo\r\n */\nfunction resizeGeo(geoModel, api) {\n  var boundingCoords = geoModel.get('boundingCoords');\n  if (boundingCoords != null) {\n    var leftTop_1 = boundingCoords[0];\n    var rightBottom_1 = boundingCoords[1];\n    if (!(isFinite(leftTop_1[0]) && isFinite(leftTop_1[1]) && isFinite(rightBottom_1[0]) && isFinite(rightBottom_1[1]))) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Invalid boundingCoords');\n      }\n    } else {\n      // Sample around the lng/lat rect and use projection to calculate actual bounding rect.\n      var projection_1 = this.projection;\n      if (projection_1) {\n        var xMin = leftTop_1[0];\n        var yMin = leftTop_1[1];\n        var xMax = rightBottom_1[0];\n        var yMax = rightBottom_1[1];\n        leftTop_1 = [Infinity, Infinity];\n        rightBottom_1 = [-Infinity, -Infinity];\n        // TODO better way?\n        var sampleLine = function (x0, y0, x1, y1) {\n          var dx = x1 - x0;\n          var dy = y1 - y0;\n          for (var i = 0; i <= 100; i++) {\n            var p = i / 100;\n            var pt = projection_1.project([x0 + dx * p, y0 + dy * p]);\n            vector.min(leftTop_1, leftTop_1, pt);\n            vector.max(rightBottom_1, rightBottom_1, pt);\n          }\n        };\n        // Top\n        sampleLine(xMin, yMin, xMax, yMin);\n        // Right\n        sampleLine(xMax, yMin, xMax, yMax);\n        // Bottom\n        sampleLine(xMax, yMax, xMin, yMax);\n        // Left\n        sampleLine(xMin, yMax, xMax, yMin);\n      }\n      this.setBoundingRect(leftTop_1[0], leftTop_1[1], rightBottom_1[0] - leftTop_1[0], rightBottom_1[1] - leftTop_1[1]);\n    }\n  }\n  var rect = this.getBoundingRect();\n  var centerOption = geoModel.get('layoutCenter');\n  var sizeOption = geoModel.get('layoutSize');\n  var viewWidth = api.getWidth();\n  var viewHeight = api.getHeight();\n  var aspect = rect.width / rect.height * this.aspectScale;\n  var useCenterAndSize = false;\n  var center;\n  var size;\n  if (centerOption && sizeOption) {\n    center = [numberUtil.parsePercent(centerOption[0], viewWidth), numberUtil.parsePercent(centerOption[1], viewHeight)];\n    size = numberUtil.parsePercent(sizeOption, Math.min(viewWidth, viewHeight));\n    if (!isNaN(center[0]) && !isNaN(center[1]) && !isNaN(size)) {\n      useCenterAndSize = true;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('Given layoutCenter or layoutSize data are invalid. Use left/top/width/height instead.');\n      }\n    }\n  }\n  var viewRect;\n  if (useCenterAndSize) {\n    viewRect = {};\n    if (aspect > 1) {\n      // Width is same with size\n      viewRect.width = size;\n      viewRect.height = size / aspect;\n    } else {\n      viewRect.height = size;\n      viewRect.width = size * aspect;\n    }\n    viewRect.y = center[1] - viewRect.height / 2;\n    viewRect.x = center[0] - viewRect.width / 2;\n  } else {\n    // Use left/top/width/height\n    var boxLayoutOption = geoModel.getBoxLayoutParams();\n    boxLayoutOption.aspect = aspect;\n    viewRect = layout.getLayoutRect(boxLayoutOption, {\n      width: viewWidth,\n      height: viewHeight\n    });\n  }\n  this.setViewRect(viewRect.x, viewRect.y, viewRect.width, viewRect.height);\n  this.setCenter(geoModel.get('center'), api);\n  this.setZoom(geoModel.get('zoom'));\n}\n// Back compat for ECharts2, where the coord map is set on map series:\n// {type: 'map', geoCoord: {'cityA': [116.46,39.92], 'cityA': [119.12,24.61]}},\nfunction setGeoCoords(geo, model) {\n  zrUtil.each(model.get('geoCoord'), function (geoCoord, name) {\n    geo.addGeoCoord(name, geoCoord);\n  });\n}\nvar GeoCreator = /** @class */function () {\n  function GeoCreator() {\n    // For deciding which dimensions to use when creating list data\n    this.dimensions = geo2DDimensions;\n  }\n  GeoCreator.prototype.create = function (ecModel, api) {\n    var geoList = [];\n    function getCommonGeoProperties(model) {\n      return {\n        nameProperty: model.get('nameProperty'),\n        aspectScale: model.get('aspectScale'),\n        projection: model.get('projection')\n      };\n    }\n    // FIXME Create each time may be slow\n    ecModel.eachComponent('geo', function (geoModel, idx) {\n      var mapName = geoModel.get('map');\n      var geo = new Geo(mapName + idx, mapName, zrUtil.extend({\n        nameMap: geoModel.get('nameMap')\n      }, getCommonGeoProperties(geoModel)));\n      geo.zoomLimit = geoModel.get('scaleLimit');\n      geoList.push(geo);\n      // setGeoCoords(geo, geoModel);\n      geoModel.coordinateSystem = geo;\n      geo.model = geoModel;\n      // Inject resize method\n      geo.resize = resizeGeo;\n      geo.resize(geoModel, api);\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      var coordSys = seriesModel.get('coordinateSystem');\n      if (coordSys === 'geo') {\n        var geoIndex = seriesModel.get('geoIndex') || 0;\n        seriesModel.coordinateSystem = geoList[geoIndex];\n      }\n    });\n    // If has map series\n    var mapModelGroupBySeries = {};\n    ecModel.eachSeriesByType('map', function (seriesModel) {\n      if (!seriesModel.getHostGeoModel()) {\n        var mapType = seriesModel.getMapType();\n        mapModelGroupBySeries[mapType] = mapModelGroupBySeries[mapType] || [];\n        mapModelGroupBySeries[mapType].push(seriesModel);\n      }\n    });\n    zrUtil.each(mapModelGroupBySeries, function (mapSeries, mapType) {\n      var nameMapList = zrUtil.map(mapSeries, function (singleMapSeries) {\n        return singleMapSeries.get('nameMap');\n      });\n      var geo = new Geo(mapType, mapType, zrUtil.extend({\n        nameMap: zrUtil.mergeAll(nameMapList)\n      }, getCommonGeoProperties(mapSeries[0])));\n      geo.zoomLimit = zrUtil.retrieve.apply(null, zrUtil.map(mapSeries, function (singleMapSeries) {\n        return singleMapSeries.get('scaleLimit');\n      }));\n      geoList.push(geo);\n      // Inject resize method\n      geo.resize = resizeGeo;\n      geo.resize(mapSeries[0], api);\n      zrUtil.each(mapSeries, function (singleMapSeries) {\n        singleMapSeries.coordinateSystem = geo;\n        setGeoCoords(geo, singleMapSeries);\n      });\n    });\n    return geoList;\n  };\n  /**\r\n   * Fill given regions array\r\n   */\n  GeoCreator.prototype.getFilledRegions = function (originRegionArr, mapName, nameMap, nameProperty) {\n    // Not use the original\n    var regionsArr = (originRegionArr || []).slice();\n    var dataNameMap = zrUtil.createHashMap();\n    for (var i = 0; i < regionsArr.length; i++) {\n      dataNameMap.set(regionsArr[i].name, regionsArr[i]);\n    }\n    var source = geoSourceManager.load(mapName, nameMap, nameProperty);\n    zrUtil.each(source.regions, function (region) {\n      var name = region.name;\n      var regionOption = dataNameMap.get(name);\n      // apply specified echarts style in GeoJSON data\n      var specifiedGeoJSONRegionStyle = region.properties && region.properties.echartsStyle;\n      if (!regionOption) {\n        regionOption = {\n          name: name\n        };\n        regionsArr.push(regionOption);\n      }\n      specifiedGeoJSONRegionStyle && zrUtil.merge(regionOption, specifiedGeoJSONRegionStyle);\n    });\n    return regionsArr;\n  };\n  return GeoCreator;\n}();\nvar geoCreator = new GeoCreator();\nexport default geoCreator;", "map": {"version": 3, "names": ["zrUtil", "Geo", "geo2DDimensions", "layout", "numberUtil", "geoSourceManager", "vector", "resizeGeo", "geoModel", "api", "boundingCoords", "get", "leftTop_1", "rightBottom_1", "isFinite", "process", "env", "NODE_ENV", "console", "error", "projection_1", "projection", "xMin", "yMin", "xMax", "yMax", "Infinity", "sampleLine", "x0", "y0", "x1", "y1", "dx", "dy", "i", "p", "pt", "project", "min", "max", "setBoundingRect", "rect", "getBoundingRect", "centerOption", "sizeOption", "viewWidth", "getWidth", "viewHeight", "getHeight", "aspect", "width", "height", "aspectScale", "useCenterAndSize", "center", "size", "parsePercent", "Math", "isNaN", "warn", "viewRect", "y", "x", "boxLayoutOption", "getBoxLayoutParams", "getLayoutRect", "setViewRect", "setCenter", "setZoom", "setGeoCoords", "geo", "model", "each", "geoCoord", "name", "addGeoCoord", "GeoCreator", "dimensions", "prototype", "create", "ecModel", "geoList", "getCommonGeoProperties", "nameProperty", "eachComponent", "idx", "mapName", "extend", "nameMap", "zoomLimit", "push", "coordinateSystem", "resize", "eachSeries", "seriesModel", "coordSys", "geoIndex", "mapModelGroupBySeries", "eachSeriesByType", "getHostGeoModel", "mapType", "getMapType", "mapSeries", "nameMapList", "map", "singleMapSeries", "mergeAll", "retrieve", "apply", "getFilledRegions", "originRegionArr", "regionsArr", "slice", "dataNameMap", "createHashMap", "length", "set", "source", "load", "regions", "region", "regionOption", "specifiedGeoJSONRegionStyle", "properties", "echartsStyle", "merge", "geoCreator"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/coord/geo/geoCreator.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Geo, { geo2DDimensions } from './Geo.js';\nimport * as layout from '../../util/layout.js';\nimport * as numberUtil from '../../util/number.js';\nimport geoSourceManager from './geoSourceManager.js';\nimport * as vector from 'zrender/lib/core/vector.js';\n/**\r\n * Resize method bound to the geo\r\n */\nfunction resizeGeo(geoModel, api) {\n  var boundingCoords = geoModel.get('boundingCoords');\n  if (boundingCoords != null) {\n    var leftTop_1 = boundingCoords[0];\n    var rightBottom_1 = boundingCoords[1];\n    if (!(isFinite(leftTop_1[0]) && isFinite(leftTop_1[1]) && isFinite(rightBottom_1[0]) && isFinite(rightBottom_1[1]))) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Invalid boundingCoords');\n      }\n    } else {\n      // Sample around the lng/lat rect and use projection to calculate actual bounding rect.\n      var projection_1 = this.projection;\n      if (projection_1) {\n        var xMin = leftTop_1[0];\n        var yMin = leftTop_1[1];\n        var xMax = rightBottom_1[0];\n        var yMax = rightBottom_1[1];\n        leftTop_1 = [Infinity, Infinity];\n        rightBottom_1 = [-Infinity, -Infinity];\n        // TODO better way?\n        var sampleLine = function (x0, y0, x1, y1) {\n          var dx = x1 - x0;\n          var dy = y1 - y0;\n          for (var i = 0; i <= 100; i++) {\n            var p = i / 100;\n            var pt = projection_1.project([x0 + dx * p, y0 + dy * p]);\n            vector.min(leftTop_1, leftTop_1, pt);\n            vector.max(rightBottom_1, rightBottom_1, pt);\n          }\n        };\n        // Top\n        sampleLine(xMin, yMin, xMax, yMin);\n        // Right\n        sampleLine(xMax, yMin, xMax, yMax);\n        // Bottom\n        sampleLine(xMax, yMax, xMin, yMax);\n        // Left\n        sampleLine(xMin, yMax, xMax, yMin);\n      }\n      this.setBoundingRect(leftTop_1[0], leftTop_1[1], rightBottom_1[0] - leftTop_1[0], rightBottom_1[1] - leftTop_1[1]);\n    }\n  }\n  var rect = this.getBoundingRect();\n  var centerOption = geoModel.get('layoutCenter');\n  var sizeOption = geoModel.get('layoutSize');\n  var viewWidth = api.getWidth();\n  var viewHeight = api.getHeight();\n  var aspect = rect.width / rect.height * this.aspectScale;\n  var useCenterAndSize = false;\n  var center;\n  var size;\n  if (centerOption && sizeOption) {\n    center = [numberUtil.parsePercent(centerOption[0], viewWidth), numberUtil.parsePercent(centerOption[1], viewHeight)];\n    size = numberUtil.parsePercent(sizeOption, Math.min(viewWidth, viewHeight));\n    if (!isNaN(center[0]) && !isNaN(center[1]) && !isNaN(size)) {\n      useCenterAndSize = true;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('Given layoutCenter or layoutSize data are invalid. Use left/top/width/height instead.');\n      }\n    }\n  }\n  var viewRect;\n  if (useCenterAndSize) {\n    viewRect = {};\n    if (aspect > 1) {\n      // Width is same with size\n      viewRect.width = size;\n      viewRect.height = size / aspect;\n    } else {\n      viewRect.height = size;\n      viewRect.width = size * aspect;\n    }\n    viewRect.y = center[1] - viewRect.height / 2;\n    viewRect.x = center[0] - viewRect.width / 2;\n  } else {\n    // Use left/top/width/height\n    var boxLayoutOption = geoModel.getBoxLayoutParams();\n    boxLayoutOption.aspect = aspect;\n    viewRect = layout.getLayoutRect(boxLayoutOption, {\n      width: viewWidth,\n      height: viewHeight\n    });\n  }\n  this.setViewRect(viewRect.x, viewRect.y, viewRect.width, viewRect.height);\n  this.setCenter(geoModel.get('center'), api);\n  this.setZoom(geoModel.get('zoom'));\n}\n// Back compat for ECharts2, where the coord map is set on map series:\n// {type: 'map', geoCoord: {'cityA': [116.46,39.92], 'cityA': [119.12,24.61]}},\nfunction setGeoCoords(geo, model) {\n  zrUtil.each(model.get('geoCoord'), function (geoCoord, name) {\n    geo.addGeoCoord(name, geoCoord);\n  });\n}\nvar GeoCreator = /** @class */function () {\n  function GeoCreator() {\n    // For deciding which dimensions to use when creating list data\n    this.dimensions = geo2DDimensions;\n  }\n  GeoCreator.prototype.create = function (ecModel, api) {\n    var geoList = [];\n    function getCommonGeoProperties(model) {\n      return {\n        nameProperty: model.get('nameProperty'),\n        aspectScale: model.get('aspectScale'),\n        projection: model.get('projection')\n      };\n    }\n    // FIXME Create each time may be slow\n    ecModel.eachComponent('geo', function (geoModel, idx) {\n      var mapName = geoModel.get('map');\n      var geo = new Geo(mapName + idx, mapName, zrUtil.extend({\n        nameMap: geoModel.get('nameMap')\n      }, getCommonGeoProperties(geoModel)));\n      geo.zoomLimit = geoModel.get('scaleLimit');\n      geoList.push(geo);\n      // setGeoCoords(geo, geoModel);\n      geoModel.coordinateSystem = geo;\n      geo.model = geoModel;\n      // Inject resize method\n      geo.resize = resizeGeo;\n      geo.resize(geoModel, api);\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      var coordSys = seriesModel.get('coordinateSystem');\n      if (coordSys === 'geo') {\n        var geoIndex = seriesModel.get('geoIndex') || 0;\n        seriesModel.coordinateSystem = geoList[geoIndex];\n      }\n    });\n    // If has map series\n    var mapModelGroupBySeries = {};\n    ecModel.eachSeriesByType('map', function (seriesModel) {\n      if (!seriesModel.getHostGeoModel()) {\n        var mapType = seriesModel.getMapType();\n        mapModelGroupBySeries[mapType] = mapModelGroupBySeries[mapType] || [];\n        mapModelGroupBySeries[mapType].push(seriesModel);\n      }\n    });\n    zrUtil.each(mapModelGroupBySeries, function (mapSeries, mapType) {\n      var nameMapList = zrUtil.map(mapSeries, function (singleMapSeries) {\n        return singleMapSeries.get('nameMap');\n      });\n      var geo = new Geo(mapType, mapType, zrUtil.extend({\n        nameMap: zrUtil.mergeAll(nameMapList)\n      }, getCommonGeoProperties(mapSeries[0])));\n      geo.zoomLimit = zrUtil.retrieve.apply(null, zrUtil.map(mapSeries, function (singleMapSeries) {\n        return singleMapSeries.get('scaleLimit');\n      }));\n      geoList.push(geo);\n      // Inject resize method\n      geo.resize = resizeGeo;\n      geo.resize(mapSeries[0], api);\n      zrUtil.each(mapSeries, function (singleMapSeries) {\n        singleMapSeries.coordinateSystem = geo;\n        setGeoCoords(geo, singleMapSeries);\n      });\n    });\n    return geoList;\n  };\n  /**\r\n   * Fill given regions array\r\n   */\n  GeoCreator.prototype.getFilledRegions = function (originRegionArr, mapName, nameMap, nameProperty) {\n    // Not use the original\n    var regionsArr = (originRegionArr || []).slice();\n    var dataNameMap = zrUtil.createHashMap();\n    for (var i = 0; i < regionsArr.length; i++) {\n      dataNameMap.set(regionsArr[i].name, regionsArr[i]);\n    }\n    var source = geoSourceManager.load(mapName, nameMap, nameProperty);\n    zrUtil.each(source.regions, function (region) {\n      var name = region.name;\n      var regionOption = dataNameMap.get(name);\n      // apply specified echarts style in GeoJSON data\n      var specifiedGeoJSONRegionStyle = region.properties && region.properties.echartsStyle;\n      if (!regionOption) {\n        regionOption = {\n          name: name\n        };\n        regionsArr.push(regionOption);\n      }\n      specifiedGeoJSONRegionStyle && zrUtil.merge(regionOption, specifiedGeoJSONRegionStyle);\n    });\n    return regionsArr;\n  };\n  return GeoCreator;\n}();\nvar geoCreator = new GeoCreator();\nexport default geoCreator;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,GAAG,IAAIC,eAAe,QAAQ,UAAU;AAC/C,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD;AACA;AACA;AACA,SAASC,SAASA,CAACC,QAAQ,EAAEC,GAAG,EAAE;EAChC,IAAIC,cAAc,GAAGF,QAAQ,CAACG,GAAG,CAAC,gBAAgB,CAAC;EACnD,IAAID,cAAc,IAAI,IAAI,EAAE;IAC1B,IAAIE,SAAS,GAAGF,cAAc,CAAC,CAAC,CAAC;IACjC,IAAIG,aAAa,GAAGH,cAAc,CAAC,CAAC,CAAC;IACrC,IAAI,EAAEI,QAAQ,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC,IAAIE,QAAQ,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC,IAAIE,QAAQ,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC,IAAIC,QAAQ,CAACD,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnH,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,wBAAwB,CAAC;MACzC;IACF,CAAC,MAAM;MACL;MACA,IAAIC,YAAY,GAAG,IAAI,CAACC,UAAU;MAClC,IAAID,YAAY,EAAE;QAChB,IAAIE,IAAI,GAAGV,SAAS,CAAC,CAAC,CAAC;QACvB,IAAIW,IAAI,GAAGX,SAAS,CAAC,CAAC,CAAC;QACvB,IAAIY,IAAI,GAAGX,aAAa,CAAC,CAAC,CAAC;QAC3B,IAAIY,IAAI,GAAGZ,aAAa,CAAC,CAAC,CAAC;QAC3BD,SAAS,GAAG,CAACc,QAAQ,EAAEA,QAAQ,CAAC;QAChCb,aAAa,GAAG,CAAC,CAACa,QAAQ,EAAE,CAACA,QAAQ,CAAC;QACtC;QACA,IAAIC,UAAU,GAAG,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;UACzC,IAAIC,EAAE,GAAGF,EAAE,GAAGF,EAAE;UAChB,IAAIK,EAAE,GAAGF,EAAE,GAAGF,EAAE;UAChB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,GAAG,EAAEA,CAAC,EAAE,EAAE;YAC7B,IAAIC,CAAC,GAAGD,CAAC,GAAG,GAAG;YACf,IAAIE,EAAE,GAAGhB,YAAY,CAACiB,OAAO,CAAC,CAACT,EAAE,GAAGI,EAAE,GAAGG,CAAC,EAAEN,EAAE,GAAGI,EAAE,GAAGE,CAAC,CAAC,CAAC;YACzD7B,MAAM,CAACgC,GAAG,CAAC1B,SAAS,EAAEA,SAAS,EAAEwB,EAAE,CAAC;YACpC9B,MAAM,CAACiC,GAAG,CAAC1B,aAAa,EAAEA,aAAa,EAAEuB,EAAE,CAAC;UAC9C;QACF,CAAC;QACD;QACAT,UAAU,CAACL,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAED,IAAI,CAAC;QAClC;QACAI,UAAU,CAACH,IAAI,EAAED,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;QAClC;QACAE,UAAU,CAACH,IAAI,EAAEC,IAAI,EAAEH,IAAI,EAAEG,IAAI,CAAC;QAClC;QACAE,UAAU,CAACL,IAAI,EAAEG,IAAI,EAAED,IAAI,EAAED,IAAI,CAAC;MACpC;MACA,IAAI,CAACiB,eAAe,CAAC5B,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,EAAEC,aAAa,CAAC,CAAC,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,CAAC;IACpH;EACF;EACA,IAAI6B,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;EACjC,IAAIC,YAAY,GAAGnC,QAAQ,CAACG,GAAG,CAAC,cAAc,CAAC;EAC/C,IAAIiC,UAAU,GAAGpC,QAAQ,CAACG,GAAG,CAAC,YAAY,CAAC;EAC3C,IAAIkC,SAAS,GAAGpC,GAAG,CAACqC,QAAQ,CAAC,CAAC;EAC9B,IAAIC,UAAU,GAAGtC,GAAG,CAACuC,SAAS,CAAC,CAAC;EAChC,IAAIC,MAAM,GAAGR,IAAI,CAACS,KAAK,GAAGT,IAAI,CAACU,MAAM,GAAG,IAAI,CAACC,WAAW;EACxD,IAAIC,gBAAgB,GAAG,KAAK;EAC5B,IAAIC,MAAM;EACV,IAAIC,IAAI;EACR,IAAIZ,YAAY,IAAIC,UAAU,EAAE;IAC9BU,MAAM,GAAG,CAAClD,UAAU,CAACoD,YAAY,CAACb,YAAY,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC,EAAEzC,UAAU,CAACoD,YAAY,CAACb,YAAY,CAAC,CAAC,CAAC,EAAEI,UAAU,CAAC,CAAC;IACpHQ,IAAI,GAAGnD,UAAU,CAACoD,YAAY,CAACZ,UAAU,EAAEa,IAAI,CAACnB,GAAG,CAACO,SAAS,EAAEE,UAAU,CAAC,CAAC;IAC3E,IAAI,CAACW,KAAK,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAACI,KAAK,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAACI,KAAK,CAACH,IAAI,CAAC,EAAE;MAC1DF,gBAAgB,GAAG,IAAI;IACzB,CAAC,MAAM;MACL,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACyC,IAAI,CAAC,uFAAuF,CAAC;MACvG;IACF;EACF;EACA,IAAIC,QAAQ;EACZ,IAAIP,gBAAgB,EAAE;IACpBO,QAAQ,GAAG,CAAC,CAAC;IACb,IAAIX,MAAM,GAAG,CAAC,EAAE;MACd;MACAW,QAAQ,CAACV,KAAK,GAAGK,IAAI;MACrBK,QAAQ,CAACT,MAAM,GAAGI,IAAI,GAAGN,MAAM;IACjC,CAAC,MAAM;MACLW,QAAQ,CAACT,MAAM,GAAGI,IAAI;MACtBK,QAAQ,CAACV,KAAK,GAAGK,IAAI,GAAGN,MAAM;IAChC;IACAW,QAAQ,CAACC,CAAC,GAAGP,MAAM,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAACT,MAAM,GAAG,CAAC;IAC5CS,QAAQ,CAACE,CAAC,GAAGR,MAAM,CAAC,CAAC,CAAC,GAAGM,QAAQ,CAACV,KAAK,GAAG,CAAC;EAC7C,CAAC,MAAM;IACL;IACA,IAAIa,eAAe,GAAGvD,QAAQ,CAACwD,kBAAkB,CAAC,CAAC;IACnDD,eAAe,CAACd,MAAM,GAAGA,MAAM;IAC/BW,QAAQ,GAAGzD,MAAM,CAAC8D,aAAa,CAACF,eAAe,EAAE;MAC/Cb,KAAK,EAAEL,SAAS;MAChBM,MAAM,EAAEJ;IACV,CAAC,CAAC;EACJ;EACA,IAAI,CAACmB,WAAW,CAACN,QAAQ,CAACE,CAAC,EAAEF,QAAQ,CAACC,CAAC,EAAED,QAAQ,CAACV,KAAK,EAAEU,QAAQ,CAACT,MAAM,CAAC;EACzE,IAAI,CAACgB,SAAS,CAAC3D,QAAQ,CAACG,GAAG,CAAC,QAAQ,CAAC,EAAEF,GAAG,CAAC;EAC3C,IAAI,CAAC2D,OAAO,CAAC5D,QAAQ,CAACG,GAAG,CAAC,MAAM,CAAC,CAAC;AACpC;AACA;AACA;AACA,SAAS0D,YAAYA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAChCvE,MAAM,CAACwE,IAAI,CAACD,KAAK,CAAC5D,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU8D,QAAQ,EAAEC,IAAI,EAAE;IAC3DJ,GAAG,CAACK,WAAW,CAACD,IAAI,EAAED,QAAQ,CAAC;EACjC,CAAC,CAAC;AACJ;AACA,IAAIG,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAAA,EAAG;IACpB;IACA,IAAI,CAACC,UAAU,GAAG3E,eAAe;EACnC;EACA0E,UAAU,CAACE,SAAS,CAACC,MAAM,GAAG,UAAUC,OAAO,EAAEvE,GAAG,EAAE;IACpD,IAAIwE,OAAO,GAAG,EAAE;IAChB,SAASC,sBAAsBA,CAACX,KAAK,EAAE;MACrC,OAAO;QACLY,YAAY,EAAEZ,KAAK,CAAC5D,GAAG,CAAC,cAAc,CAAC;QACvCyC,WAAW,EAAEmB,KAAK,CAAC5D,GAAG,CAAC,aAAa,CAAC;QACrCU,UAAU,EAAEkD,KAAK,CAAC5D,GAAG,CAAC,YAAY;MACpC,CAAC;IACH;IACA;IACAqE,OAAO,CAACI,aAAa,CAAC,KAAK,EAAE,UAAU5E,QAAQ,EAAE6E,GAAG,EAAE;MACpD,IAAIC,OAAO,GAAG9E,QAAQ,CAACG,GAAG,CAAC,KAAK,CAAC;MACjC,IAAI2D,GAAG,GAAG,IAAIrE,GAAG,CAACqF,OAAO,GAAGD,GAAG,EAAEC,OAAO,EAAEtF,MAAM,CAACuF,MAAM,CAAC;QACtDC,OAAO,EAAEhF,QAAQ,CAACG,GAAG,CAAC,SAAS;MACjC,CAAC,EAAEuE,sBAAsB,CAAC1E,QAAQ,CAAC,CAAC,CAAC;MACrC8D,GAAG,CAACmB,SAAS,GAAGjF,QAAQ,CAACG,GAAG,CAAC,YAAY,CAAC;MAC1CsE,OAAO,CAACS,IAAI,CAACpB,GAAG,CAAC;MACjB;MACA9D,QAAQ,CAACmF,gBAAgB,GAAGrB,GAAG;MAC/BA,GAAG,CAACC,KAAK,GAAG/D,QAAQ;MACpB;MACA8D,GAAG,CAACsB,MAAM,GAAGrF,SAAS;MACtB+D,GAAG,CAACsB,MAAM,CAACpF,QAAQ,EAAEC,GAAG,CAAC;IAC3B,CAAC,CAAC;IACFuE,OAAO,CAACa,UAAU,CAAC,UAAUC,WAAW,EAAE;MACxC,IAAIC,QAAQ,GAAGD,WAAW,CAACnF,GAAG,CAAC,kBAAkB,CAAC;MAClD,IAAIoF,QAAQ,KAAK,KAAK,EAAE;QACtB,IAAIC,QAAQ,GAAGF,WAAW,CAACnF,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;QAC/CmF,WAAW,CAACH,gBAAgB,GAAGV,OAAO,CAACe,QAAQ,CAAC;MAClD;IACF,CAAC,CAAC;IACF;IACA,IAAIC,qBAAqB,GAAG,CAAC,CAAC;IAC9BjB,OAAO,CAACkB,gBAAgB,CAAC,KAAK,EAAE,UAAUJ,WAAW,EAAE;MACrD,IAAI,CAACA,WAAW,CAACK,eAAe,CAAC,CAAC,EAAE;QAClC,IAAIC,OAAO,GAAGN,WAAW,CAACO,UAAU,CAAC,CAAC;QACtCJ,qBAAqB,CAACG,OAAO,CAAC,GAAGH,qBAAqB,CAACG,OAAO,CAAC,IAAI,EAAE;QACrEH,qBAAqB,CAACG,OAAO,CAAC,CAACV,IAAI,CAACI,WAAW,CAAC;MAClD;IACF,CAAC,CAAC;IACF9F,MAAM,CAACwE,IAAI,CAACyB,qBAAqB,EAAE,UAAUK,SAAS,EAAEF,OAAO,EAAE;MAC/D,IAAIG,WAAW,GAAGvG,MAAM,CAACwG,GAAG,CAACF,SAAS,EAAE,UAAUG,eAAe,EAAE;QACjE,OAAOA,eAAe,CAAC9F,GAAG,CAAC,SAAS,CAAC;MACvC,CAAC,CAAC;MACF,IAAI2D,GAAG,GAAG,IAAIrE,GAAG,CAACmG,OAAO,EAAEA,OAAO,EAAEpG,MAAM,CAACuF,MAAM,CAAC;QAChDC,OAAO,EAAExF,MAAM,CAAC0G,QAAQ,CAACH,WAAW;MACtC,CAAC,EAAErB,sBAAsB,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzChC,GAAG,CAACmB,SAAS,GAAGzF,MAAM,CAAC2G,QAAQ,CAACC,KAAK,CAAC,IAAI,EAAE5G,MAAM,CAACwG,GAAG,CAACF,SAAS,EAAE,UAAUG,eAAe,EAAE;QAC3F,OAAOA,eAAe,CAAC9F,GAAG,CAAC,YAAY,CAAC;MAC1C,CAAC,CAAC,CAAC;MACHsE,OAAO,CAACS,IAAI,CAACpB,GAAG,CAAC;MACjB;MACAA,GAAG,CAACsB,MAAM,GAAGrF,SAAS;MACtB+D,GAAG,CAACsB,MAAM,CAACU,SAAS,CAAC,CAAC,CAAC,EAAE7F,GAAG,CAAC;MAC7BT,MAAM,CAACwE,IAAI,CAAC8B,SAAS,EAAE,UAAUG,eAAe,EAAE;QAChDA,eAAe,CAACd,gBAAgB,GAAGrB,GAAG;QACtCD,YAAY,CAACC,GAAG,EAAEmC,eAAe,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOxB,OAAO;EAChB,CAAC;EACD;AACF;AACA;EACEL,UAAU,CAACE,SAAS,CAAC+B,gBAAgB,GAAG,UAAUC,eAAe,EAAExB,OAAO,EAAEE,OAAO,EAAEL,YAAY,EAAE;IACjG;IACA,IAAI4B,UAAU,GAAG,CAACD,eAAe,IAAI,EAAE,EAAEE,KAAK,CAAC,CAAC;IAChD,IAAIC,WAAW,GAAGjH,MAAM,CAACkH,aAAa,CAAC,CAAC;IACxC,KAAK,IAAIhF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6E,UAAU,CAACI,MAAM,EAAEjF,CAAC,EAAE,EAAE;MAC1C+E,WAAW,CAACG,GAAG,CAACL,UAAU,CAAC7E,CAAC,CAAC,CAACwC,IAAI,EAAEqC,UAAU,CAAC7E,CAAC,CAAC,CAAC;IACpD;IACA,IAAImF,MAAM,GAAGhH,gBAAgB,CAACiH,IAAI,CAAChC,OAAO,EAAEE,OAAO,EAAEL,YAAY,CAAC;IAClEnF,MAAM,CAACwE,IAAI,CAAC6C,MAAM,CAACE,OAAO,EAAE,UAAUC,MAAM,EAAE;MAC5C,IAAI9C,IAAI,GAAG8C,MAAM,CAAC9C,IAAI;MACtB,IAAI+C,YAAY,GAAGR,WAAW,CAACtG,GAAG,CAAC+D,IAAI,CAAC;MACxC;MACA,IAAIgD,2BAA2B,GAAGF,MAAM,CAACG,UAAU,IAAIH,MAAM,CAACG,UAAU,CAACC,YAAY;MACrF,IAAI,CAACH,YAAY,EAAE;QACjBA,YAAY,GAAG;UACb/C,IAAI,EAAEA;QACR,CAAC;QACDqC,UAAU,CAACrB,IAAI,CAAC+B,YAAY,CAAC;MAC/B;MACAC,2BAA2B,IAAI1H,MAAM,CAAC6H,KAAK,CAACJ,YAAY,EAAEC,2BAA2B,CAAC;IACxF,CAAC,CAAC;IACF,OAAOX,UAAU;EACnB,CAAC;EACD,OAAOnC,UAAU;AACnB,CAAC,CAAC,CAAC;AACH,IAAIkD,UAAU,GAAG,IAAIlD,UAAU,CAAC,CAAC;AACjC,eAAekD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}