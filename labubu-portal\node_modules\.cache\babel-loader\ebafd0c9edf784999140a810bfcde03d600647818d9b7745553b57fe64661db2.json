{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar each = zrUtil.each;\nexport default function visualMapPreprocessor(option) {\n  var visualMap = option && option.visualMap;\n  if (!zrUtil.isArray(visualMap)) {\n    visualMap = visualMap ? [visualMap] : [];\n  }\n  each(visualMap, function (opt) {\n    if (!opt) {\n      return;\n    }\n    // rename splitList to pieces\n    if (has(opt, 'splitList') && !has(opt, 'pieces')) {\n      opt.pieces = opt.splitList;\n      delete opt.splitList;\n    }\n    var pieces = opt.pieces;\n    if (pieces && zrUtil.isArray(pieces)) {\n      each(pieces, function (piece) {\n        if (zrUtil.isObject(piece)) {\n          if (has(piece, 'start') && !has(piece, 'min')) {\n            piece.min = piece.start;\n          }\n          if (has(piece, 'end') && !has(piece, 'max')) {\n            piece.max = piece.end;\n          }\n        }\n      });\n    }\n  });\n}\nfunction has(obj, name) {\n  return obj && obj.hasOwnProperty && obj.hasOwnProperty(name);\n}", "map": {"version": 3, "names": ["zrUtil", "each", "visualMapPreprocessor", "option", "visualMap", "isArray", "opt", "has", "pieces", "splitList", "piece", "isObject", "min", "start", "max", "end", "obj", "name", "hasOwnProperty"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/visualMap/preprocessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar each = zrUtil.each;\nexport default function visualMapPreprocessor(option) {\n  var visualMap = option && option.visualMap;\n  if (!zrUtil.isArray(visualMap)) {\n    visualMap = visualMap ? [visualMap] : [];\n  }\n  each(visualMap, function (opt) {\n    if (!opt) {\n      return;\n    }\n    // rename splitList to pieces\n    if (has(opt, 'splitList') && !has(opt, 'pieces')) {\n      opt.pieces = opt.splitList;\n      delete opt.splitList;\n    }\n    var pieces = opt.pieces;\n    if (pieces && zrUtil.isArray(pieces)) {\n      each(pieces, function (piece) {\n        if (zrUtil.isObject(piece)) {\n          if (has(piece, 'start') && !has(piece, 'min')) {\n            piece.min = piece.start;\n          }\n          if (has(piece, 'end') && !has(piece, 'max')) {\n            piece.max = piece.end;\n          }\n        }\n      });\n    }\n  });\n}\nfunction has(obj, name) {\n  return obj && obj.hasOwnProperty && obj.hasOwnProperty(name);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,IAAI,GAAGD,MAAM,CAACC,IAAI;AACtB,eAAe,SAASC,qBAAqBA,CAACC,MAAM,EAAE;EACpD,IAAIC,SAAS,GAAGD,MAAM,IAAIA,MAAM,CAACC,SAAS;EAC1C,IAAI,CAACJ,MAAM,CAACK,OAAO,CAACD,SAAS,CAAC,EAAE;IAC9BA,SAAS,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC,GAAG,EAAE;EAC1C;EACAH,IAAI,CAACG,SAAS,EAAE,UAAUE,GAAG,EAAE;IAC7B,IAAI,CAACA,GAAG,EAAE;MACR;IACF;IACA;IACA,IAAIC,GAAG,CAACD,GAAG,EAAE,WAAW,CAAC,IAAI,CAACC,GAAG,CAACD,GAAG,EAAE,QAAQ,CAAC,EAAE;MAChDA,GAAG,CAACE,MAAM,GAAGF,GAAG,CAACG,SAAS;MAC1B,OAAOH,GAAG,CAACG,SAAS;IACtB;IACA,IAAID,MAAM,GAAGF,GAAG,CAACE,MAAM;IACvB,IAAIA,MAAM,IAAIR,MAAM,CAACK,OAAO,CAACG,MAAM,CAAC,EAAE;MACpCP,IAAI,CAACO,MAAM,EAAE,UAAUE,KAAK,EAAE;QAC5B,IAAIV,MAAM,CAACW,QAAQ,CAACD,KAAK,CAAC,EAAE;UAC1B,IAAIH,GAAG,CAACG,KAAK,EAAE,OAAO,CAAC,IAAI,CAACH,GAAG,CAACG,KAAK,EAAE,KAAK,CAAC,EAAE;YAC7CA,KAAK,CAACE,GAAG,GAAGF,KAAK,CAACG,KAAK;UACzB;UACA,IAAIN,GAAG,CAACG,KAAK,EAAE,KAAK,CAAC,IAAI,CAACH,GAAG,CAACG,KAAK,EAAE,KAAK,CAAC,EAAE;YAC3CA,KAAK,CAACI,GAAG,GAAGJ,KAAK,CAACK,GAAG;UACvB;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;AACA,SAASR,GAAGA,CAACS,GAAG,EAAEC,IAAI,EAAE;EACtB,OAAOD,GAAG,IAAIA,GAAG,CAACE,cAAc,IAAIF,GAAG,CAACE,cAAc,CAACD,IAAI,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}