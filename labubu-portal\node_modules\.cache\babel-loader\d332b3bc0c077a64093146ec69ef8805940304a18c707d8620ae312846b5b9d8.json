{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as roundSectorHelper from '../helper/roundSector.js';\nvar SectorShape = function () {\n  function SectorShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.r0 = 0;\n    this.r = 0;\n    this.startAngle = 0;\n    this.endAngle = Math.PI * 2;\n    this.clockwise = true;\n    this.cornerRadius = 0;\n  }\n  return SectorShape;\n}();\nexport { SectorShape };\nvar Sector = function (_super) {\n  __extends(Sector, _super);\n  function Sector(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Sector.prototype.getDefaultShape = function () {\n    return new SectorShape();\n  };\n  Sector.prototype.buildPath = function (ctx, shape) {\n    roundSectorHelper.buildPath(ctx, shape);\n  };\n  Sector.prototype.isZeroArea = function () {\n    return this.shape.startAngle === this.shape.endAngle || this.shape.r === this.shape.r0;\n  };\n  return Sector;\n}(Path);\nSector.prototype.type = 'sector';\nexport default Sector;", "map": {"version": 3, "names": ["__extends", "Path", "roundSectorHelper", "SectorShape", "cx", "cy", "r0", "r", "startAngle", "endAngle", "Math", "PI", "clockwise", "cornerRadius", "Sector", "_super", "opts", "call", "prototype", "getDefaultShape", "buildPath", "ctx", "shape", "isZeroArea", "type"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/zrender/lib/graphic/shape/Sector.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nimport * as roundSectorHelper from '../helper/roundSector.js';\nvar SectorShape = (function () {\n    function SectorShape() {\n        this.cx = 0;\n        this.cy = 0;\n        this.r0 = 0;\n        this.r = 0;\n        this.startAngle = 0;\n        this.endAngle = Math.PI * 2;\n        this.clockwise = true;\n        this.cornerRadius = 0;\n    }\n    return SectorShape;\n}());\nexport { SectorShape };\nvar Sector = (function (_super) {\n    __extends(Sector, _super);\n    function Sector(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Sector.prototype.getDefaultShape = function () {\n        return new SectorShape();\n    };\n    Sector.prototype.buildPath = function (ctx, shape) {\n        roundSectorHelper.buildPath(ctx, shape);\n    };\n    Sector.prototype.isZeroArea = function () {\n        return this.shape.startAngle === this.shape.endAngle\n            || this.shape.r === this.shape.r0;\n    };\n    return Sector;\n}(Path));\nSector.prototype.type = 'sector';\nexport default Sector;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAO,KAAKC,iBAAiB,MAAM,0BAA0B;AAC7D,IAAIC,WAAW,GAAI,YAAY;EAC3B,SAASA,WAAWA,CAAA,EAAG;IACnB,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IAC3B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,YAAY,GAAG,CAAC;EACzB;EACA,OAAOV,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,SAASA,WAAW;AACpB,IAAIW,MAAM,GAAI,UAAUC,MAAM,EAAE;EAC5Bf,SAAS,CAACc,MAAM,EAAEC,MAAM,CAAC;EACzB,SAASD,MAAMA,CAACE,IAAI,EAAE;IAClB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EAC1C;EACAF,MAAM,CAACI,SAAS,CAACC,eAAe,GAAG,YAAY;IAC3C,OAAO,IAAIhB,WAAW,CAAC,CAAC;EAC5B,CAAC;EACDW,MAAM,CAACI,SAAS,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC/CpB,iBAAiB,CAACkB,SAAS,CAACC,GAAG,EAAEC,KAAK,CAAC;EAC3C,CAAC;EACDR,MAAM,CAACI,SAAS,CAACK,UAAU,GAAG,YAAY;IACtC,OAAO,IAAI,CAACD,KAAK,CAACd,UAAU,KAAK,IAAI,CAACc,KAAK,CAACb,QAAQ,IAC7C,IAAI,CAACa,KAAK,CAACf,CAAC,KAAK,IAAI,CAACe,KAAK,CAAChB,EAAE;EACzC,CAAC;EACD,OAAOQ,MAAM;AACjB,CAAC,CAACb,IAAI,CAAE;AACRa,MAAM,CAACI,SAAS,CAACM,IAAI,GAAG,QAAQ;AAChC,eAAeV,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}