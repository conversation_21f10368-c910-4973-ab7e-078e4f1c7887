{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport './assets/tailwind.css';\nVue.config.productionTip = false;\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "config", "productionTip", "render", "h", "$mount"], "sources": ["C:/suibianwanwan/labubu-portal/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport './assets/tailwind.css'\n\nVue.config.productionTip = false\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAO,uBAAuB;AAE9BD,GAAG,CAACE,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIH,GAAG,CAAC;EACNI,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACJ,GAAG;AACpB,CAAC,CAAC,CAACK,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}