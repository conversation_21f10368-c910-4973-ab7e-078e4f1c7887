# lodash.topath v4.5.2

The [lodash](https://lodash.com/) method `_.toPath` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.topath
```

In Node.js:
```js
var toPath = require('lodash.topath');
```

See the [documentation](https://lodash.com/docs#toPath) or [package source](https://github.com/lodash/lodash/blob/4.5.2-npm-packages/lodash.topath) for more details.
