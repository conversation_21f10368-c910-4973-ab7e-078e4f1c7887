{"name": "css-unit-converter", "version": "1.1.2", "description": "Converts CSS values from one unit to another", "main": "index.js", "scripts": {"test": "node test/test.js"}, "repository": {"type": "git", "url": "https://github.com/andy<PERSON>sson/css-unit-converter.git"}, "keywords": ["css", "value", "unit", "converter", "convert"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/andy<PERSON><PERSON>/css-unit-converter/issues"}, "homepage": "https://github.com/andy<PERSON><PERSON>/css-unit-converter", "devDependencies": {"tape": "^4.6.3"}}