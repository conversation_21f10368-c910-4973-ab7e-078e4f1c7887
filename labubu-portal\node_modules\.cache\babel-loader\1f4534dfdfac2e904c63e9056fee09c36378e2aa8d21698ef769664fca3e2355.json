{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Polyline from './Polyline.js';\nimport EffectLine from './EffectLine.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar EffectPolyline = /** @class */function (_super) {\n  __extends(EffectPolyline, _super);\n  function EffectPolyline() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._lastFrame = 0;\n    _this._lastFramePercent = 0;\n    return _this;\n  }\n  // Override\n  EffectPolyline.prototype.createLine = function (lineData, idx, seriesScope) {\n    return new Polyline(lineData, idx, seriesScope);\n  };\n  ;\n  // Override\n  EffectPolyline.prototype._updateAnimationPoints = function (symbol, points) {\n    this._points = points;\n    var accLenArr = [0];\n    var len = 0;\n    for (var i = 1; i < points.length; i++) {\n      var p1 = points[i - 1];\n      var p2 = points[i];\n      len += vec2.dist(p1, p2);\n      accLenArr.push(len);\n    }\n    if (len === 0) {\n      this._length = 0;\n      return;\n    }\n    for (var i = 0; i < accLenArr.length; i++) {\n      accLenArr[i] /= len;\n    }\n    this._offsets = accLenArr;\n    this._length = len;\n  };\n  ;\n  // Override\n  EffectPolyline.prototype._getLineLength = function () {\n    return this._length;\n  };\n  ;\n  // Override\n  EffectPolyline.prototype._updateSymbolPosition = function (symbol) {\n    var t = symbol.__t < 1 ? symbol.__t : 2 - symbol.__t;\n    var points = this._points;\n    var offsets = this._offsets;\n    var len = points.length;\n    if (!offsets) {\n      // Has length 0\n      return;\n    }\n    var lastFrame = this._lastFrame;\n    var frame;\n    if (t < this._lastFramePercent) {\n      // Start from the next frame\n      // PENDING start from lastFrame ?\n      var start = Math.min(lastFrame + 1, len - 1);\n      for (frame = start; frame >= 0; frame--) {\n        if (offsets[frame] <= t) {\n          break;\n        }\n      }\n      // PENDING really need to do this ?\n      frame = Math.min(frame, len - 2);\n    } else {\n      for (frame = lastFrame; frame < len; frame++) {\n        if (offsets[frame] > t) {\n          break;\n        }\n      }\n      frame = Math.min(frame - 1, len - 2);\n    }\n    var p = (t - offsets[frame]) / (offsets[frame + 1] - offsets[frame]);\n    var p0 = points[frame];\n    var p1 = points[frame + 1];\n    symbol.x = p0[0] * (1 - p) + p * p1[0];\n    symbol.y = p0[1] * (1 - p) + p * p1[1];\n    var tx = symbol.__t < 1 ? p1[0] - p0[0] : p0[0] - p1[0];\n    var ty = symbol.__t < 1 ? p1[1] - p0[1] : p0[1] - p1[1];\n    symbol.rotation = -Math.atan2(ty, tx) - Math.PI / 2;\n    this._lastFrame = frame;\n    this._lastFramePercent = t;\n    symbol.ignore = false;\n  };\n  ;\n  return EffectPolyline;\n}(EffectLine);\nexport default EffectPolyline;", "map": {"version": 3, "names": ["__extends", "Polyline", "EffectLine", "vec2", "EffectPolyline", "_super", "_this", "apply", "arguments", "_lastFrame", "_lastFrameP<PERSON>cent", "prototype", "createLine", "lineData", "idx", "seriesScope", "_updateAnimationPoints", "symbol", "points", "_points", "accLenArr", "len", "i", "length", "p1", "p2", "dist", "push", "_length", "_offsets", "_getLine<PERSON>ength", "_updateSymbolPosition", "t", "__t", "offsets", "<PERSON><PERSON><PERSON><PERSON>", "frame", "start", "Math", "min", "p", "p0", "x", "y", "tx", "ty", "rotation", "atan2", "PI", "ignore"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/helper/EffectPolyline.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Polyline from './Polyline.js';\nimport EffectLine from './EffectLine.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar EffectPolyline = /** @class */function (_super) {\n  __extends(EffectPolyline, _super);\n  function EffectPolyline() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this._lastFrame = 0;\n    _this._lastFramePercent = 0;\n    return _this;\n  }\n  // Override\n  EffectPolyline.prototype.createLine = function (lineData, idx, seriesScope) {\n    return new Polyline(lineData, idx, seriesScope);\n  };\n  ;\n  // Override\n  EffectPolyline.prototype._updateAnimationPoints = function (symbol, points) {\n    this._points = points;\n    var accLenArr = [0];\n    var len = 0;\n    for (var i = 1; i < points.length; i++) {\n      var p1 = points[i - 1];\n      var p2 = points[i];\n      len += vec2.dist(p1, p2);\n      accLenArr.push(len);\n    }\n    if (len === 0) {\n      this._length = 0;\n      return;\n    }\n    for (var i = 0; i < accLenArr.length; i++) {\n      accLenArr[i] /= len;\n    }\n    this._offsets = accLenArr;\n    this._length = len;\n  };\n  ;\n  // Override\n  EffectPolyline.prototype._getLineLength = function () {\n    return this._length;\n  };\n  ;\n  // Override\n  EffectPolyline.prototype._updateSymbolPosition = function (symbol) {\n    var t = symbol.__t < 1 ? symbol.__t : 2 - symbol.__t;\n    var points = this._points;\n    var offsets = this._offsets;\n    var len = points.length;\n    if (!offsets) {\n      // Has length 0\n      return;\n    }\n    var lastFrame = this._lastFrame;\n    var frame;\n    if (t < this._lastFramePercent) {\n      // Start from the next frame\n      // PENDING start from lastFrame ?\n      var start = Math.min(lastFrame + 1, len - 1);\n      for (frame = start; frame >= 0; frame--) {\n        if (offsets[frame] <= t) {\n          break;\n        }\n      }\n      // PENDING really need to do this ?\n      frame = Math.min(frame, len - 2);\n    } else {\n      for (frame = lastFrame; frame < len; frame++) {\n        if (offsets[frame] > t) {\n          break;\n        }\n      }\n      frame = Math.min(frame - 1, len - 2);\n    }\n    var p = (t - offsets[frame]) / (offsets[frame + 1] - offsets[frame]);\n    var p0 = points[frame];\n    var p1 = points[frame + 1];\n    symbol.x = p0[0] * (1 - p) + p * p1[0];\n    symbol.y = p0[1] * (1 - p) + p * p1[1];\n    var tx = symbol.__t < 1 ? p1[0] - p0[0] : p0[0] - p1[0];\n    var ty = symbol.__t < 1 ? p1[1] - p0[1] : p0[1] - p1[1];\n    symbol.rotation = -Math.atan2(ty, tx) - Math.PI / 2;\n    this._lastFrame = frame;\n    this._lastFramePercent = t;\n    symbol.ignore = false;\n  };\n  ;\n  return EffectPolyline;\n}(EffectLine);\nexport default EffectPolyline;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAO,KAAKC,IAAI,MAAM,4BAA4B;AAClD,IAAIC,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDL,SAAS,CAACI,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,UAAU,GAAG,CAAC;IACpBH,KAAK,CAACI,iBAAiB,GAAG,CAAC;IAC3B,OAAOJ,KAAK;EACd;EACA;EACAF,cAAc,CAACO,SAAS,CAACC,UAAU,GAAG,UAAUC,QAAQ,EAAEC,GAAG,EAAEC,WAAW,EAAE;IAC1E,OAAO,IAAId,QAAQ,CAACY,QAAQ,EAAEC,GAAG,EAAEC,WAAW,CAAC;EACjD,CAAC;EACD;EACA;EACAX,cAAc,CAACO,SAAS,CAACK,sBAAsB,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAE;IAC1E,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB,IAAIE,SAAS,GAAG,CAAC,CAAC,CAAC;IACnB,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIE,EAAE,GAAGN,MAAM,CAACI,CAAC,GAAG,CAAC,CAAC;MACtB,IAAIG,EAAE,GAAGP,MAAM,CAACI,CAAC,CAAC;MAClBD,GAAG,IAAIlB,IAAI,CAACuB,IAAI,CAACF,EAAE,EAAEC,EAAE,CAAC;MACxBL,SAAS,CAACO,IAAI,CAACN,GAAG,CAAC;IACrB;IACA,IAAIA,GAAG,KAAK,CAAC,EAAE;MACb,IAAI,CAACO,OAAO,GAAG,CAAC;MAChB;IACF;IACA,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACzCF,SAAS,CAACE,CAAC,CAAC,IAAID,GAAG;IACrB;IACA,IAAI,CAACQ,QAAQ,GAAGT,SAAS;IACzB,IAAI,CAACQ,OAAO,GAAGP,GAAG;EACpB,CAAC;EACD;EACA;EACAjB,cAAc,CAACO,SAAS,CAACmB,cAAc,GAAG,YAAY;IACpD,OAAO,IAAI,CAACF,OAAO;EACrB,CAAC;EACD;EACA;EACAxB,cAAc,CAACO,SAAS,CAACoB,qBAAqB,GAAG,UAAUd,MAAM,EAAE;IACjE,IAAIe,CAAC,GAAGf,MAAM,CAACgB,GAAG,GAAG,CAAC,GAAGhB,MAAM,CAACgB,GAAG,GAAG,CAAC,GAAGhB,MAAM,CAACgB,GAAG;IACpD,IAAIf,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAIe,OAAO,GAAG,IAAI,CAACL,QAAQ;IAC3B,IAAIR,GAAG,GAAGH,MAAM,CAACK,MAAM;IACvB,IAAI,CAACW,OAAO,EAAE;MACZ;MACA;IACF;IACA,IAAIC,SAAS,GAAG,IAAI,CAAC1B,UAAU;IAC/B,IAAI2B,KAAK;IACT,IAAIJ,CAAC,GAAG,IAAI,CAACtB,iBAAiB,EAAE;MAC9B;MACA;MACA,IAAI2B,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,SAAS,GAAG,CAAC,EAAEd,GAAG,GAAG,CAAC,CAAC;MAC5C,KAAKe,KAAK,GAAGC,KAAK,EAAED,KAAK,IAAI,CAAC,EAAEA,KAAK,EAAE,EAAE;QACvC,IAAIF,OAAO,CAACE,KAAK,CAAC,IAAIJ,CAAC,EAAE;UACvB;QACF;MACF;MACA;MACAI,KAAK,GAAGE,IAAI,CAACC,GAAG,CAACH,KAAK,EAAEf,GAAG,GAAG,CAAC,CAAC;IAClC,CAAC,MAAM;MACL,KAAKe,KAAK,GAAGD,SAAS,EAAEC,KAAK,GAAGf,GAAG,EAAEe,KAAK,EAAE,EAAE;QAC5C,IAAIF,OAAO,CAACE,KAAK,CAAC,GAAGJ,CAAC,EAAE;UACtB;QACF;MACF;MACAI,KAAK,GAAGE,IAAI,CAACC,GAAG,CAACH,KAAK,GAAG,CAAC,EAAEf,GAAG,GAAG,CAAC,CAAC;IACtC;IACA,IAAImB,CAAC,GAAG,CAACR,CAAC,GAAGE,OAAO,CAACE,KAAK,CAAC,KAAKF,OAAO,CAACE,KAAK,GAAG,CAAC,CAAC,GAAGF,OAAO,CAACE,KAAK,CAAC,CAAC;IACpE,IAAIK,EAAE,GAAGvB,MAAM,CAACkB,KAAK,CAAC;IACtB,IAAIZ,EAAE,GAAGN,MAAM,CAACkB,KAAK,GAAG,CAAC,CAAC;IAC1BnB,MAAM,CAACyB,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC,GAAGA,CAAC,GAAGhB,EAAE,CAAC,CAAC,CAAC;IACtCP,MAAM,CAAC0B,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC,GAAGA,CAAC,GAAGhB,EAAE,CAAC,CAAC,CAAC;IACtC,IAAIoB,EAAE,GAAG3B,MAAM,CAACgB,GAAG,GAAG,CAAC,GAAGT,EAAE,CAAC,CAAC,CAAC,GAAGiB,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAGjB,EAAE,CAAC,CAAC,CAAC;IACvD,IAAIqB,EAAE,GAAG5B,MAAM,CAACgB,GAAG,GAAG,CAAC,GAAGT,EAAE,CAAC,CAAC,CAAC,GAAGiB,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAGjB,EAAE,CAAC,CAAC,CAAC;IACvDP,MAAM,CAAC6B,QAAQ,GAAG,CAACR,IAAI,CAACS,KAAK,CAACF,EAAE,EAAED,EAAE,CAAC,GAAGN,IAAI,CAACU,EAAE,GAAG,CAAC;IACnD,IAAI,CAACvC,UAAU,GAAG2B,KAAK;IACvB,IAAI,CAAC1B,iBAAiB,GAAGsB,CAAC;IAC1Bf,MAAM,CAACgC,MAAM,GAAG,KAAK;EACvB,CAAC;EACD;EACA,OAAO7C,cAAc;AACvB,CAAC,CAACF,UAAU,CAAC;AACb,eAAeE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}