{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.typed-array.with.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO Batch by color\nimport * as graphic from '../../util/graphic.js';\nimport * as lineContain from 'zrender/lib/contain/line.js';\nimport * as quadraticContain from 'zrender/lib/contain/quadratic.js';\nimport { getECData } from '../../util/innerStore.js';\nvar LargeLinesPathShape = /** @class */function () {\n  function LargeLinesPathShape() {\n    this.polyline = false;\n    this.curveness = 0;\n    this.segs = [];\n  }\n  return LargeLinesPathShape;\n}();\nvar LargeLinesPath = /** @class */function (_super) {\n  __extends(LargeLinesPath, _super);\n  function LargeLinesPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this._off = 0;\n    _this.hoverDataIdx = -1;\n    return _this;\n  }\n  LargeLinesPath.prototype.reset = function () {\n    this.notClear = false;\n    this._off = 0;\n  };\n  LargeLinesPath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  LargeLinesPath.prototype.getDefaultShape = function () {\n    return new LargeLinesPathShape();\n  };\n  LargeLinesPath.prototype.buildPath = function (ctx, shape) {\n    var segs = shape.segs;\n    var curveness = shape.curveness;\n    var i;\n    if (shape.polyline) {\n      for (i = this._off; i < segs.length;) {\n        var count = segs[i++];\n        if (count > 0) {\n          ctx.moveTo(segs[i++], segs[i++]);\n          for (var k = 1; k < count; k++) {\n            ctx.lineTo(segs[i++], segs[i++]);\n          }\n        }\n      }\n    } else {\n      for (i = this._off; i < segs.length;) {\n        var x0 = segs[i++];\n        var y0 = segs[i++];\n        var x1 = segs[i++];\n        var y1 = segs[i++];\n        ctx.moveTo(x0, y0);\n        if (curveness > 0) {\n          var x2 = (x0 + x1) / 2 - (y0 - y1) * curveness;\n          var y2 = (y0 + y1) / 2 - (x1 - x0) * curveness;\n          ctx.quadraticCurveTo(x2, y2, x1, y1);\n        } else {\n          ctx.lineTo(x1, y1);\n        }\n      }\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeLinesPath.prototype.findDataIndex = function (x, y) {\n    var shape = this.shape;\n    var segs = shape.segs;\n    var curveness = shape.curveness;\n    var lineWidth = this.style.lineWidth;\n    if (shape.polyline) {\n      var dataIndex = 0;\n      for (var i = 0; i < segs.length;) {\n        var count = segs[i++];\n        if (count > 0) {\n          var x0 = segs[i++];\n          var y0 = segs[i++];\n          for (var k = 1; k < count; k++) {\n            var x1 = segs[i++];\n            var y1 = segs[i++];\n            if (lineContain.containStroke(x0, y0, x1, y1, lineWidth, x, y)) {\n              return dataIndex;\n            }\n          }\n        }\n        dataIndex++;\n      }\n    } else {\n      var dataIndex = 0;\n      for (var i = 0; i < segs.length;) {\n        var x0 = segs[i++];\n        var y0 = segs[i++];\n        var x1 = segs[i++];\n        var y1 = segs[i++];\n        if (curveness > 0) {\n          var x2 = (x0 + x1) / 2 - (y0 - y1) * curveness;\n          var y2 = (y0 + y1) / 2 - (x1 - x0) * curveness;\n          if (quadraticContain.containStroke(x0, y0, x2, y2, x1, y1, lineWidth, x, y)) {\n            return dataIndex;\n          }\n        } else {\n          if (lineContain.containStroke(x0, y0, x1, y1, lineWidth, x, y)) {\n            return dataIndex;\n          }\n        }\n        dataIndex++;\n      }\n    }\n    return -1;\n  };\n  LargeLinesPath.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    x = localPos[0];\n    y = localPos[1];\n    if (rect.contain(x, y)) {\n      // Cache found data index.\n      var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);\n      return dataIdx >= 0;\n    }\n    this.hoverDataIdx = -1;\n    return false;\n  };\n  LargeLinesPath.prototype.getBoundingRect = function () {\n    // Ignore stroke for large symbol draw.\n    var rect = this._rect;\n    if (!rect) {\n      var shape = this.shape;\n      var points = shape.segs;\n      var minX = Infinity;\n      var minY = Infinity;\n      var maxX = -Infinity;\n      var maxY = -Infinity;\n      for (var i = 0; i < points.length;) {\n        var x = points[i++];\n        var y = points[i++];\n        minX = Math.min(x, minX);\n        maxX = Math.max(x, maxX);\n        minY = Math.min(y, minY);\n        maxY = Math.max(y, maxY);\n      }\n      rect = this._rect = new graphic.BoundingRect(minX, minY, maxX, maxY);\n    }\n    return rect;\n  };\n  return LargeLinesPath;\n}(graphic.Path);\nvar LargeLineDraw = /** @class */function () {\n  function LargeLineDraw() {\n    this.group = new graphic.Group();\n  }\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  LargeLineDraw.prototype.updateData = function (data) {\n    this._clear();\n    var lineEl = this._create();\n    lineEl.setShape({\n      segs: data.getLayout('linesPoints')\n    });\n    this._setCommon(lineEl, data);\n  };\n  ;\n  /**\r\n   * @override\r\n   */\n  LargeLineDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this.group.removeAll();\n    this._clear();\n  };\n  ;\n  /**\r\n   * @override\r\n   */\n  LargeLineDraw.prototype.incrementalUpdate = function (taskParams, data) {\n    var lastAdded = this._newAdded[0];\n    var linePoints = data.getLayout('linesPoints');\n    var oldSegs = lastAdded && lastAdded.shape.segs;\n    // Merging the exists. Each element has 1e4 points.\n    // Consider the performance balance between too much elements and too much points in one shape(may affect hover optimization)\n    if (oldSegs && oldSegs.length < 2e4) {\n      var oldLen = oldSegs.length;\n      var newSegs = new Float32Array(oldLen + linePoints.length);\n      // Concat two array\n      newSegs.set(oldSegs);\n      newSegs.set(linePoints, oldLen);\n      lastAdded.setShape({\n        segs: newSegs\n      });\n    } else {\n      // Clear\n      this._newAdded = [];\n      var lineEl = this._create();\n      lineEl.incremental = true;\n      lineEl.setShape({\n        segs: linePoints\n      });\n      this._setCommon(lineEl, data);\n      lineEl.__startIndex = taskParams.start;\n    }\n  };\n  /**\r\n   * @override\r\n   */\n  LargeLineDraw.prototype.remove = function () {\n    this._clear();\n  };\n  LargeLineDraw.prototype.eachRendered = function (cb) {\n    this._newAdded[0] && cb(this._newAdded[0]);\n  };\n  LargeLineDraw.prototype._create = function () {\n    var lineEl = new LargeLinesPath({\n      cursor: 'default',\n      ignoreCoarsePointer: true\n    });\n    this._newAdded.push(lineEl);\n    this.group.add(lineEl);\n    return lineEl;\n  };\n  LargeLineDraw.prototype._setCommon = function (lineEl, data, isIncremental) {\n    var hostModel = data.hostModel;\n    lineEl.setShape({\n      polyline: hostModel.get('polyline'),\n      curveness: hostModel.get(['lineStyle', 'curveness'])\n    });\n    lineEl.useStyle(hostModel.getModel('lineStyle').getLineStyle());\n    lineEl.style.strokeNoScale = true;\n    var style = data.getVisual('style');\n    if (style && style.stroke) {\n      lineEl.setStyle('stroke', style.stroke);\n    }\n    lineEl.setStyle('fill', null);\n    var ecData = getECData(lineEl);\n    // Enable tooltip\n    // PENDING May have performance issue when path is extremely large\n    ecData.seriesIndex = hostModel.seriesIndex;\n    lineEl.on('mousemove', function (e) {\n      ecData.dataIndex = null;\n      var dataIndex = lineEl.hoverDataIdx;\n      if (dataIndex > 0) {\n        // Provide dataIndex for tooltip\n        ecData.dataIndex = dataIndex + lineEl.__startIndex;\n      }\n    });\n  };\n  ;\n  LargeLineDraw.prototype._clear = function () {\n    this._newAdded = [];\n    this.group.removeAll();\n  };\n  ;\n  return LargeLineDraw;\n}();\nexport default LargeLineDraw;", "map": {"version": 3, "names": ["__extends", "graphic", "lineContain", "quadraticContain", "getECData", "LargeLinesPathShape", "polyline", "curveness", "segs", "LargeLinesPath", "_super", "opts", "_this", "call", "_off", "hoverDataIdx", "prototype", "reset", "notClear", "getDefaultStyle", "stroke", "fill", "getDefaultShape", "buildPath", "ctx", "shape", "i", "length", "count", "moveTo", "k", "lineTo", "x0", "y0", "x1", "y1", "x2", "y2", "quadraticCurveTo", "incremental", "findDataIndex", "x", "y", "lineWidth", "style", "dataIndex", "containStroke", "contain", "localPos", "transformCoordToLocal", "rect", "getBoundingRect", "dataIdx", "_rect", "points", "minX", "Infinity", "minY", "maxX", "maxY", "Math", "min", "max", "BoundingRect", "Path", "LargeLineDraw", "group", "Group", "updateData", "data", "_clear", "lineEl", "_create", "setShape", "getLayout", "_setCommon", "incrementalPrepareUpdate", "removeAll", "incrementalUpdate", "taskParams", "lastAdded", "_newAdded", "linePoints", "oldSegs", "old<PERSON>en", "newSegs", "Float32Array", "set", "__startIndex", "start", "remove", "eachRendered", "cb", "cursor", "ignoreCoarsePointer", "push", "add", "isIncremental", "hostModel", "get", "useStyle", "getModel", "getLineStyle", "strokeNoScale", "getVisual", "setStyle", "ecData", "seriesIndex", "on", "e"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/helper/LargeLineDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO Batch by color\nimport * as graphic from '../../util/graphic.js';\nimport * as lineContain from 'zrender/lib/contain/line.js';\nimport * as quadraticContain from 'zrender/lib/contain/quadratic.js';\nimport { getECData } from '../../util/innerStore.js';\nvar LargeLinesPathShape = /** @class */function () {\n  function LargeLinesPathShape() {\n    this.polyline = false;\n    this.curveness = 0;\n    this.segs = [];\n  }\n  return LargeLinesPathShape;\n}();\nvar LargeLinesPath = /** @class */function (_super) {\n  __extends(LargeLinesPath, _super);\n  function LargeLinesPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this._off = 0;\n    _this.hoverDataIdx = -1;\n    return _this;\n  }\n  LargeLinesPath.prototype.reset = function () {\n    this.notClear = false;\n    this._off = 0;\n  };\n  LargeLinesPath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  LargeLinesPath.prototype.getDefaultShape = function () {\n    return new LargeLinesPathShape();\n  };\n  LargeLinesPath.prototype.buildPath = function (ctx, shape) {\n    var segs = shape.segs;\n    var curveness = shape.curveness;\n    var i;\n    if (shape.polyline) {\n      for (i = this._off; i < segs.length;) {\n        var count = segs[i++];\n        if (count > 0) {\n          ctx.moveTo(segs[i++], segs[i++]);\n          for (var k = 1; k < count; k++) {\n            ctx.lineTo(segs[i++], segs[i++]);\n          }\n        }\n      }\n    } else {\n      for (i = this._off; i < segs.length;) {\n        var x0 = segs[i++];\n        var y0 = segs[i++];\n        var x1 = segs[i++];\n        var y1 = segs[i++];\n        ctx.moveTo(x0, y0);\n        if (curveness > 0) {\n          var x2 = (x0 + x1) / 2 - (y0 - y1) * curveness;\n          var y2 = (y0 + y1) / 2 - (x1 - x0) * curveness;\n          ctx.quadraticCurveTo(x2, y2, x1, y1);\n        } else {\n          ctx.lineTo(x1, y1);\n        }\n      }\n    }\n    if (this.incremental) {\n      this._off = i;\n      this.notClear = true;\n    }\n  };\n  LargeLinesPath.prototype.findDataIndex = function (x, y) {\n    var shape = this.shape;\n    var segs = shape.segs;\n    var curveness = shape.curveness;\n    var lineWidth = this.style.lineWidth;\n    if (shape.polyline) {\n      var dataIndex = 0;\n      for (var i = 0; i < segs.length;) {\n        var count = segs[i++];\n        if (count > 0) {\n          var x0 = segs[i++];\n          var y0 = segs[i++];\n          for (var k = 1; k < count; k++) {\n            var x1 = segs[i++];\n            var y1 = segs[i++];\n            if (lineContain.containStroke(x0, y0, x1, y1, lineWidth, x, y)) {\n              return dataIndex;\n            }\n          }\n        }\n        dataIndex++;\n      }\n    } else {\n      var dataIndex = 0;\n      for (var i = 0; i < segs.length;) {\n        var x0 = segs[i++];\n        var y0 = segs[i++];\n        var x1 = segs[i++];\n        var y1 = segs[i++];\n        if (curveness > 0) {\n          var x2 = (x0 + x1) / 2 - (y0 - y1) * curveness;\n          var y2 = (y0 + y1) / 2 - (x1 - x0) * curveness;\n          if (quadraticContain.containStroke(x0, y0, x2, y2, x1, y1, lineWidth, x, y)) {\n            return dataIndex;\n          }\n        } else {\n          if (lineContain.containStroke(x0, y0, x1, y1, lineWidth, x, y)) {\n            return dataIndex;\n          }\n        }\n        dataIndex++;\n      }\n    }\n    return -1;\n  };\n  LargeLinesPath.prototype.contain = function (x, y) {\n    var localPos = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    x = localPos[0];\n    y = localPos[1];\n    if (rect.contain(x, y)) {\n      // Cache found data index.\n      var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);\n      return dataIdx >= 0;\n    }\n    this.hoverDataIdx = -1;\n    return false;\n  };\n  LargeLinesPath.prototype.getBoundingRect = function () {\n    // Ignore stroke for large symbol draw.\n    var rect = this._rect;\n    if (!rect) {\n      var shape = this.shape;\n      var points = shape.segs;\n      var minX = Infinity;\n      var minY = Infinity;\n      var maxX = -Infinity;\n      var maxY = -Infinity;\n      for (var i = 0; i < points.length;) {\n        var x = points[i++];\n        var y = points[i++];\n        minX = Math.min(x, minX);\n        maxX = Math.max(x, maxX);\n        minY = Math.min(y, minY);\n        maxY = Math.max(y, maxY);\n      }\n      rect = this._rect = new graphic.BoundingRect(minX, minY, maxX, maxY);\n    }\n    return rect;\n  };\n  return LargeLinesPath;\n}(graphic.Path);\nvar LargeLineDraw = /** @class */function () {\n  function LargeLineDraw() {\n    this.group = new graphic.Group();\n  }\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  LargeLineDraw.prototype.updateData = function (data) {\n    this._clear();\n    var lineEl = this._create();\n    lineEl.setShape({\n      segs: data.getLayout('linesPoints')\n    });\n    this._setCommon(lineEl, data);\n  };\n  ;\n  /**\r\n   * @override\r\n   */\n  LargeLineDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this.group.removeAll();\n    this._clear();\n  };\n  ;\n  /**\r\n   * @override\r\n   */\n  LargeLineDraw.prototype.incrementalUpdate = function (taskParams, data) {\n    var lastAdded = this._newAdded[0];\n    var linePoints = data.getLayout('linesPoints');\n    var oldSegs = lastAdded && lastAdded.shape.segs;\n    // Merging the exists. Each element has 1e4 points.\n    // Consider the performance balance between too much elements and too much points in one shape(may affect hover optimization)\n    if (oldSegs && oldSegs.length < 2e4) {\n      var oldLen = oldSegs.length;\n      var newSegs = new Float32Array(oldLen + linePoints.length);\n      // Concat two array\n      newSegs.set(oldSegs);\n      newSegs.set(linePoints, oldLen);\n      lastAdded.setShape({\n        segs: newSegs\n      });\n    } else {\n      // Clear\n      this._newAdded = [];\n      var lineEl = this._create();\n      lineEl.incremental = true;\n      lineEl.setShape({\n        segs: linePoints\n      });\n      this._setCommon(lineEl, data);\n      lineEl.__startIndex = taskParams.start;\n    }\n  };\n  /**\r\n   * @override\r\n   */\n  LargeLineDraw.prototype.remove = function () {\n    this._clear();\n  };\n  LargeLineDraw.prototype.eachRendered = function (cb) {\n    this._newAdded[0] && cb(this._newAdded[0]);\n  };\n  LargeLineDraw.prototype._create = function () {\n    var lineEl = new LargeLinesPath({\n      cursor: 'default',\n      ignoreCoarsePointer: true\n    });\n    this._newAdded.push(lineEl);\n    this.group.add(lineEl);\n    return lineEl;\n  };\n  LargeLineDraw.prototype._setCommon = function (lineEl, data, isIncremental) {\n    var hostModel = data.hostModel;\n    lineEl.setShape({\n      polyline: hostModel.get('polyline'),\n      curveness: hostModel.get(['lineStyle', 'curveness'])\n    });\n    lineEl.useStyle(hostModel.getModel('lineStyle').getLineStyle());\n    lineEl.style.strokeNoScale = true;\n    var style = data.getVisual('style');\n    if (style && style.stroke) {\n      lineEl.setStyle('stroke', style.stroke);\n    }\n    lineEl.setStyle('fill', null);\n    var ecData = getECData(lineEl);\n    // Enable tooltip\n    // PENDING May have performance issue when path is extremely large\n    ecData.seriesIndex = hostModel.seriesIndex;\n    lineEl.on('mousemove', function (e) {\n      ecData.dataIndex = null;\n      var dataIndex = lineEl.hoverDataIdx;\n      if (dataIndex > 0) {\n        // Provide dataIndex for tooltip\n        ecData.dataIndex = dataIndex + lineEl.__startIndex;\n      }\n    });\n  };\n  ;\n  LargeLineDraw.prototype._clear = function () {\n    this._newAdded = [];\n    this.group.removeAll();\n  };\n  ;\n  return LargeLineDraw;\n}();\nexport default LargeLineDraw;"], "mappings": ";;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,WAAW,MAAM,6BAA6B;AAC1D,OAAO,KAAKC,gBAAgB,MAAM,kCAAkC;AACpE,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,mBAAmB,GAAG,aAAa,YAAY;EACjD,SAASA,mBAAmBA,CAAA,EAAG;IAC7B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,IAAI,GAAG,EAAE;EAChB;EACA,OAAOH,mBAAmB;AAC5B,CAAC,CAAC,CAAC;AACH,IAAII,cAAc,GAAG,aAAa,UAAUC,MAAM,EAAE;EAClDV,SAAS,CAACS,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAACE,IAAI,EAAE;IAC5B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,CAAC,IAAI,IAAI;IAC3CC,KAAK,CAACE,IAAI,GAAG,CAAC;IACdF,KAAK,CAACG,YAAY,GAAG,CAAC,CAAC;IACvB,OAAOH,KAAK;EACd;EACAH,cAAc,CAACO,SAAS,CAACC,KAAK,GAAG,YAAY;IAC3C,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACJ,IAAI,GAAG,CAAC;EACf,CAAC;EACDL,cAAc,CAACO,SAAS,CAACG,eAAe,GAAG,YAAY;IACrD,OAAO;MACLC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDZ,cAAc,CAACO,SAAS,CAACM,eAAe,GAAG,YAAY;IACrD,OAAO,IAAIjB,mBAAmB,CAAC,CAAC;EAClC,CAAC;EACDI,cAAc,CAACO,SAAS,CAACO,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACzD,IAAIjB,IAAI,GAAGiB,KAAK,CAACjB,IAAI;IACrB,IAAID,SAAS,GAAGkB,KAAK,CAAClB,SAAS;IAC/B,IAAImB,CAAC;IACL,IAAID,KAAK,CAACnB,QAAQ,EAAE;MAClB,KAAKoB,CAAC,GAAG,IAAI,CAACZ,IAAI,EAAEY,CAAC,GAAGlB,IAAI,CAACmB,MAAM,GAAG;QACpC,IAAIC,KAAK,GAAGpB,IAAI,CAACkB,CAAC,EAAE,CAAC;QACrB,IAAIE,KAAK,GAAG,CAAC,EAAE;UACbJ,GAAG,CAACK,MAAM,CAACrB,IAAI,CAACkB,CAAC,EAAE,CAAC,EAAElB,IAAI,CAACkB,CAAC,EAAE,CAAC,CAAC;UAChC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;YAC9BN,GAAG,CAACO,MAAM,CAACvB,IAAI,CAACkB,CAAC,EAAE,CAAC,EAAElB,IAAI,CAACkB,CAAC,EAAE,CAAC,CAAC;UAClC;QACF;MACF;IACF,CAAC,MAAM;MACL,KAAKA,CAAC,GAAG,IAAI,CAACZ,IAAI,EAAEY,CAAC,GAAGlB,IAAI,CAACmB,MAAM,GAAG;QACpC,IAAIK,EAAE,GAAGxB,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClB,IAAIO,EAAE,GAAGzB,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClB,IAAIQ,EAAE,GAAG1B,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClB,IAAIS,EAAE,GAAG3B,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClBF,GAAG,CAACK,MAAM,CAACG,EAAE,EAAEC,EAAE,CAAC;QAClB,IAAI1B,SAAS,GAAG,CAAC,EAAE;UACjB,IAAI6B,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC,GAAG,CAACD,EAAE,GAAGE,EAAE,IAAI5B,SAAS;UAC9C,IAAI8B,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC,GAAG,CAACD,EAAE,GAAGF,EAAE,IAAIzB,SAAS;UAC9CiB,GAAG,CAACc,gBAAgB,CAACF,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEC,EAAE,CAAC;QACtC,CAAC,MAAM;UACLX,GAAG,CAACO,MAAM,CAACG,EAAE,EAAEC,EAAE,CAAC;QACpB;MACF;IACF;IACA,IAAI,IAAI,CAACI,WAAW,EAAE;MACpB,IAAI,CAACzB,IAAI,GAAGY,CAAC;MACb,IAAI,CAACR,QAAQ,GAAG,IAAI;IACtB;EACF,CAAC;EACDT,cAAc,CAACO,SAAS,CAACwB,aAAa,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACvD,IAAIjB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIjB,IAAI,GAAGiB,KAAK,CAACjB,IAAI;IACrB,IAAID,SAAS,GAAGkB,KAAK,CAAClB,SAAS;IAC/B,IAAIoC,SAAS,GAAG,IAAI,CAACC,KAAK,CAACD,SAAS;IACpC,IAAIlB,KAAK,CAACnB,QAAQ,EAAE;MAClB,IAAIuC,SAAS,GAAG,CAAC;MACjB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,IAAI,CAACmB,MAAM,GAAG;QAChC,IAAIC,KAAK,GAAGpB,IAAI,CAACkB,CAAC,EAAE,CAAC;QACrB,IAAIE,KAAK,GAAG,CAAC,EAAE;UACb,IAAII,EAAE,GAAGxB,IAAI,CAACkB,CAAC,EAAE,CAAC;UAClB,IAAIO,EAAE,GAAGzB,IAAI,CAACkB,CAAC,EAAE,CAAC;UAClB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;YAC9B,IAAII,EAAE,GAAG1B,IAAI,CAACkB,CAAC,EAAE,CAAC;YAClB,IAAIS,EAAE,GAAG3B,IAAI,CAACkB,CAAC,EAAE,CAAC;YAClB,IAAIxB,WAAW,CAAC4C,aAAa,CAACd,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEQ,SAAS,EAAEF,CAAC,EAAEC,CAAC,CAAC,EAAE;cAC9D,OAAOG,SAAS;YAClB;UACF;QACF;QACAA,SAAS,EAAE;MACb;IACF,CAAC,MAAM;MACL,IAAIA,SAAS,GAAG,CAAC;MACjB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,IAAI,CAACmB,MAAM,GAAG;QAChC,IAAIK,EAAE,GAAGxB,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClB,IAAIO,EAAE,GAAGzB,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClB,IAAIQ,EAAE,GAAG1B,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClB,IAAIS,EAAE,GAAG3B,IAAI,CAACkB,CAAC,EAAE,CAAC;QAClB,IAAInB,SAAS,GAAG,CAAC,EAAE;UACjB,IAAI6B,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC,GAAG,CAACD,EAAE,GAAGE,EAAE,IAAI5B,SAAS;UAC9C,IAAI8B,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC,GAAG,CAACD,EAAE,GAAGF,EAAE,IAAIzB,SAAS;UAC9C,IAAIJ,gBAAgB,CAAC2C,aAAa,CAACd,EAAE,EAAEC,EAAE,EAAEG,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEC,EAAE,EAAEQ,SAAS,EAAEF,CAAC,EAAEC,CAAC,CAAC,EAAE;YAC3E,OAAOG,SAAS;UAClB;QACF,CAAC,MAAM;UACL,IAAI3C,WAAW,CAAC4C,aAAa,CAACd,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEQ,SAAS,EAAEF,CAAC,EAAEC,CAAC,CAAC,EAAE;YAC9D,OAAOG,SAAS;UAClB;QACF;QACAA,SAAS,EAAE;MACb;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACDpC,cAAc,CAACO,SAAS,CAAC+B,OAAO,GAAG,UAAUN,CAAC,EAAEC,CAAC,EAAE;IACjD,IAAIM,QAAQ,GAAG,IAAI,CAACC,qBAAqB,CAACR,CAAC,EAAEC,CAAC,CAAC;IAC/C,IAAIQ,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACjCV,CAAC,GAAGO,QAAQ,CAAC,CAAC,CAAC;IACfN,CAAC,GAAGM,QAAQ,CAAC,CAAC,CAAC;IACf,IAAIE,IAAI,CAACH,OAAO,CAACN,CAAC,EAAEC,CAAC,CAAC,EAAE;MACtB;MACA,IAAIU,OAAO,GAAG,IAAI,CAACrC,YAAY,GAAG,IAAI,CAACyB,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;MAC1D,OAAOU,OAAO,IAAI,CAAC;IACrB;IACA,IAAI,CAACrC,YAAY,GAAG,CAAC,CAAC;IACtB,OAAO,KAAK;EACd,CAAC;EACDN,cAAc,CAACO,SAAS,CAACmC,eAAe,GAAG,YAAY;IACrD;IACA,IAAID,IAAI,GAAG,IAAI,CAACG,KAAK;IACrB,IAAI,CAACH,IAAI,EAAE;MACT,IAAIzB,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI6B,MAAM,GAAG7B,KAAK,CAACjB,IAAI;MACvB,IAAI+C,IAAI,GAAGC,QAAQ;MACnB,IAAIC,IAAI,GAAGD,QAAQ;MACnB,IAAIE,IAAI,GAAG,CAACF,QAAQ;MACpB,IAAIG,IAAI,GAAG,CAACH,QAAQ;MACpB,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,MAAM,CAAC3B,MAAM,GAAG;QAClC,IAAIc,CAAC,GAAGa,MAAM,CAAC5B,CAAC,EAAE,CAAC;QACnB,IAAIgB,CAAC,GAAGY,MAAM,CAAC5B,CAAC,EAAE,CAAC;QACnB6B,IAAI,GAAGK,IAAI,CAACC,GAAG,CAACpB,CAAC,EAAEc,IAAI,CAAC;QACxBG,IAAI,GAAGE,IAAI,CAACE,GAAG,CAACrB,CAAC,EAAEiB,IAAI,CAAC;QACxBD,IAAI,GAAGG,IAAI,CAACC,GAAG,CAACnB,CAAC,EAAEe,IAAI,CAAC;QACxBE,IAAI,GAAGC,IAAI,CAACE,GAAG,CAACpB,CAAC,EAAEiB,IAAI,CAAC;MAC1B;MACAT,IAAI,GAAG,IAAI,CAACG,KAAK,GAAG,IAAIpD,OAAO,CAAC8D,YAAY,CAACR,IAAI,EAAEE,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;IACtE;IACA,OAAOT,IAAI;EACb,CAAC;EACD,OAAOzC,cAAc;AACvB,CAAC,CAACR,OAAO,CAAC+D,IAAI,CAAC;AACf,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAI,CAACC,KAAK,GAAG,IAAIjE,OAAO,CAACkE,KAAK,CAAC,CAAC;EAClC;EACA;AACF;AACA;EACEF,aAAa,CAACjD,SAAS,CAACoD,UAAU,GAAG,UAAUC,IAAI,EAAE;IACnD,IAAI,CAACC,MAAM,CAAC,CAAC;IACb,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAC3BD,MAAM,CAACE,QAAQ,CAAC;MACdjE,IAAI,EAAE6D,IAAI,CAACK,SAAS,CAAC,aAAa;IACpC,CAAC,CAAC;IACF,IAAI,CAACC,UAAU,CAACJ,MAAM,EAAEF,IAAI,CAAC;EAC/B,CAAC;EACD;EACA;AACF;AACA;EACEJ,aAAa,CAACjD,SAAS,CAAC4D,wBAAwB,GAAG,UAAUP,IAAI,EAAE;IACjE,IAAI,CAACH,KAAK,CAACW,SAAS,CAAC,CAAC;IACtB,IAAI,CAACP,MAAM,CAAC,CAAC;EACf,CAAC;EACD;EACA;AACF;AACA;EACEL,aAAa,CAACjD,SAAS,CAAC8D,iBAAiB,GAAG,UAAUC,UAAU,EAAEV,IAAI,EAAE;IACtE,IAAIW,SAAS,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IACjC,IAAIC,UAAU,GAAGb,IAAI,CAACK,SAAS,CAAC,aAAa,CAAC;IAC9C,IAAIS,OAAO,GAAGH,SAAS,IAAIA,SAAS,CAACvD,KAAK,CAACjB,IAAI;IAC/C;IACA;IACA,IAAI2E,OAAO,IAAIA,OAAO,CAACxD,MAAM,GAAG,GAAG,EAAE;MACnC,IAAIyD,MAAM,GAAGD,OAAO,CAACxD,MAAM;MAC3B,IAAI0D,OAAO,GAAG,IAAIC,YAAY,CAACF,MAAM,GAAGF,UAAU,CAACvD,MAAM,CAAC;MAC1D;MACA0D,OAAO,CAACE,GAAG,CAACJ,OAAO,CAAC;MACpBE,OAAO,CAACE,GAAG,CAACL,UAAU,EAAEE,MAAM,CAAC;MAC/BJ,SAAS,CAACP,QAAQ,CAAC;QACjBjE,IAAI,EAAE6E;MACR,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAACJ,SAAS,GAAG,EAAE;MACnB,IAAIV,MAAM,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3BD,MAAM,CAAChC,WAAW,GAAG,IAAI;MACzBgC,MAAM,CAACE,QAAQ,CAAC;QACdjE,IAAI,EAAE0E;MACR,CAAC,CAAC;MACF,IAAI,CAACP,UAAU,CAACJ,MAAM,EAAEF,IAAI,CAAC;MAC7BE,MAAM,CAACiB,YAAY,GAAGT,UAAU,CAACU,KAAK;IACxC;EACF,CAAC;EACD;AACF;AACA;EACExB,aAAa,CAACjD,SAAS,CAAC0E,MAAM,GAAG,YAAY;IAC3C,IAAI,CAACpB,MAAM,CAAC,CAAC;EACf,CAAC;EACDL,aAAa,CAACjD,SAAS,CAAC2E,YAAY,GAAG,UAAUC,EAAE,EAAE;IACnD,IAAI,CAACX,SAAS,CAAC,CAAC,CAAC,IAAIW,EAAE,CAAC,IAAI,CAACX,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC;EACDhB,aAAa,CAACjD,SAAS,CAACwD,OAAO,GAAG,YAAY;IAC5C,IAAID,MAAM,GAAG,IAAI9D,cAAc,CAAC;MAC9BoF,MAAM,EAAE,SAAS;MACjBC,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACF,IAAI,CAACb,SAAS,CAACc,IAAI,CAACxB,MAAM,CAAC;IAC3B,IAAI,CAACL,KAAK,CAAC8B,GAAG,CAACzB,MAAM,CAAC;IACtB,OAAOA,MAAM;EACf,CAAC;EACDN,aAAa,CAACjD,SAAS,CAAC2D,UAAU,GAAG,UAAUJ,MAAM,EAAEF,IAAI,EAAE4B,aAAa,EAAE;IAC1E,IAAIC,SAAS,GAAG7B,IAAI,CAAC6B,SAAS;IAC9B3B,MAAM,CAACE,QAAQ,CAAC;MACdnE,QAAQ,EAAE4F,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACnC5F,SAAS,EAAE2F,SAAS,CAACC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC;IACrD,CAAC,CAAC;IACF5B,MAAM,CAAC6B,QAAQ,CAACF,SAAS,CAACG,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAC/D/B,MAAM,CAAC3B,KAAK,CAAC2D,aAAa,GAAG,IAAI;IACjC,IAAI3D,KAAK,GAAGyB,IAAI,CAACmC,SAAS,CAAC,OAAO,CAAC;IACnC,IAAI5D,KAAK,IAAIA,KAAK,CAACxB,MAAM,EAAE;MACzBmD,MAAM,CAACkC,QAAQ,CAAC,QAAQ,EAAE7D,KAAK,CAACxB,MAAM,CAAC;IACzC;IACAmD,MAAM,CAACkC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC;IAC7B,IAAIC,MAAM,GAAGtG,SAAS,CAACmE,MAAM,CAAC;IAC9B;IACA;IACAmC,MAAM,CAACC,WAAW,GAAGT,SAAS,CAACS,WAAW;IAC1CpC,MAAM,CAACqC,EAAE,CAAC,WAAW,EAAE,UAAUC,CAAC,EAAE;MAClCH,MAAM,CAAC7D,SAAS,GAAG,IAAI;MACvB,IAAIA,SAAS,GAAG0B,MAAM,CAACxD,YAAY;MACnC,IAAI8B,SAAS,GAAG,CAAC,EAAE;QACjB;QACA6D,MAAM,CAAC7D,SAAS,GAAGA,SAAS,GAAG0B,MAAM,CAACiB,YAAY;MACpD;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAvB,aAAa,CAACjD,SAAS,CAACsD,MAAM,GAAG,YAAY;IAC3C,IAAI,CAACW,SAAS,GAAG,EAAE;IACnB,IAAI,CAACf,KAAK,CAACW,SAAS,CAAC,CAAC;EACxB,CAAC;EACD;EACA,OAAOZ,aAAa;AACtB,CAAC,CAAC,CAAC;AACH,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}