{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as numberUtil from '../../util/number.js';\nexport default function themeRiverLayout(ecModel, api) {\n  ecModel.eachSeriesByType('themeRiver', function (seriesModel) {\n    var data = seriesModel.getData();\n    var single = seriesModel.coordinateSystem;\n    var layoutInfo = {};\n    // use the axis boundingRect for view\n    var rect = single.getRect();\n    layoutInfo.rect = rect;\n    var boundaryGap = seriesModel.get('boundaryGap');\n    var axis = single.getAxis();\n    layoutInfo.boundaryGap = boundaryGap;\n    if (axis.orient === 'horizontal') {\n      boundaryGap[0] = numberUtil.parsePercent(boundaryGap[0], rect.height);\n      boundaryGap[1] = numberUtil.parsePercent(boundaryGap[1], rect.height);\n      var height = rect.height - boundaryGap[0] - boundaryGap[1];\n      doThemeRiverLayout(data, seriesModel, height);\n    } else {\n      boundaryGap[0] = numberUtil.parsePercent(boundaryGap[0], rect.width);\n      boundaryGap[1] = numberUtil.parsePercent(boundaryGap[1], rect.width);\n      var width = rect.width - boundaryGap[0] - boundaryGap[1];\n      doThemeRiverLayout(data, seriesModel, width);\n    }\n    data.setLayout('layoutInfo', layoutInfo);\n  });\n}\n/**\r\n * The layout information about themeriver\r\n *\r\n * @param data  data in the series\r\n * @param seriesModel  the model object of themeRiver series\r\n * @param height  value used to compute every series height\r\n */\nfunction doThemeRiverLayout(data, seriesModel, height) {\n  if (!data.count()) {\n    return;\n  }\n  var coordSys = seriesModel.coordinateSystem;\n  // the data in each layer are organized into a series.\n  var layerSeries = seriesModel.getLayerSeries();\n  // the points in each layer.\n  var timeDim = data.mapDimension('single');\n  var valueDim = data.mapDimension('value');\n  var layerPoints = zrUtil.map(layerSeries, function (singleLayer) {\n    return zrUtil.map(singleLayer.indices, function (idx) {\n      var pt = coordSys.dataToPoint(data.get(timeDim, idx));\n      pt[1] = data.get(valueDim, idx);\n      return pt;\n    });\n  });\n  var base = computeBaseline(layerPoints);\n  var baseLine = base.y0;\n  var ky = height / base.max;\n  // set layout information for each item.\n  var n = layerSeries.length;\n  var m = layerSeries[0].indices.length;\n  var baseY0;\n  for (var j = 0; j < m; ++j) {\n    baseY0 = baseLine[j] * ky;\n    data.setItemLayout(layerSeries[0].indices[j], {\n      layerIndex: 0,\n      x: layerPoints[0][j][0],\n      y0: baseY0,\n      y: layerPoints[0][j][1] * ky\n    });\n    for (var i = 1; i < n; ++i) {\n      baseY0 += layerPoints[i - 1][j][1] * ky;\n      data.setItemLayout(layerSeries[i].indices[j], {\n        layerIndex: i,\n        x: layerPoints[i][j][0],\n        y0: baseY0,\n        y: layerPoints[i][j][1] * ky\n      });\n    }\n  }\n}\n/**\r\n * Compute the baseLine of the rawdata\r\n * Inspired by Lee Byron's paper Stacked Graphs - Geometry & Aesthetics\r\n *\r\n * @param  data  the points in each layer\r\n */\nfunction computeBaseline(data) {\n  var layerNum = data.length;\n  var pointNum = data[0].length;\n  var sums = [];\n  var y0 = [];\n  var max = 0;\n  for (var i = 0; i < pointNum; ++i) {\n    var temp = 0;\n    for (var j = 0; j < layerNum; ++j) {\n      temp += data[j][i][1];\n    }\n    if (temp > max) {\n      max = temp;\n    }\n    sums.push(temp);\n  }\n  for (var k = 0; k < pointNum; ++k) {\n    y0[k] = (max - sums[k]) / 2;\n  }\n  max = 0;\n  for (var l = 0; l < pointNum; ++l) {\n    var sum = sums[l] + y0[l];\n    if (sum > max) {\n      max = sum;\n    }\n  }\n  return {\n    y0: y0,\n    max: max\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "numberUtil", "themeRiverLayout", "ecModel", "api", "eachSeriesByType", "seriesModel", "data", "getData", "single", "coordinateSystem", "layoutInfo", "rect", "getRect", "boundaryGap", "get", "axis", "getAxis", "orient", "parsePercent", "height", "doThemeRiverLayout", "width", "setLayout", "count", "coordSys", "layerSeries", "getLayerSeries", "timeDim", "mapDimension", "valueDim", "layerPoints", "map", "singleLayer", "indices", "idx", "pt", "dataToPoint", "base", "computeBaseline", "baseLine", "y0", "ky", "max", "n", "length", "m", "baseY0", "j", "setItemLayout", "layerIndex", "x", "y", "i", "layerNum", "pointNum", "sums", "temp", "push", "k", "l", "sum"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/themeRiver/themeRiverLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as numberUtil from '../../util/number.js';\nexport default function themeRiverLayout(ecModel, api) {\n  ecModel.eachSeriesByType('themeRiver', function (seriesModel) {\n    var data = seriesModel.getData();\n    var single = seriesModel.coordinateSystem;\n    var layoutInfo = {};\n    // use the axis boundingRect for view\n    var rect = single.getRect();\n    layoutInfo.rect = rect;\n    var boundaryGap = seriesModel.get('boundaryGap');\n    var axis = single.getAxis();\n    layoutInfo.boundaryGap = boundaryGap;\n    if (axis.orient === 'horizontal') {\n      boundaryGap[0] = numberUtil.parsePercent(boundaryGap[0], rect.height);\n      boundaryGap[1] = numberUtil.parsePercent(boundaryGap[1], rect.height);\n      var height = rect.height - boundaryGap[0] - boundaryGap[1];\n      doThemeRiverLayout(data, seriesModel, height);\n    } else {\n      boundaryGap[0] = numberUtil.parsePercent(boundaryGap[0], rect.width);\n      boundaryGap[1] = numberUtil.parsePercent(boundaryGap[1], rect.width);\n      var width = rect.width - boundaryGap[0] - boundaryGap[1];\n      doThemeRiverLayout(data, seriesModel, width);\n    }\n    data.setLayout('layoutInfo', layoutInfo);\n  });\n}\n/**\r\n * The layout information about themeriver\r\n *\r\n * @param data  data in the series\r\n * @param seriesModel  the model object of themeRiver series\r\n * @param height  value used to compute every series height\r\n */\nfunction doThemeRiverLayout(data, seriesModel, height) {\n  if (!data.count()) {\n    return;\n  }\n  var coordSys = seriesModel.coordinateSystem;\n  // the data in each layer are organized into a series.\n  var layerSeries = seriesModel.getLayerSeries();\n  // the points in each layer.\n  var timeDim = data.mapDimension('single');\n  var valueDim = data.mapDimension('value');\n  var layerPoints = zrUtil.map(layerSeries, function (singleLayer) {\n    return zrUtil.map(singleLayer.indices, function (idx) {\n      var pt = coordSys.dataToPoint(data.get(timeDim, idx));\n      pt[1] = data.get(valueDim, idx);\n      return pt;\n    });\n  });\n  var base = computeBaseline(layerPoints);\n  var baseLine = base.y0;\n  var ky = height / base.max;\n  // set layout information for each item.\n  var n = layerSeries.length;\n  var m = layerSeries[0].indices.length;\n  var baseY0;\n  for (var j = 0; j < m; ++j) {\n    baseY0 = baseLine[j] * ky;\n    data.setItemLayout(layerSeries[0].indices[j], {\n      layerIndex: 0,\n      x: layerPoints[0][j][0],\n      y0: baseY0,\n      y: layerPoints[0][j][1] * ky\n    });\n    for (var i = 1; i < n; ++i) {\n      baseY0 += layerPoints[i - 1][j][1] * ky;\n      data.setItemLayout(layerSeries[i].indices[j], {\n        layerIndex: i,\n        x: layerPoints[i][j][0],\n        y0: baseY0,\n        y: layerPoints[i][j][1] * ky\n      });\n    }\n  }\n}\n/**\r\n * Compute the baseLine of the rawdata\r\n * Inspired by Lee Byron's paper Stacked Graphs - Geometry & Aesthetics\r\n *\r\n * @param  data  the points in each layer\r\n */\nfunction computeBaseline(data) {\n  var layerNum = data.length;\n  var pointNum = data[0].length;\n  var sums = [];\n  var y0 = [];\n  var max = 0;\n  for (var i = 0; i < pointNum; ++i) {\n    var temp = 0;\n    for (var j = 0; j < layerNum; ++j) {\n      temp += data[j][i][1];\n    }\n    if (temp > max) {\n      max = temp;\n    }\n    sums.push(temp);\n  }\n  for (var k = 0; k < pointNum; ++k) {\n    y0[k] = (max - sums[k]) / 2;\n  }\n  max = 0;\n  for (var l = 0; l < pointNum; ++l) {\n    var sum = sums[l] + y0[l];\n    if (sum > max) {\n      max = sum;\n    }\n  }\n  return {\n    y0: y0,\n    max: max\n  };\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,GAAG,EAAE;EACrDD,OAAO,CAACE,gBAAgB,CAAC,YAAY,EAAE,UAAUC,WAAW,EAAE;IAC5D,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;IAChC,IAAIC,MAAM,GAAGH,WAAW,CAACI,gBAAgB;IACzC,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB;IACA,IAAIC,IAAI,GAAGH,MAAM,CAACI,OAAO,CAAC,CAAC;IAC3BF,UAAU,CAACC,IAAI,GAAGA,IAAI;IACtB,IAAIE,WAAW,GAAGR,WAAW,CAACS,GAAG,CAAC,aAAa,CAAC;IAChD,IAAIC,IAAI,GAAGP,MAAM,CAACQ,OAAO,CAAC,CAAC;IAC3BN,UAAU,CAACG,WAAW,GAAGA,WAAW;IACpC,IAAIE,IAAI,CAACE,MAAM,KAAK,YAAY,EAAE;MAChCJ,WAAW,CAAC,CAAC,CAAC,GAAGb,UAAU,CAACkB,YAAY,CAACL,WAAW,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACQ,MAAM,CAAC;MACrEN,WAAW,CAAC,CAAC,CAAC,GAAGb,UAAU,CAACkB,YAAY,CAACL,WAAW,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACQ,MAAM,CAAC;MACrE,IAAIA,MAAM,GAAGR,IAAI,CAACQ,MAAM,GAAGN,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;MAC1DO,kBAAkB,CAACd,IAAI,EAAED,WAAW,EAAEc,MAAM,CAAC;IAC/C,CAAC,MAAM;MACLN,WAAW,CAAC,CAAC,CAAC,GAAGb,UAAU,CAACkB,YAAY,CAACL,WAAW,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACU,KAAK,CAAC;MACpER,WAAW,CAAC,CAAC,CAAC,GAAGb,UAAU,CAACkB,YAAY,CAACL,WAAW,CAAC,CAAC,CAAC,EAAEF,IAAI,CAACU,KAAK,CAAC;MACpE,IAAIA,KAAK,GAAGV,IAAI,CAACU,KAAK,GAAGR,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;MACxDO,kBAAkB,CAACd,IAAI,EAAED,WAAW,EAAEgB,KAAK,CAAC;IAC9C;IACAf,IAAI,CAACgB,SAAS,CAAC,YAAY,EAAEZ,UAAU,CAAC;EAC1C,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,kBAAkBA,CAACd,IAAI,EAAED,WAAW,EAAEc,MAAM,EAAE;EACrD,IAAI,CAACb,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAE;IACjB;EACF;EACA,IAAIC,QAAQ,GAAGnB,WAAW,CAACI,gBAAgB;EAC3C;EACA,IAAIgB,WAAW,GAAGpB,WAAW,CAACqB,cAAc,CAAC,CAAC;EAC9C;EACA,IAAIC,OAAO,GAAGrB,IAAI,CAACsB,YAAY,CAAC,QAAQ,CAAC;EACzC,IAAIC,QAAQ,GAAGvB,IAAI,CAACsB,YAAY,CAAC,OAAO,CAAC;EACzC,IAAIE,WAAW,GAAG/B,MAAM,CAACgC,GAAG,CAACN,WAAW,EAAE,UAAUO,WAAW,EAAE;IAC/D,OAAOjC,MAAM,CAACgC,GAAG,CAACC,WAAW,CAACC,OAAO,EAAE,UAAUC,GAAG,EAAE;MACpD,IAAIC,EAAE,GAAGX,QAAQ,CAACY,WAAW,CAAC9B,IAAI,CAACQ,GAAG,CAACa,OAAO,EAAEO,GAAG,CAAC,CAAC;MACrDC,EAAE,CAAC,CAAC,CAAC,GAAG7B,IAAI,CAACQ,GAAG,CAACe,QAAQ,EAAEK,GAAG,CAAC;MAC/B,OAAOC,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIE,IAAI,GAAGC,eAAe,CAACR,WAAW,CAAC;EACvC,IAAIS,QAAQ,GAAGF,IAAI,CAACG,EAAE;EACtB,IAAIC,EAAE,GAAGtB,MAAM,GAAGkB,IAAI,CAACK,GAAG;EAC1B;EACA,IAAIC,CAAC,GAAGlB,WAAW,CAACmB,MAAM;EAC1B,IAAIC,CAAC,GAAGpB,WAAW,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACW,MAAM;EACrC,IAAIE,MAAM;EACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IAC1BD,MAAM,GAAGP,QAAQ,CAACQ,CAAC,CAAC,GAAGN,EAAE;IACzBnC,IAAI,CAAC0C,aAAa,CAACvB,WAAW,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACc,CAAC,CAAC,EAAE;MAC5CE,UAAU,EAAE,CAAC;MACbC,CAAC,EAAEpB,WAAW,CAAC,CAAC,CAAC,CAACiB,CAAC,CAAC,CAAC,CAAC,CAAC;MACvBP,EAAE,EAAEM,MAAM;MACVK,CAAC,EAAErB,WAAW,CAAC,CAAC,CAAC,CAACiB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN;IAC5B,CAAC,CAAC;IACF,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,CAAC,EAAE,EAAES,CAAC,EAAE;MAC1BN,MAAM,IAAIhB,WAAW,CAACsB,CAAC,GAAG,CAAC,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,EAAE;MACvCnC,IAAI,CAAC0C,aAAa,CAACvB,WAAW,CAAC2B,CAAC,CAAC,CAACnB,OAAO,CAACc,CAAC,CAAC,EAAE;QAC5CE,UAAU,EAAEG,CAAC;QACbF,CAAC,EAAEpB,WAAW,CAACsB,CAAC,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC;QACvBP,EAAE,EAAEM,MAAM;QACVK,CAAC,EAAErB,WAAW,CAACsB,CAAC,CAAC,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN;MAC5B,CAAC,CAAC;IACJ;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,eAAeA,CAAChC,IAAI,EAAE;EAC7B,IAAI+C,QAAQ,GAAG/C,IAAI,CAACsC,MAAM;EAC1B,IAAIU,QAAQ,GAAGhD,IAAI,CAAC,CAAC,CAAC,CAACsC,MAAM;EAC7B,IAAIW,IAAI,GAAG,EAAE;EACb,IAAIf,EAAE,GAAG,EAAE;EACX,IAAIE,GAAG,GAAG,CAAC;EACX,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,QAAQ,EAAE,EAAEF,CAAC,EAAE;IACjC,IAAII,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,QAAQ,EAAE,EAAEN,CAAC,EAAE;MACjCS,IAAI,IAAIlD,IAAI,CAACyC,CAAC,CAAC,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,IAAII,IAAI,GAAGd,GAAG,EAAE;MACdA,GAAG,GAAGc,IAAI;IACZ;IACAD,IAAI,CAACE,IAAI,CAACD,IAAI,CAAC;EACjB;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,EAAE,EAAEI,CAAC,EAAE;IACjClB,EAAE,CAACkB,CAAC,CAAC,GAAG,CAAChB,GAAG,GAAGa,IAAI,CAACG,CAAC,CAAC,IAAI,CAAC;EAC7B;EACAhB,GAAG,GAAG,CAAC;EACP,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,EAAE,EAAEK,CAAC,EAAE;IACjC,IAAIC,GAAG,GAAGL,IAAI,CAACI,CAAC,CAAC,GAAGnB,EAAE,CAACmB,CAAC,CAAC;IACzB,IAAIC,GAAG,GAAGlB,GAAG,EAAE;MACbA,GAAG,GAAGkB,GAAG;IACX;EACF;EACA,OAAO;IACLpB,EAAE,EAAEA,EAAE;IACNE,GAAG,EAAEA;EACP,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}