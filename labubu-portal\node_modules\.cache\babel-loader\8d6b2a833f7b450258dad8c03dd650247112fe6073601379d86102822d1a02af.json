{"ast": null, "code": "import * as vec2 from './vector.js';\nimport * as curve from './curve.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI2 = Math.PI * 2;\nvar start = vec2.create();\nvar end = vec2.create();\nvar extremity = vec2.create();\nexport function fromPoints(points, min, max) {\n  if (points.length === 0) {\n    return;\n  }\n  var p = points[0];\n  var left = p[0];\n  var right = p[0];\n  var top = p[1];\n  var bottom = p[1];\n  for (var i = 1; i < points.length; i++) {\n    p = points[i];\n    left = mathMin(left, p[0]);\n    right = mathMax(right, p[0]);\n    top = mathMin(top, p[1]);\n    bottom = mathMax(bottom, p[1]);\n  }\n  min[0] = left;\n  min[1] = top;\n  max[0] = right;\n  max[1] = bottom;\n}\nexport function fromLine(x0, y0, x1, y1, min, max) {\n  min[0] = mathMin(x0, x1);\n  min[1] = mathMin(y0, y1);\n  max[0] = mathMax(x0, x1);\n  max[1] = mathMax(y0, y1);\n}\nvar xDim = [];\nvar yDim = [];\nexport function fromCubic(x0, y0, x1, y1, x2, y2, x3, y3, min, max) {\n  var cubicExtrema = curve.cubicExtrema;\n  var cubicAt = curve.cubicAt;\n  var n = cubicExtrema(x0, x1, x2, x3, xDim);\n  min[0] = Infinity;\n  min[1] = Infinity;\n  max[0] = -Infinity;\n  max[1] = -Infinity;\n  for (var i = 0; i < n; i++) {\n    var x = cubicAt(x0, x1, x2, x3, xDim[i]);\n    min[0] = mathMin(x, min[0]);\n    max[0] = mathMax(x, max[0]);\n  }\n  n = cubicExtrema(y0, y1, y2, y3, yDim);\n  for (var i = 0; i < n; i++) {\n    var y = cubicAt(y0, y1, y2, y3, yDim[i]);\n    min[1] = mathMin(y, min[1]);\n    max[1] = mathMax(y, max[1]);\n  }\n  min[0] = mathMin(x0, min[0]);\n  max[0] = mathMax(x0, max[0]);\n  min[0] = mathMin(x3, min[0]);\n  max[0] = mathMax(x3, max[0]);\n  min[1] = mathMin(y0, min[1]);\n  max[1] = mathMax(y0, max[1]);\n  min[1] = mathMin(y3, min[1]);\n  max[1] = mathMax(y3, max[1]);\n}\nexport function fromQuadratic(x0, y0, x1, y1, x2, y2, min, max) {\n  var quadraticExtremum = curve.quadraticExtremum;\n  var quadraticAt = curve.quadraticAt;\n  var tx = mathMax(mathMin(quadraticExtremum(x0, x1, x2), 1), 0);\n  var ty = mathMax(mathMin(quadraticExtremum(y0, y1, y2), 1), 0);\n  var x = quadraticAt(x0, x1, x2, tx);\n  var y = quadraticAt(y0, y1, y2, ty);\n  min[0] = mathMin(x0, x2, x);\n  min[1] = mathMin(y0, y2, y);\n  max[0] = mathMax(x0, x2, x);\n  max[1] = mathMax(y0, y2, y);\n}\nexport function fromArc(x, y, rx, ry, startAngle, endAngle, anticlockwise, min, max) {\n  var vec2Min = vec2.min;\n  var vec2Max = vec2.max;\n  var diff = Math.abs(startAngle - endAngle);\n  if (diff % PI2 < 1e-4 && diff > 1e-4) {\n    min[0] = x - rx;\n    min[1] = y - ry;\n    max[0] = x + rx;\n    max[1] = y + ry;\n    return;\n  }\n  start[0] = mathCos(startAngle) * rx + x;\n  start[1] = mathSin(startAngle) * ry + y;\n  end[0] = mathCos(endAngle) * rx + x;\n  end[1] = mathSin(endAngle) * ry + y;\n  vec2Min(min, start, end);\n  vec2Max(max, start, end);\n  startAngle = startAngle % PI2;\n  if (startAngle < 0) {\n    startAngle = startAngle + PI2;\n  }\n  endAngle = endAngle % PI2;\n  if (endAngle < 0) {\n    endAngle = endAngle + PI2;\n  }\n  if (startAngle > endAngle && !anticlockwise) {\n    endAngle += PI2;\n  } else if (startAngle < endAngle && anticlockwise) {\n    startAngle += PI2;\n  }\n  if (anticlockwise) {\n    var tmp = endAngle;\n    endAngle = startAngle;\n    startAngle = tmp;\n  }\n  for (var angle = 0; angle < endAngle; angle += Math.PI / 2) {\n    if (angle > startAngle) {\n      extremity[0] = mathCos(angle) * rx + x;\n      extremity[1] = mathSin(angle) * ry + y;\n      vec2Min(min, extremity, min);\n      vec2Max(max, extremity, max);\n    }\n  }\n}", "map": {"version": 3, "names": ["vec2", "curve", "mathMin", "Math", "min", "mathMax", "max", "mathSin", "sin", "mathCos", "cos", "PI2", "PI", "start", "create", "end", "extremity", "fromPoints", "points", "length", "p", "left", "right", "top", "bottom", "i", "fromLine", "x0", "y0", "x1", "y1", "xDim", "yDim", "fromCubic", "x2", "y2", "x3", "y3", "cubicExtrema", "cubicAt", "n", "Infinity", "x", "y", "fromQuadratic", "quadraticExtremum", "quadraticAt", "tx", "ty", "fromArc", "rx", "ry", "startAngle", "endAngle", "anticlockwise", "vec2Min", "vec2Max", "diff", "abs", "tmp", "angle"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/zrender/lib/core/bbox.js"], "sourcesContent": ["import * as vec2 from './vector.js';\nimport * as curve from './curve.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nvar mathSin = Math.sin;\nvar mathCos = Math.cos;\nvar PI2 = Math.PI * 2;\nvar start = vec2.create();\nvar end = vec2.create();\nvar extremity = vec2.create();\nexport function fromPoints(points, min, max) {\n    if (points.length === 0) {\n        return;\n    }\n    var p = points[0];\n    var left = p[0];\n    var right = p[0];\n    var top = p[1];\n    var bottom = p[1];\n    for (var i = 1; i < points.length; i++) {\n        p = points[i];\n        left = mathMin(left, p[0]);\n        right = mathMax(right, p[0]);\n        top = mathMin(top, p[1]);\n        bottom = mathMax(bottom, p[1]);\n    }\n    min[0] = left;\n    min[1] = top;\n    max[0] = right;\n    max[1] = bottom;\n}\nexport function fromLine(x0, y0, x1, y1, min, max) {\n    min[0] = mathMin(x0, x1);\n    min[1] = mathMin(y0, y1);\n    max[0] = mathMax(x0, x1);\n    max[1] = mathMax(y0, y1);\n}\nvar xDim = [];\nvar yDim = [];\nexport function fromCubic(x0, y0, x1, y1, x2, y2, x3, y3, min, max) {\n    var cubicExtrema = curve.cubicExtrema;\n    var cubicAt = curve.cubicAt;\n    var n = cubicExtrema(x0, x1, x2, x3, xDim);\n    min[0] = Infinity;\n    min[1] = Infinity;\n    max[0] = -Infinity;\n    max[1] = -Infinity;\n    for (var i = 0; i < n; i++) {\n        var x = cubicAt(x0, x1, x2, x3, xDim[i]);\n        min[0] = mathMin(x, min[0]);\n        max[0] = mathMax(x, max[0]);\n    }\n    n = cubicExtrema(y0, y1, y2, y3, yDim);\n    for (var i = 0; i < n; i++) {\n        var y = cubicAt(y0, y1, y2, y3, yDim[i]);\n        min[1] = mathMin(y, min[1]);\n        max[1] = mathMax(y, max[1]);\n    }\n    min[0] = mathMin(x0, min[0]);\n    max[0] = mathMax(x0, max[0]);\n    min[0] = mathMin(x3, min[0]);\n    max[0] = mathMax(x3, max[0]);\n    min[1] = mathMin(y0, min[1]);\n    max[1] = mathMax(y0, max[1]);\n    min[1] = mathMin(y3, min[1]);\n    max[1] = mathMax(y3, max[1]);\n}\nexport function fromQuadratic(x0, y0, x1, y1, x2, y2, min, max) {\n    var quadraticExtremum = curve.quadraticExtremum;\n    var quadraticAt = curve.quadraticAt;\n    var tx = mathMax(mathMin(quadraticExtremum(x0, x1, x2), 1), 0);\n    var ty = mathMax(mathMin(quadraticExtremum(y0, y1, y2), 1), 0);\n    var x = quadraticAt(x0, x1, x2, tx);\n    var y = quadraticAt(y0, y1, y2, ty);\n    min[0] = mathMin(x0, x2, x);\n    min[1] = mathMin(y0, y2, y);\n    max[0] = mathMax(x0, x2, x);\n    max[1] = mathMax(y0, y2, y);\n}\nexport function fromArc(x, y, rx, ry, startAngle, endAngle, anticlockwise, min, max) {\n    var vec2Min = vec2.min;\n    var vec2Max = vec2.max;\n    var diff = Math.abs(startAngle - endAngle);\n    if (diff % PI2 < 1e-4 && diff > 1e-4) {\n        min[0] = x - rx;\n        min[1] = y - ry;\n        max[0] = x + rx;\n        max[1] = y + ry;\n        return;\n    }\n    start[0] = mathCos(startAngle) * rx + x;\n    start[1] = mathSin(startAngle) * ry + y;\n    end[0] = mathCos(endAngle) * rx + x;\n    end[1] = mathSin(endAngle) * ry + y;\n    vec2Min(min, start, end);\n    vec2Max(max, start, end);\n    startAngle = startAngle % (PI2);\n    if (startAngle < 0) {\n        startAngle = startAngle + PI2;\n    }\n    endAngle = endAngle % (PI2);\n    if (endAngle < 0) {\n        endAngle = endAngle + PI2;\n    }\n    if (startAngle > endAngle && !anticlockwise) {\n        endAngle += PI2;\n    }\n    else if (startAngle < endAngle && anticlockwise) {\n        startAngle += PI2;\n    }\n    if (anticlockwise) {\n        var tmp = endAngle;\n        endAngle = startAngle;\n        startAngle = tmp;\n    }\n    for (var angle = 0; angle < endAngle; angle += Math.PI / 2) {\n        if (angle > startAngle) {\n            extremity[0] = mathCos(angle) * rx + x;\n            extremity[1] = mathSin(angle) * ry + y;\n            vec2Min(min, extremity, min);\n            vec2Max(max, extremity, max);\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,aAAa;AACnC,OAAO,KAAKC,KAAK,MAAM,YAAY;AACnC,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,IAAIC,OAAO,GAAGJ,IAAI,CAACK,GAAG;AACtB,IAAIC,OAAO,GAAGN,IAAI,CAACO,GAAG;AACtB,IAAIC,GAAG,GAAGR,IAAI,CAACS,EAAE,GAAG,CAAC;AACrB,IAAIC,KAAK,GAAGb,IAAI,CAACc,MAAM,CAAC,CAAC;AACzB,IAAIC,GAAG,GAAGf,IAAI,CAACc,MAAM,CAAC,CAAC;AACvB,IAAIE,SAAS,GAAGhB,IAAI,CAACc,MAAM,CAAC,CAAC;AAC7B,OAAO,SAASG,UAAUA,CAACC,MAAM,EAAEd,GAAG,EAAEE,GAAG,EAAE;EACzC,IAAIY,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;IACrB;EACJ;EACA,IAAIC,CAAC,GAAGF,MAAM,CAAC,CAAC,CAAC;EACjB,IAAIG,IAAI,GAAGD,CAAC,CAAC,CAAC,CAAC;EACf,IAAIE,KAAK,GAAGF,CAAC,CAAC,CAAC,CAAC;EAChB,IAAIG,GAAG,GAAGH,CAAC,CAAC,CAAC,CAAC;EACd,IAAII,MAAM,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACjB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACC,MAAM,EAAEM,CAAC,EAAE,EAAE;IACpCL,CAAC,GAAGF,MAAM,CAACO,CAAC,CAAC;IACbJ,IAAI,GAAGnB,OAAO,CAACmB,IAAI,EAAED,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1BE,KAAK,GAAGjB,OAAO,CAACiB,KAAK,EAAEF,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5BG,GAAG,GAAGrB,OAAO,CAACqB,GAAG,EAAEH,CAAC,CAAC,CAAC,CAAC,CAAC;IACxBI,MAAM,GAAGnB,OAAO,CAACmB,MAAM,EAAEJ,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC;EACAhB,GAAG,CAAC,CAAC,CAAC,GAAGiB,IAAI;EACbjB,GAAG,CAAC,CAAC,CAAC,GAAGmB,GAAG;EACZjB,GAAG,CAAC,CAAC,CAAC,GAAGgB,KAAK;EACdhB,GAAG,CAAC,CAAC,CAAC,GAAGkB,MAAM;AACnB;AACA,OAAO,SAASE,QAAQA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE1B,GAAG,EAAEE,GAAG,EAAE;EAC/CF,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACyB,EAAE,EAAEE,EAAE,CAAC;EACxBzB,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAAC0B,EAAE,EAAEE,EAAE,CAAC;EACxBxB,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACsB,EAAE,EAAEE,EAAE,CAAC;EACxBvB,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACuB,EAAE,EAAEE,EAAE,CAAC;AAC5B;AACA,IAAIC,IAAI,GAAG,EAAE;AACb,IAAIC,IAAI,GAAG,EAAE;AACb,OAAO,SAASC,SAASA,CAACN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEjC,GAAG,EAAEE,GAAG,EAAE;EAChE,IAAIgC,YAAY,GAAGrC,KAAK,CAACqC,YAAY;EACrC,IAAIC,OAAO,GAAGtC,KAAK,CAACsC,OAAO;EAC3B,IAAIC,CAAC,GAAGF,YAAY,CAACX,EAAE,EAAEE,EAAE,EAAEK,EAAE,EAAEE,EAAE,EAAEL,IAAI,CAAC;EAC1C3B,GAAG,CAAC,CAAC,CAAC,GAAGqC,QAAQ;EACjBrC,GAAG,CAAC,CAAC,CAAC,GAAGqC,QAAQ;EACjBnC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACmC,QAAQ;EAClBnC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACmC,QAAQ;EAClB,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,CAAC,EAAEf,CAAC,EAAE,EAAE;IACxB,IAAIiB,CAAC,GAAGH,OAAO,CAACZ,EAAE,EAAEE,EAAE,EAAEK,EAAE,EAAEE,EAAE,EAAEL,IAAI,CAACN,CAAC,CAAC,CAAC;IACxCrB,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACwC,CAAC,EAAEtC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3BE,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACqC,CAAC,EAAEpC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/B;EACAkC,CAAC,GAAGF,YAAY,CAACV,EAAE,EAAEE,EAAE,EAAEK,EAAE,EAAEE,EAAE,EAAEL,IAAI,CAAC;EACtC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,CAAC,EAAEf,CAAC,EAAE,EAAE;IACxB,IAAIkB,CAAC,GAAGJ,OAAO,CAACX,EAAE,EAAEE,EAAE,EAAEK,EAAE,EAAEE,EAAE,EAAEL,IAAI,CAACP,CAAC,CAAC,CAAC;IACxCrB,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACyC,CAAC,EAAEvC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3BE,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACsC,CAAC,EAAErC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC/B;EACAF,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACyB,EAAE,EAAEvB,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5BE,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACsB,EAAE,EAAErB,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5BF,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACkC,EAAE,EAAEhC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5BE,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAAC+B,EAAE,EAAE9B,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5BF,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAAC0B,EAAE,EAAExB,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5BE,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACuB,EAAE,EAAEtB,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5BF,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACmC,EAAE,EAAEjC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5BE,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACgC,EAAE,EAAE/B,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,OAAO,SAASsC,aAAaA,CAACjB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEI,EAAE,EAAEC,EAAE,EAAE/B,GAAG,EAAEE,GAAG,EAAE;EAC5D,IAAIuC,iBAAiB,GAAG5C,KAAK,CAAC4C,iBAAiB;EAC/C,IAAIC,WAAW,GAAG7C,KAAK,CAAC6C,WAAW;EACnC,IAAIC,EAAE,GAAG1C,OAAO,CAACH,OAAO,CAAC2C,iBAAiB,CAAClB,EAAE,EAAEE,EAAE,EAAEK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9D,IAAIc,EAAE,GAAG3C,OAAO,CAACH,OAAO,CAAC2C,iBAAiB,CAACjB,EAAE,EAAEE,EAAE,EAAEK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9D,IAAIO,CAAC,GAAGI,WAAW,CAACnB,EAAE,EAAEE,EAAE,EAAEK,EAAE,EAAEa,EAAE,CAAC;EACnC,IAAIJ,CAAC,GAAGG,WAAW,CAAClB,EAAE,EAAEE,EAAE,EAAEK,EAAE,EAAEa,EAAE,CAAC;EACnC5C,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACyB,EAAE,EAAEO,EAAE,EAAEQ,CAAC,CAAC;EAC3BtC,GAAG,CAAC,CAAC,CAAC,GAAGF,OAAO,CAAC0B,EAAE,EAAEO,EAAE,EAAEQ,CAAC,CAAC;EAC3BrC,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACsB,EAAE,EAAEO,EAAE,EAAEQ,CAAC,CAAC;EAC3BpC,GAAG,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACuB,EAAE,EAAEO,EAAE,EAAEQ,CAAC,CAAC;AAC/B;AACA,OAAO,SAASM,OAAOA,CAACP,CAAC,EAAEC,CAAC,EAAEO,EAAE,EAAEC,EAAE,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAElD,GAAG,EAAEE,GAAG,EAAE;EACjF,IAAIiD,OAAO,GAAGvD,IAAI,CAACI,GAAG;EACtB,IAAIoD,OAAO,GAAGxD,IAAI,CAACM,GAAG;EACtB,IAAImD,IAAI,GAAGtD,IAAI,CAACuD,GAAG,CAACN,UAAU,GAAGC,QAAQ,CAAC;EAC1C,IAAII,IAAI,GAAG9C,GAAG,GAAG,IAAI,IAAI8C,IAAI,GAAG,IAAI,EAAE;IAClCrD,GAAG,CAAC,CAAC,CAAC,GAAGsC,CAAC,GAAGQ,EAAE;IACf9C,GAAG,CAAC,CAAC,CAAC,GAAGuC,CAAC,GAAGQ,EAAE;IACf7C,GAAG,CAAC,CAAC,CAAC,GAAGoC,CAAC,GAAGQ,EAAE;IACf5C,GAAG,CAAC,CAAC,CAAC,GAAGqC,CAAC,GAAGQ,EAAE;IACf;EACJ;EACAtC,KAAK,CAAC,CAAC,CAAC,GAAGJ,OAAO,CAAC2C,UAAU,CAAC,GAAGF,EAAE,GAAGR,CAAC;EACvC7B,KAAK,CAAC,CAAC,CAAC,GAAGN,OAAO,CAAC6C,UAAU,CAAC,GAAGD,EAAE,GAAGR,CAAC;EACvC5B,GAAG,CAAC,CAAC,CAAC,GAAGN,OAAO,CAAC4C,QAAQ,CAAC,GAAGH,EAAE,GAAGR,CAAC;EACnC3B,GAAG,CAAC,CAAC,CAAC,GAAGR,OAAO,CAAC8C,QAAQ,CAAC,GAAGF,EAAE,GAAGR,CAAC;EACnCY,OAAO,CAACnD,GAAG,EAAES,KAAK,EAAEE,GAAG,CAAC;EACxByC,OAAO,CAAClD,GAAG,EAAEO,KAAK,EAAEE,GAAG,CAAC;EACxBqC,UAAU,GAAGA,UAAU,GAAIzC,GAAI;EAC/B,IAAIyC,UAAU,GAAG,CAAC,EAAE;IAChBA,UAAU,GAAGA,UAAU,GAAGzC,GAAG;EACjC;EACA0C,QAAQ,GAAGA,QAAQ,GAAI1C,GAAI;EAC3B,IAAI0C,QAAQ,GAAG,CAAC,EAAE;IACdA,QAAQ,GAAGA,QAAQ,GAAG1C,GAAG;EAC7B;EACA,IAAIyC,UAAU,GAAGC,QAAQ,IAAI,CAACC,aAAa,EAAE;IACzCD,QAAQ,IAAI1C,GAAG;EACnB,CAAC,MACI,IAAIyC,UAAU,GAAGC,QAAQ,IAAIC,aAAa,EAAE;IAC7CF,UAAU,IAAIzC,GAAG;EACrB;EACA,IAAI2C,aAAa,EAAE;IACf,IAAIK,GAAG,GAAGN,QAAQ;IAClBA,QAAQ,GAAGD,UAAU;IACrBA,UAAU,GAAGO,GAAG;EACpB;EACA,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,QAAQ,EAAEO,KAAK,IAAIzD,IAAI,CAACS,EAAE,GAAG,CAAC,EAAE;IACxD,IAAIgD,KAAK,GAAGR,UAAU,EAAE;MACpBpC,SAAS,CAAC,CAAC,CAAC,GAAGP,OAAO,CAACmD,KAAK,CAAC,GAAGV,EAAE,GAAGR,CAAC;MACtC1B,SAAS,CAAC,CAAC,CAAC,GAAGT,OAAO,CAACqD,KAAK,CAAC,GAAGT,EAAE,GAAGR,CAAC;MACtCY,OAAO,CAACnD,GAAG,EAAEY,SAAS,EAAEZ,GAAG,CAAC;MAC5BoD,OAAO,CAAClD,GAAG,EAAEU,SAAS,EAAEV,GAAG,CAAC;IAChC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}