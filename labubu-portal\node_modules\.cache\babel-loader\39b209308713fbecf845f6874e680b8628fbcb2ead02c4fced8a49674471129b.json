{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/web.dom-exception.stack.js\";\nimport { RADIAN_TO_DEGREE, retrieve2, logError, isFunction } from '../core/util.js';\nimport { parse } from '../tool/color.js';\nimport env from '../core/env.js';\nvar mathRound = Math.round;\nexport function normalizeColor(color) {\n  var opacity;\n  if (!color || color === 'transparent') {\n    color = 'none';\n  } else if (typeof color === 'string' && color.indexOf('rgba') > -1) {\n    var arr = parse(color);\n    if (arr) {\n      color = 'rgb(' + arr[0] + ',' + arr[1] + ',' + arr[2] + ')';\n      opacity = arr[3];\n    }\n  }\n  return {\n    color: color,\n    opacity: opacity == null ? 1 : opacity\n  };\n}\nvar EPSILON = 1e-4;\nexport function isAroundZero(transform) {\n  return transform < EPSILON && transform > -EPSILON;\n}\nexport function round3(transform) {\n  return mathRound(transform * 1e3) / 1e3;\n}\nexport function round4(transform) {\n  return mathRound(transform * 1e4) / 1e4;\n}\nexport function round1(transform) {\n  return mathRound(transform * 10) / 10;\n}\nexport function getMatrixStr(m) {\n  return 'matrix(' + round3(m[0]) + ',' + round3(m[1]) + ',' + round3(m[2]) + ',' + round3(m[3]) + ',' + round4(m[4]) + ',' + round4(m[5]) + ')';\n}\nexport var TEXT_ALIGN_TO_ANCHOR = {\n  left: 'start',\n  right: 'end',\n  center: 'middle',\n  middle: 'middle'\n};\nexport function adjustTextY(y, lineHeight, textBaseline) {\n  if (textBaseline === 'top') {\n    y += lineHeight / 2;\n  } else if (textBaseline === 'bottom') {\n    y -= lineHeight / 2;\n  }\n  return y;\n}\nexport function hasShadow(style) {\n  return style && (style.shadowBlur || style.shadowOffsetX || style.shadowOffsetY);\n}\nexport function getShadowKey(displayable) {\n  var style = displayable.style;\n  var globalScale = displayable.getGlobalScale();\n  return [style.shadowColor, (style.shadowBlur || 0).toFixed(2), (style.shadowOffsetX || 0).toFixed(2), (style.shadowOffsetY || 0).toFixed(2), globalScale[0], globalScale[1]].join(',');\n}\nexport function getClipPathsKey(clipPaths) {\n  var key = [];\n  if (clipPaths) {\n    for (var i = 0; i < clipPaths.length; i++) {\n      var clipPath = clipPaths[i];\n      key.push(clipPath.id);\n    }\n  }\n  return key.join(',');\n}\nexport function isImagePattern(val) {\n  return val && !!val.image;\n}\nexport function isSVGPattern(val) {\n  return val && !!val.svgElement;\n}\nexport function isPattern(val) {\n  return isImagePattern(val) || isSVGPattern(val);\n}\nexport function isLinearGradient(val) {\n  return val.type === 'linear';\n}\nexport function isRadialGradient(val) {\n  return val.type === 'radial';\n}\nexport function isGradient(val) {\n  return val && (val.type === 'linear' || val.type === 'radial');\n}\nexport function getIdURL(id) {\n  return \"url(#\" + id + \")\";\n}\nexport function getPathPrecision(el) {\n  var scale = el.getGlobalScale();\n  var size = Math.max(scale[0], scale[1]);\n  return Math.max(Math.ceil(Math.log(size) / Math.log(10)), 1);\n}\nexport function getSRTTransformString(transform) {\n  var x = transform.x || 0;\n  var y = transform.y || 0;\n  var rotation = (transform.rotation || 0) * RADIAN_TO_DEGREE;\n  var scaleX = retrieve2(transform.scaleX, 1);\n  var scaleY = retrieve2(transform.scaleY, 1);\n  var skewX = transform.skewX || 0;\n  var skewY = transform.skewY || 0;\n  var res = [];\n  if (x || y) {\n    res.push(\"translate(\" + x + \"px,\" + y + \"px)\");\n  }\n  if (rotation) {\n    res.push(\"rotate(\" + rotation + \")\");\n  }\n  if (scaleX !== 1 || scaleY !== 1) {\n    res.push(\"scale(\" + scaleX + \",\" + scaleY + \")\");\n  }\n  if (skewX || skewY) {\n    res.push(\"skew(\" + mathRound(skewX * RADIAN_TO_DEGREE) + \"deg, \" + mathRound(skewY * RADIAN_TO_DEGREE) + \"deg)\");\n  }\n  return res.join(' ');\n}\nexport var encodeBase64 = function () {\n  if (env.hasGlobalWindow && isFunction(window.btoa)) {\n    return function (str) {\n      return window.btoa(unescape(encodeURIComponent(str)));\n    };\n  }\n  if (typeof Buffer !== 'undefined') {\n    return function (str) {\n      return Buffer.from(str).toString('base64');\n    };\n  }\n  return function (str) {\n    if (process.env.NODE_ENV !== 'production') {\n      logError('Base64 isn\\'t natively supported in the current environment.');\n    }\n    return null;\n  };\n}();", "map": {"version": 3, "names": ["RADIAN_TO_DEGREE", "retrieve2", "logError", "isFunction", "parse", "env", "mathRound", "Math", "round", "normalizeColor", "color", "opacity", "indexOf", "arr", "EPSILON", "isAroundZero", "transform", "round3", "round4", "round1", "getMatrixStr", "m", "TEXT_ALIGN_TO_ANCHOR", "left", "right", "center", "middle", "adjustTextY", "y", "lineHeight", "textBaseline", "<PERSON><PERSON><PERSON><PERSON>", "style", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "getShadowKey", "displayable", "globalScale", "getGlobalScale", "shadowColor", "toFixed", "join", "getClipPaths<PERSON>ey", "clipPaths", "key", "i", "length", "clipPath", "push", "id", "isImagePattern", "val", "image", "isSVGPattern", "svgElement", "isPattern", "isLinearGradient", "type", "isRadialGradient", "isGradient", "getIdURL", "getPathPrecision", "el", "scale", "size", "max", "ceil", "log", "getSRTTransformString", "x", "rotation", "scaleX", "scaleY", "skewX", "skewY", "res", "encodeBase64", "hasGlobalWindow", "window", "btoa", "str", "unescape", "encodeURIComponent", "<PERSON><PERSON><PERSON>", "from", "toString", "process", "NODE_ENV"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/zrender/lib/svg/helper.js"], "sourcesContent": ["import { RADIAN_TO_DEGREE, retrieve2, logError, isFunction } from '../core/util.js';\nimport { parse } from '../tool/color.js';\nimport env from '../core/env.js';\nvar mathRound = Math.round;\nexport function normalizeColor(color) {\n    var opacity;\n    if (!color || color === 'transparent') {\n        color = 'none';\n    }\n    else if (typeof color === 'string' && color.indexOf('rgba') > -1) {\n        var arr = parse(color);\n        if (arr) {\n            color = 'rgb(' + arr[0] + ',' + arr[1] + ',' + arr[2] + ')';\n            opacity = arr[3];\n        }\n    }\n    return {\n        color: color,\n        opacity: opacity == null ? 1 : opacity\n    };\n}\nvar EPSILON = 1e-4;\nexport function isAroundZero(transform) {\n    return transform < EPSILON && transform > -EPSILON;\n}\nexport function round3(transform) {\n    return mathRound(transform * 1e3) / 1e3;\n}\nexport function round4(transform) {\n    return mathRound(transform * 1e4) / 1e4;\n}\nexport function round1(transform) {\n    return mathRound(transform * 10) / 10;\n}\nexport function getMatrixStr(m) {\n    return 'matrix('\n        + round3(m[0]) + ','\n        + round3(m[1]) + ','\n        + round3(m[2]) + ','\n        + round3(m[3]) + ','\n        + round4(m[4]) + ','\n        + round4(m[5])\n        + ')';\n}\nexport var TEXT_ALIGN_TO_ANCHOR = {\n    left: 'start',\n    right: 'end',\n    center: 'middle',\n    middle: 'middle'\n};\nexport function adjustTextY(y, lineHeight, textBaseline) {\n    if (textBaseline === 'top') {\n        y += lineHeight / 2;\n    }\n    else if (textBaseline === 'bottom') {\n        y -= lineHeight / 2;\n    }\n    return y;\n}\nexport function hasShadow(style) {\n    return style\n        && (style.shadowBlur || style.shadowOffsetX || style.shadowOffsetY);\n}\nexport function getShadowKey(displayable) {\n    var style = displayable.style;\n    var globalScale = displayable.getGlobalScale();\n    return [\n        style.shadowColor,\n        (style.shadowBlur || 0).toFixed(2),\n        (style.shadowOffsetX || 0).toFixed(2),\n        (style.shadowOffsetY || 0).toFixed(2),\n        globalScale[0],\n        globalScale[1]\n    ].join(',');\n}\nexport function getClipPathsKey(clipPaths) {\n    var key = [];\n    if (clipPaths) {\n        for (var i = 0; i < clipPaths.length; i++) {\n            var clipPath = clipPaths[i];\n            key.push(clipPath.id);\n        }\n    }\n    return key.join(',');\n}\nexport function isImagePattern(val) {\n    return val && (!!val.image);\n}\nexport function isSVGPattern(val) {\n    return val && (!!val.svgElement);\n}\nexport function isPattern(val) {\n    return isImagePattern(val) || isSVGPattern(val);\n}\nexport function isLinearGradient(val) {\n    return val.type === 'linear';\n}\nexport function isRadialGradient(val) {\n    return val.type === 'radial';\n}\nexport function isGradient(val) {\n    return val && (val.type === 'linear'\n        || val.type === 'radial');\n}\nexport function getIdURL(id) {\n    return \"url(#\" + id + \")\";\n}\nexport function getPathPrecision(el) {\n    var scale = el.getGlobalScale();\n    var size = Math.max(scale[0], scale[1]);\n    return Math.max(Math.ceil(Math.log(size) / Math.log(10)), 1);\n}\nexport function getSRTTransformString(transform) {\n    var x = transform.x || 0;\n    var y = transform.y || 0;\n    var rotation = (transform.rotation || 0) * RADIAN_TO_DEGREE;\n    var scaleX = retrieve2(transform.scaleX, 1);\n    var scaleY = retrieve2(transform.scaleY, 1);\n    var skewX = transform.skewX || 0;\n    var skewY = transform.skewY || 0;\n    var res = [];\n    if (x || y) {\n        res.push(\"translate(\" + x + \"px,\" + y + \"px)\");\n    }\n    if (rotation) {\n        res.push(\"rotate(\" + rotation + \")\");\n    }\n    if (scaleX !== 1 || scaleY !== 1) {\n        res.push(\"scale(\" + scaleX + \",\" + scaleY + \")\");\n    }\n    if (skewX || skewY) {\n        res.push(\"skew(\" + mathRound(skewX * RADIAN_TO_DEGREE) + \"deg, \" + mathRound(skewY * RADIAN_TO_DEGREE) + \"deg)\");\n    }\n    return res.join(' ');\n}\nexport var encodeBase64 = (function () {\n    if (env.hasGlobalWindow && isFunction(window.btoa)) {\n        return function (str) {\n            return window.btoa(unescape(encodeURIComponent(str)));\n        };\n    }\n    if (typeof Buffer !== 'undefined') {\n        return function (str) {\n            return Buffer.from(str).toString('base64');\n        };\n    }\n    return function (str) {\n        if (process.env.NODE_ENV !== 'production') {\n            logError('Base64 isn\\'t natively supported in the current environment.');\n        }\n        return null;\n    };\n})();\n"], "mappings": ";;AAAA,SAASA,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,iBAAiB;AACnF,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK;AAC1B,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EAClC,IAAIC,OAAO;EACX,IAAI,CAACD,KAAK,IAAIA,KAAK,KAAK,aAAa,EAAE;IACnCA,KAAK,GAAG,MAAM;EAClB,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC9D,IAAIC,GAAG,GAAGT,KAAK,CAACM,KAAK,CAAC;IACtB,IAAIG,GAAG,EAAE;MACLH,KAAK,GAAG,MAAM,GAAGG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;MAC3DF,OAAO,GAAGE,GAAG,CAAC,CAAC,CAAC;IACpB;EACJ;EACA,OAAO;IACHH,KAAK,EAAEA,KAAK;IACZC,OAAO,EAAEA,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA;EACnC,CAAC;AACL;AACA,IAAIG,OAAO,GAAG,IAAI;AAClB,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAE;EACpC,OAAOA,SAAS,GAAGF,OAAO,IAAIE,SAAS,GAAG,CAACF,OAAO;AACtD;AACA,OAAO,SAASG,MAAMA,CAACD,SAAS,EAAE;EAC9B,OAAOV,SAAS,CAACU,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG;AAC3C;AACA,OAAO,SAASE,MAAMA,CAACF,SAAS,EAAE;EAC9B,OAAOV,SAAS,CAACU,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG;AAC3C;AACA,OAAO,SAASG,MAAMA,CAACH,SAAS,EAAE;EAC9B,OAAOV,SAAS,CAACU,SAAS,GAAG,EAAE,CAAC,GAAG,EAAE;AACzC;AACA,OAAO,SAASI,YAAYA,CAACC,CAAC,EAAE;EAC5B,OAAO,SAAS,GACVJ,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAClBJ,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAClBJ,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAClBJ,MAAM,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAClBH,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAClBH,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,GACZ,GAAG;AACb;AACA,OAAO,IAAIC,oBAAoB,GAAG;EAC9BC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE;AACZ,CAAC;AACD,OAAO,SAASC,WAAWA,CAACC,CAAC,EAAEC,UAAU,EAAEC,YAAY,EAAE;EACrD,IAAIA,YAAY,KAAK,KAAK,EAAE;IACxBF,CAAC,IAAIC,UAAU,GAAG,CAAC;EACvB,CAAC,MACI,IAAIC,YAAY,KAAK,QAAQ,EAAE;IAChCF,CAAC,IAAIC,UAAU,GAAG,CAAC;EACvB;EACA,OAAOD,CAAC;AACZ;AACA,OAAO,SAASG,SAASA,CAACC,KAAK,EAAE;EAC7B,OAAOA,KAAK,KACJA,KAAK,CAACC,UAAU,IAAID,KAAK,CAACE,aAAa,IAAIF,KAAK,CAACG,aAAa,CAAC;AAC3E;AACA,OAAO,SAASC,YAAYA,CAACC,WAAW,EAAE;EACtC,IAAIL,KAAK,GAAGK,WAAW,CAACL,KAAK;EAC7B,IAAIM,WAAW,GAAGD,WAAW,CAACE,cAAc,CAAC,CAAC;EAC9C,OAAO,CACHP,KAAK,CAACQ,WAAW,EACjB,CAACR,KAAK,CAACC,UAAU,IAAI,CAAC,EAAEQ,OAAO,CAAC,CAAC,CAAC,EAClC,CAACT,KAAK,CAACE,aAAa,IAAI,CAAC,EAAEO,OAAO,CAAC,CAAC,CAAC,EACrC,CAACT,KAAK,CAACG,aAAa,IAAI,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,EACrCH,WAAW,CAAC,CAAC,CAAC,EACdA,WAAW,CAAC,CAAC,CAAC,CACjB,CAACI,IAAI,CAAC,GAAG,CAAC;AACf;AACA,OAAO,SAASC,eAAeA,CAACC,SAAS,EAAE;EACvC,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAID,SAAS,EAAE;IACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAIE,QAAQ,GAAGJ,SAAS,CAACE,CAAC,CAAC;MAC3BD,GAAG,CAACI,IAAI,CAACD,QAAQ,CAACE,EAAE,CAAC;IACzB;EACJ;EACA,OAAOL,GAAG,CAACH,IAAI,CAAC,GAAG,CAAC;AACxB;AACA,OAAO,SAASS,cAAcA,CAACC,GAAG,EAAE;EAChC,OAAOA,GAAG,IAAK,CAAC,CAACA,GAAG,CAACC,KAAM;AAC/B;AACA,OAAO,SAASC,YAAYA,CAACF,GAAG,EAAE;EAC9B,OAAOA,GAAG,IAAK,CAAC,CAACA,GAAG,CAACG,UAAW;AACpC;AACA,OAAO,SAASC,SAASA,CAACJ,GAAG,EAAE;EAC3B,OAAOD,cAAc,CAACC,GAAG,CAAC,IAAIE,YAAY,CAACF,GAAG,CAAC;AACnD;AACA,OAAO,SAASK,gBAAgBA,CAACL,GAAG,EAAE;EAClC,OAAOA,GAAG,CAACM,IAAI,KAAK,QAAQ;AAChC;AACA,OAAO,SAASC,gBAAgBA,CAACP,GAAG,EAAE;EAClC,OAAOA,GAAG,CAACM,IAAI,KAAK,QAAQ;AAChC;AACA,OAAO,SAASE,UAAUA,CAACR,GAAG,EAAE;EAC5B,OAAOA,GAAG,KAAKA,GAAG,CAACM,IAAI,KAAK,QAAQ,IAC7BN,GAAG,CAACM,IAAI,KAAK,QAAQ,CAAC;AACjC;AACA,OAAO,SAASG,QAAQA,CAACX,EAAE,EAAE;EACzB,OAAO,OAAO,GAAGA,EAAE,GAAG,GAAG;AAC7B;AACA,OAAO,SAASY,gBAAgBA,CAACC,EAAE,EAAE;EACjC,IAAIC,KAAK,GAAGD,EAAE,CAACxB,cAAc,CAAC,CAAC;EAC/B,IAAI0B,IAAI,GAAG1D,IAAI,CAAC2D,GAAG,CAACF,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EACvC,OAAOzD,IAAI,CAAC2D,GAAG,CAAC3D,IAAI,CAAC4D,IAAI,CAAC5D,IAAI,CAAC6D,GAAG,CAACH,IAAI,CAAC,GAAG1D,IAAI,CAAC6D,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAChE;AACA,OAAO,SAASC,qBAAqBA,CAACrD,SAAS,EAAE;EAC7C,IAAIsD,CAAC,GAAGtD,SAAS,CAACsD,CAAC,IAAI,CAAC;EACxB,IAAI1C,CAAC,GAAGZ,SAAS,CAACY,CAAC,IAAI,CAAC;EACxB,IAAI2C,QAAQ,GAAG,CAACvD,SAAS,CAACuD,QAAQ,IAAI,CAAC,IAAIvE,gBAAgB;EAC3D,IAAIwE,MAAM,GAAGvE,SAAS,CAACe,SAAS,CAACwD,MAAM,EAAE,CAAC,CAAC;EAC3C,IAAIC,MAAM,GAAGxE,SAAS,CAACe,SAAS,CAACyD,MAAM,EAAE,CAAC,CAAC;EAC3C,IAAIC,KAAK,GAAG1D,SAAS,CAAC0D,KAAK,IAAI,CAAC;EAChC,IAAIC,KAAK,GAAG3D,SAAS,CAAC2D,KAAK,IAAI,CAAC;EAChC,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIN,CAAC,IAAI1C,CAAC,EAAE;IACRgD,GAAG,CAAC3B,IAAI,CAAC,YAAY,GAAGqB,CAAC,GAAG,KAAK,GAAG1C,CAAC,GAAG,KAAK,CAAC;EAClD;EACA,IAAI2C,QAAQ,EAAE;IACVK,GAAG,CAAC3B,IAAI,CAAC,SAAS,GAAGsB,QAAQ,GAAG,GAAG,CAAC;EACxC;EACA,IAAIC,MAAM,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,EAAE;IAC9BG,GAAG,CAAC3B,IAAI,CAAC,QAAQ,GAAGuB,MAAM,GAAG,GAAG,GAAGC,MAAM,GAAG,GAAG,CAAC;EACpD;EACA,IAAIC,KAAK,IAAIC,KAAK,EAAE;IAChBC,GAAG,CAAC3B,IAAI,CAAC,OAAO,GAAG3C,SAAS,CAACoE,KAAK,GAAG1E,gBAAgB,CAAC,GAAG,OAAO,GAAGM,SAAS,CAACqE,KAAK,GAAG3E,gBAAgB,CAAC,GAAG,MAAM,CAAC;EACpH;EACA,OAAO4E,GAAG,CAAClC,IAAI,CAAC,GAAG,CAAC;AACxB;AACA,OAAO,IAAImC,YAAY,GAAI,YAAY;EACnC,IAAIxE,GAAG,CAACyE,eAAe,IAAI3E,UAAU,CAAC4E,MAAM,CAACC,IAAI,CAAC,EAAE;IAChD,OAAO,UAAUC,GAAG,EAAE;MAClB,OAAOF,MAAM,CAACC,IAAI,CAACE,QAAQ,CAACC,kBAAkB,CAACF,GAAG,CAAC,CAAC,CAAC;IACzD,CAAC;EACL;EACA,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAO,UAAUH,GAAG,EAAE;MAClB,OAAOG,MAAM,CAACC,IAAI,CAACJ,GAAG,CAAC,CAACK,QAAQ,CAAC,QAAQ,CAAC;IAC9C,CAAC;EACL;EACA,OAAO,UAAUL,GAAG,EAAE;IAClB,IAAIM,OAAO,CAAClF,GAAG,CAACmF,QAAQ,KAAK,YAAY,EAAE;MACvCtF,QAAQ,CAAC,8DAA8D,CAAC;IAC5E;IACA,OAAO,IAAI;EACf,CAAC;AACL,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}