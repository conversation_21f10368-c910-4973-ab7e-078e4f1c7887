{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport BoxplotSeriesModel from './BoxplotSeries.js';\nimport BoxplotView from './BoxplotView.js';\nimport boxplotLayout from './boxplotLayout.js';\nimport { boxplotTransform } from './boxplotTransform.js';\nexport function install(registers) {\n  registers.registerSeriesModel(BoxplotSeriesModel);\n  registers.registerChartView(BoxplotView);\n  registers.registerLayout(boxplotLayout);\n  registers.registerTransform(boxplotTransform);\n}", "map": {"version": 3, "names": ["BoxplotSeriesModel", "BoxplotView", "boxplotLayout", "boxplotTransform", "install", "registers", "registerSeriesModel", "registerChartView", "registerLayout", "registerTransform"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/chart/boxplot/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport BoxplotSeriesModel from './BoxplotSeries.js';\nimport BoxplotView from './BoxplotView.js';\nimport boxplotLayout from './boxplotLayout.js';\nimport { boxplotTransform } from './boxplotTransform.js';\nexport function install(registers) {\n  registers.registerSeriesModel(BoxplotSeriesModel);\n  registers.registerChartView(BoxplotView);\n  registers.registerLayout(boxplotLayout);\n  registers.registerTransform(boxplotTransform);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,kBAAkB,MAAM,oBAAoB;AACnD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,mBAAmB,CAACN,kBAAkB,CAAC;EACjDK,SAAS,CAACE,iBAAiB,CAACN,WAAW,CAAC;EACxCI,SAAS,CAACG,cAAc,CAACN,aAAa,CAAC;EACvCG,SAAS,CAACI,iBAAiB,CAACN,gBAAgB,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}