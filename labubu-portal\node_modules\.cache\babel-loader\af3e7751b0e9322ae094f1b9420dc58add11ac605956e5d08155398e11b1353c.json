{"ast": null, "code": "import { Swiper, SwiperSlide, directive } from \"vue-awesome-swiper\";\nimport * as echarts from \"echarts\";\nexport default {\n  components: {\n    Swiper,\n    SwiperSlide\n  },\n  directives: {\n    swiper: directive\n  },\n  data() {\n    return {\n      activeNav: \"news\",\n      logoUrl: \"https://readdy.ai/api/search-image?query=cute%20cartoon%20logo%20for%20Labubu%2C%20minimalist%2C%20pastel%20colors%2C%20simple%20design%2C%20kawaii%20style%2C%20rounded%20edges%2C%20pink%20and%20mint%20color%20palette%2C%20white%20background%2C%20high%20quality&width=200&height=200&seq=1&orientation=squarish\",\n      heroBackgroundUrl: \"https://readdy.ai/api/search-image?query=cute%20cartoon%20characters%20collection%2C%20pastel%20colors%2C%20kawaii%20style%2C%20soft%20gradient%20background%2C%20pink%20and%20mint%20color%20palette%2C%20adorable%20fantasy%20creatures%2C%20high%20quality%20illustration%2C%20simple%20and%20clean%20design&width=1440&height=500&seq=2&orientation=landscape\",\n      navItems: [{\n        id: \"news\",\n        name: \"最新资讯\",\n        icon: \"fas fa-newspaper\"\n      }, {\n        id: \"guide\",\n        name: \"收藏指南\",\n        icon: \"fas fa-book\"\n      }, {\n        id: \"community\",\n        name: \"社区讨论\",\n        icon: \"fas fa-users\"\n      }, {\n        id: \"purchase\",\n        name: \"购买指南\",\n        icon: \"fas fa-shopping-bag\"\n      }],\n      swiperOptions: {\n        slidesPerView: 1,\n        spaceBetween: 0,\n        loop: true,\n        autoplay: {\n          delay: 5000,\n          disableOnInteraction: false\n        },\n        pagination: {\n          el: \".swiper-pagination\",\n          clickable: true\n        },\n        navigation: {\n          nextEl: \".swiper-button-next\",\n          prevEl: \".swiper-button-prev\"\n        }\n      },\n      newsSlides: [{\n        title: \"Labubu 2025 限量版系列即将发布\",\n        description: \"全新限量版 Labubu 系列将于下月发布，包含 5 款全新角色设计\",\n        image: \"https://readdy.ai/api/search-image?query=cute%20cartoon%20figurine%20collection%20on%20display%2C%20pastel%20colors%2C%20kawaii%20style%2C%20soft%20lighting%2C%20professional%20product%20photography%2C%20clean%20background%2C%20high%20quality%2C%20detailed%20figurines&width=1200&height=600&seq=3&orientation=landscape\"\n      }, {\n        title: \"第三届 Labubu 收藏家大会圆满结束\",\n        description: \"来自全球的 Labubu 爱好者齐聚上海，分享收藏心得与经验\",\n        image: \"https://readdy.ai/api/search-image?query=cute%20cartoon%20convention%20with%20people%20displaying%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20exhibition%20hall%2C%20collectors%20meeting%2C%20bright%20and%20colorful%20atmosphere%2C%20high%20quality&width=1200&height=600&seq=4&orientation=landscape\"\n      }, {\n        title: \"Labubu 艺术家签售会即将举行\",\n        description: \"原创设计师将在北京、上海、广州三地举办签售会\",\n        image: \"https://readdy.ai/api/search-image?query=artist%20signing%20cute%20cartoon%20figurines%2C%20fans%20waiting%20in%20line%2C%20pastel%20colors%2C%20kawaii%20style%2C%20bright%20indoor%20setting%2C%20art%20gallery%20atmosphere%2C%20high%20quality&width=1200&height=600&seq=5&orientation=landscape\"\n      }],\n      newsCategories: [{\n        title: \"官方资讯\",\n        icon: \"fas fa-bullhorn\",\n        items: [\"Labubu 2025 夏季限定款预售开启\", \"设计师访谈：Labubu 灵感来源揭秘\", \"官方商城六一儿童节特别活动\", \"新品发布：Labubu 海洋系列\"]\n      }, {\n        title: \"行业动态\",\n        icon: \"fas fa-chart-line\",\n        items: [\"2025 年潮玩市场发展趋势分析\", \"Labubu 在国际收藏市场的地位\", \"潮玩收藏品投资价值研究报告\", \"全球限量版收藏品拍卖记录\"]\n      }, {\n        title: \"社区热点\",\n        icon: \"fas fa-fire\",\n        items: [\"收藏家故事：我与 Labubu 的十年之约\", \"社区投票：最受欢迎 Labubu 系列\", \"粉丝创作：Labubu 同人作品展示\", \"线下活动：各地 Labubu 粉丝聚会\"]\n      }],\n      collectionGuides: [{\n        title: \"新手入门指南\",\n        description: \"从零开始了解 Labubu 收藏，掌握基础知识和收藏技巧\",\n        image: \"https://readdy.ai/api/search-image?query=beginner%20guide%20to%20collecting%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20educational%20illustration%2C%20simple%20steps%20shown%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=6&orientation=landscape\",\n        icon: \"fas fa-star\"\n      }, {\n        title: \"真伪鉴别技巧\",\n        description: \"学习如何辨别 Labubu 正品与仿制品，保护您的收藏投资\",\n        image: \"https://readdy.ai/api/search-image?query=comparing%20authentic%20vs%20fake%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20side%20by%20side%20comparison%2C%20magnifying%20glass%2C%20detailed%20differences%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=7&orientation=landscape\",\n        icon: \"fas fa-check-circle\"\n      }, {\n        title: \"收藏保养方法\",\n        description: \"专业的 Labubu 收藏品保养技巧，让您的收藏保持最佳状态\",\n        image: \"https://readdy.ai/api/search-image?query=caring%20for%20cute%20cartoon%20figurines%2C%20cleaning%20and%20maintenance%2C%20pastel%20colors%2C%20kawaii%20style%2C%20gentle%20handling%2C%20display%20case%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=8&orientation=landscape\",\n        icon: \"fas fa-heart\"\n      }, {\n        title: \"投资价值分析\",\n        description: \"深入分析 Labubu 收藏品的市场价值和投资潜力\",\n        image: \"https://readdy.ai/api/search-image?query=investment%20value%20chart%20with%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20financial%20graphs%2C%20upward%20trends%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=9&orientation=landscape\",\n        icon: \"fas fa-chart-line\"\n      }],\n      authenticationTips: [\"检查包装盒上的官方防伪标识和序列号\", \"注意 Labubu 的材质质感和细节处理\", \"辨别正品特有的颜色饱和度和色彩过渡\", \"了解不同系列的限量编号规则\", \"通过官方渠道验证产品真伪\"],\n      authenticationImage: \"https://readdy.ai/api/search-image?query=authentication%20guide%20for%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20showing%20details%20for%20verification%2C%20magnifying%20glass%20examining%20details%2C%20clean%20background%2C%20high%20quality&width=600&height=600&seq=10&orientation=squarish\",\n      communityShowcase: [{\n        title: \"梦幻系列全收藏\",\n        user: \"星辰收藏家\",\n        image: \"https://readdy.ai/api/search-image?query=complete%20collection%20of%20cute%20cartoon%20figurines%20on%20display%20shelf%2C%20pastel%20colors%2C%20kawaii%20style%2C%20dream%20series%2C%20organized%20display%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=11&orientation=squarish\"\n      }, {\n        title: \"限量版珍藏\",\n        user: \"稀有收集者\",\n        image: \"https://readdy.ai/api/search-image?query=limited%20edition%20cute%20cartoon%20figurines%20in%20display%20case%2C%20pastel%20colors%2C%20kawaii%20style%2C%20rare%20collectibles%2C%20special%20lighting%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=12&orientation=squarish\"\n      }, {\n        title: \"海洋主题系列\",\n        user: \"蓝色梦想\",\n        image: \"https://readdy.ai/api/search-image?query=ocean%20themed%20cute%20cartoon%20figurines%20collection%2C%20pastel%20colors%2C%20kawaii%20style%2C%20blue%20color%20palette%2C%20sea%20creatures%20design%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=13&orientation=squarish\"\n      }, {\n        title: \"节日特别版\",\n        user: \"四季收藏家\",\n        image: \"https://readdy.ai/api/search-image?query=holiday%20special%20edition%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20festive%20themed%2C%20christmas%20and%20halloween%20designs%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=14&orientation=squarish\"\n      }],\n      communityDiscussions: [{\n        title: \"分享我的 Labubu 收藏之旅：从入门到痴迷\",\n        preview: \"作为一名收藏 Labubu 已有五年的老粉，想和大家分享我的收藏经验和心得...\",\n        user: \"粉红收藏家\",\n        userAvatar: \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20female%20character%2C%20pastel%20pink%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=15&orientation=squarish\",\n        date: \"2025-06-15\",\n        comments: 42,\n        likes: 156\n      }, {\n        title: \"求助：如何为 Labubu 收藏品打造完美展示空间？\",\n        preview: \"最近我的收藏越来越多，想请教各位大神如何设计一个既美观又实用的展示柜...\",\n        user: \"新手收藏者\",\n        userAvatar: \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20male%20character%2C%20pastel%20blue%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=16&orientation=squarish\",\n        date: \"2025-06-14\",\n        comments: 28,\n        likes: 73\n      }, {\n        title: \"讨论：2025年最值得投资的 Labubu 系列是哪个？\",\n        preview: \"根据市场趋势和历年数据分析，我认为今年最具投资潜力的系列是...\",\n        user: \"投资分析师\",\n        userAvatar: \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20professional%20character%20with%20glasses%2C%20pastel%20green%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=17&orientation=squarish\",\n        date: \"2025-06-12\",\n        comments: 56,\n        likes: 210\n      }],\n      expertQA: [{\n        question: \"如何判断早期 Labubu 系列的收藏价值？\",\n        answer: \"早期系列的价值主要看限量程度、保存状态和市场需求。建议关注官方发行量数据，以及历年拍卖成交价格走势。\"\n      }, {\n        question: \"Labubu 收藏品应该拆盒展示还是保持原包装？\",\n        answer: \"从保值角度看，保持原包装完好更有利于维持价值。但个人收藏可根据自己喜好，拆盒展示能更好欣赏细节。\"\n      }, {\n        question: \"如何防止 Labubu 收藏品褪色或变形？\",\n        answer: \"避免阳光直射、高温和潮湿环境，定期用软毛刷轻轻除尘，不要用湿布擦拭，展示柜最好选择防紫外线玻璃。\"\n      }],\n      officialChannels: [{\n        name: \"官方网店\",\n        icon: \"fas fa-store\",\n        description: \"Labubu 官方授权网店，提供最新发布和限量版收藏品\",\n        verification: \"官方认证销售渠道\"\n      }, {\n        name: \"授权实体店\",\n        icon: \"fas fa-map-marker-alt\",\n        description: \"遍布全国的 Labubu 授权实体店，可实地挑选和购买\",\n        verification: \"线下官方授权店铺\"\n      }, {\n        name: \"特许经销商\",\n        icon: \"fas fa-certificate\",\n        description: \"经过严格筛选的特许经销商，确保产品正品保障\",\n        verification: \"官方认证合作伙伴\"\n      }],\n      purchaseTips: [{\n        title: \"关注发售时间\",\n        content: \"限量版 Labubu 通常会在特定时间发售，提前关注官方公告，设置提醒避免错过。\"\n      }, {\n        title: \"验证销售渠道\",\n        content: \"只从官方认证的渠道购买，避免购买到假冒产品，保障收藏品的真实性和价值。\"\n      }, {\n        title: \"检查包装完整性\",\n        content: \"收到商品后，仔细检查包装是否完好，防伪标识是否清晰，附件是否齐全。\"\n      }, {\n        title: \"保留购买凭证\",\n        content: \"妥善保管购买凭证和收据，这对于后续鉴定真伪和转售都非常重要。\"\n      }],\n      antiScamGuide: [{\n        title: \"虚假预售骗局\",\n        description: \"谨防声称可预订未发布系列的卖家，官方预售只通过官方渠道进行。\"\n      }, {\n        title: \"以次充好\",\n        description: \"警惕价格明显低于市场的商品，可能是仿制品或有质量问题的次品。\"\n      }, {\n        title: \"虚假限量编号\",\n        description: \"核实限量版编号的真实性，某些骗子会伪造限量编号标签。\"\n      }],\n      antiScamImage: \"https://readdy.ai/api/search-image?query=anti-scam%20guide%20for%20buying%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20warning%20signs%2C%20security%20verification%2C%20safe%20shopping%20tips%2C%20clean%20background%2C%20high%20quality&width=600&height=500&seq=18&orientation=landscape\"\n    };\n  },\n  mounted() {\n    this.initPriceChart();\n  },\n  methods: {\n    initPriceChart() {\n      const chartDom = this.$refs.priceChart;\n      const myChart = echarts.init(chartDom);\n      const option = {\n        animation: false,\n        title: {\n          text: \"Labubu 系列价格趋势 (2023-2025)\",\n          left: \"center\",\n          textStyle: {\n            color: \"#333\"\n          }\n        },\n        tooltip: {\n          trigger: \"axis\"\n        },\n        legend: {\n          data: [\"限量版系列\", \"常规系列\", \"特别合作款\"],\n          bottom: 0\n        },\n        grid: {\n          left: \"3%\",\n          right: \"4%\",\n          bottom: \"15%\",\n          top: \"15%\",\n          containLabel: true\n        },\n        xAxis: {\n          type: \"category\",\n          boundaryGap: false,\n          data: [\"2023 Q1\", \"2023 Q2\", \"2023 Q3\", \"2023 Q4\", \"2024 Q1\", \"2024 Q2\", \"2024 Q3\", \"2024 Q4\", \"2025 Q1\", \"2025 Q2\"]\n        },\n        yAxis: {\n          type: \"value\",\n          name: \"价格 (¥)\",\n          nameLocation: \"end\"\n        },\n        series: [{\n          name: \"限量版系列\",\n          type: \"line\",\n          data: [1200, 1350, 1500, 1800, 2100, 2400, 2700, 3100, 3500, 3800],\n          smooth: true,\n          lineStyle: {\n            color: \"#ff6b81\"\n          },\n          itemStyle: {\n            color: \"#ff6b81\"\n          }\n        }, {\n          name: \"常规系列\",\n          type: \"line\",\n          data: [300, 320, 350, 370, 390, 410, 430, 450, 480, 500],\n          smooth: true,\n          lineStyle: {\n            color: \"#70a1ff\"\n          },\n          itemStyle: {\n            color: \"#70a1ff\"\n          }\n        }, {\n          name: \"特别合作款\",\n          type: \"line\",\n          data: [800, 950, 1100, 1300, 1500, 1650, 1800, 2000, 2300, 2600],\n          smooth: true,\n          lineStyle: {\n            color: \"#7bed9f\"\n          },\n          itemStyle: {\n            color: \"#7bed9f\"\n          }\n        }]\n      };\n      myChart.setOption(option);\n      window.addEventListener(\"resize\", () => {\n        myChart.resize();\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["Swiper", "SwiperSlide", "directive", "echarts", "components", "directives", "swiper", "data", "activeNav", "logoUrl", "heroBackgroundUrl", "navItems", "id", "name", "icon", "swiperOptions", "<PERSON><PERSON><PERSON><PERSON>iew", "spaceBetween", "loop", "autoplay", "delay", "disableOnInteraction", "pagination", "el", "clickable", "navigation", "nextEl", "prevEl", "newsSlides", "title", "description", "image", "newsCategories", "items", "collectionGuides", "authenticationTips", "authenticationImage", "communityShowcase", "user", "communityDiscussions", "preview", "userAvatar", "date", "comments", "likes", "expertQA", "question", "answer", "officialChannels", "verification", "purchaseTips", "content", "antiScamGuide", "antiScamImage", "mounted", "initPriceChart", "methods", "chartDom", "$refs", "priceChart", "myChart", "init", "option", "animation", "text", "left", "textStyle", "color", "tooltip", "trigger", "legend", "bottom", "grid", "right", "top", "containLabel", "xAxis", "type", "boundaryGap", "yAxis", "nameLocation", "series", "smooth", "lineStyle", "itemStyle", "setOption", "window", "addEventListener", "resize"], "sources": ["C:\\suibianwanwan\\labubu-portal\\src\\App.vue"], "sourcesContent": ["<template>\n  <div class=\"min-h-screen bg-gray-50 font-sans\">\n    <!-- 导航栏 -->\n    <header\n      class=\"sticky top-0 z-50 bg-white bg-opacity-90 backdrop-blur-sm shadow-sm\"\n    >\n      <div\n        class=\"container mx-auto px-4 py-3 flex items-center justify-between\"\n      >\n        <!-- Logo -->\n        <div class=\"flex items-center\">\n          <img :src=\"logoUrl\" alt=\"Labubu Logo\" class=\"h-12 mr-3\" />\n          <h1 class=\"text-2xl font-bold text-pink-500\">Labubu Portal</h1>\n        </div>\n\n        <!-- 主导航 -->\n        <nav class=\"hidden md:flex space-x-8\">\n          <a\n            v-for=\"(item, index) in navItems\"\n            :key=\"index\"\n            :class=\"['nav-link cursor-pointer whitespace-nowrap', activeNav === item.id ? 'text-pink-500 font-bold' : 'text-gray-600 hover:text-pink-400']\"\n            @click=\"activeNav = item.id\"\n          >\n            <i :class=\"['mr-2', item.icon]\"></i>{{ item.name }}\n          </a>\n        </nav>\n\n        <!-- 搜索和用户中心 -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"relative\">\n            <input\n              type=\"text\"\n              placeholder=\"搜索 Labubu 内容...\"\n              class=\"pl-10 pr-4 py-2 rounded-full border-none bg-gray-100 text-sm focus:ring-2 focus:ring-pink-300 focus:outline-none w-40 md:w-64\"\n            />\n            <i\n              class=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n            ></i>\n          </div>\n          <button\n            class=\"bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n          >\n            <i class=\"fas fa-user mr-2\"></i>登录\n          </button>\n        </div>\n      </div>\n    </header>\n\n    <!-- 主内容区 -->\n    <main>\n      <!-- Hero 区域 -->\n      <section class=\"relative overflow-hidden\" style=\"min-height: 500px;\">\n        <div class=\"absolute inset-0 z-0\">\n          <img\n            :src=\"heroBackgroundUrl\"\n            alt=\"Labubu 背景\"\n            class=\"w-full h-full object-cover object-top\"\n          />\n        </div>\n        <div\n          class=\"container mx-auto px-4 py-20 relative z-10 flex flex-col md:flex-row items-center\"\n        >\n          <div\n            class=\"md:w-1/2 text-white bg-pink-500 bg-opacity-80 p-8 rounded-lg backdrop-blur-sm\"\n          >\n            <h2 class=\"text-4xl font-bold mb-4\">欢迎来到 Labubu 世界</h2>\n            <p class=\"text-lg mb-6\">\n              探索可爱的 Labubu 收藏品，获取最新资讯，与收藏家社区交流互动\n            </p>\n            <div class=\"flex space-x-4\">\n              <button\n                class=\"bg-white text-pink-500 px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-gray-100 transition font-bold\"\n              >\n                <i class=\"fas fa-book-open mr-2\"></i>收藏指南\n              </button>\n              <button\n                class=\"bg-transparent border-2 border-white text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-white hover:bg-opacity-20 transition\"\n              >\n                <i class=\"fas fa-shopping-cart mr-2\"></i>购买渠道\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 最新资讯区 -->\n      <section v-if=\"activeNav === 'news'\" class=\"py-16 bg-white\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-newspaper text-pink-500 mr-2\"></i>最新资讯\n          </h2>\n\n          <!-- 轮播图 -->\n          <div class=\"mb-16\">\n            <swiper\n              class=\"rounded-xl overflow-hidden shadow-lg\"\n              :options=\"swiperOptions\"\n            >\n              <swiper-slide v-for=\"(slide, index) in newsSlides\" :key=\"index\">\n                <div class=\"relative h-96\">\n                  <img\n                    :src=\"slide.image\"\n                    :alt=\"slide.title\"\n                    class=\"w-full h-full object-cover object-top\"\n                  />\n                  <div\n                    class=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6\"\n                  >\n                    <h3 class=\"text-2xl font-bold text-white mb-2\">\n                      {{ slide.title }}\n                    </h3>\n                    <p class=\"text-white text-opacity-90\">\n                      {{ slide.description }}\n                    </p>\n                    <button\n                      class=\"mt-4 bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n                    >\n                      阅读更多\n                    </button>\n                  </div>\n                </div>\n              </swiper-slide>\n            </swiper>\n          </div>\n\n          <!-- 资讯卡片区 -->\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div\n              v-for=\"(category, index) in newsCategories\"\n              :key=\"index\"\n              class=\"bg-gray-50 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition\"\n            >\n              <div class=\"p-6\">\n                <h3\n                  class=\"text-xl font-bold mb-4 text-gray-800 flex items-center\"\n                >\n                  <i :class=\"['mr-2 text-pink-500', category.icon]\"></i>\n                  {{ category.title }}\n                </h3>\n                <ul class=\"space-y-4\">\n                  <li\n                    v-for=\"(item, itemIndex) in category.items\"\n                    :key=\"itemIndex\"\n                    class=\"border-b border-gray-200 pb-3 last:border-0\"\n                  >\n                    <a\n                      href=\"#\"\n                      class=\"text-gray-700 hover:text-pink-500 flex items-start cursor-pointer\"\n                    >\n                      <span class=\"text-pink-400 mr-2\">•</span>\n                      <span>{{ item }}</span>\n                    </a>\n                  </li>\n                </ul>\n                <button\n                  class=\"mt-4 text-pink-500 flex items-center cursor-pointer whitespace-nowrap hover:text-pink-600\"\n                >\n                  查看更多 <i class=\"fas fa-arrow-right ml-2\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 收藏指南区 -->\n      <section v-if=\"activeNav === 'guide'\" class=\"py-16 bg-gray-50\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-book text-pink-500 mr-2\"></i>收藏指南\n          </h2>\n\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            <div\n              v-for=\"(guide, index) in collectionGuides\"\n              :key=\"index\"\n              class=\"bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition transform hover:-translate-y-1\"\n            >\n              <div class=\"h-48 overflow-hidden\">\n                <img\n                  :src=\"guide.image\"\n                  :alt=\"guide.title\"\n                  class=\"w-full h-full object-cover object-top\"\n                />\n              </div>\n              <div class=\"p-6\">\n                <h3 class=\"text-xl font-bold mb-3 text-gray-800\">\n                  {{ guide.title }}\n                </h3>\n                <p class=\"text-gray-600 mb-4\">{{ guide.description }}</p>\n                <button\n                  class=\"bg-pink-500 text-white px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition w-full\"\n                >\n                  <i :class=\"['mr-2', guide.icon]\"></i>查看详情\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 鉴别技巧特别区域 -->\n          <div class=\"mt-16 bg-white rounded-xl overflow-hidden shadow-lg\">\n            <div class=\"grid grid-cols-1 md:grid-cols-2\">\n              <div class=\"p-8\">\n                <h3 class=\"text-2xl font-bold mb-4 text-gray-800\">\n                  真伪鉴别技巧\n                </h3>\n                <p class=\"text-gray-600 mb-6\">\n                  了解如何辨别 Labubu 正品与仿制品，保护您的收藏投资。\n                </p>\n                <ul class=\"space-y-3\">\n                  <li\n                    v-for=\"(tip, index) in authenticationTips\"\n                    :key=\"index\"\n                    class=\"flex items-start\"\n                  >\n                    <i class=\"fas fa-check-circle text-green-500 mt-1 mr-3\"></i>\n                    <span class=\"text-gray-700\">{{ tip }}</span>\n                  </li>\n                </ul>\n                <button\n                  class=\"mt-6 bg-pink-500 text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n                >\n                  查看完整鉴别指南\n                </button>\n              </div>\n              <div class=\"relative h-full min-h-[300px]\">\n                <img\n                  :src=\"authenticationImage\"\n                  alt=\"真伪鉴别\"\n                  class=\"absolute inset-0 w-full h-full object-cover object-top\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 社区讨论区 -->\n      <section v-if=\"activeNav === 'community'\" class=\"py-16 bg-white\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-users text-pink-500 mr-2\"></i>社区讨论\n          </h2>\n\n          <!-- 收藏品展示 -->\n          <div class=\"mb-16\">\n            <h3 class=\"text-2xl font-bold mb-8 text-gray-800\">收藏品展示</h3>\n            <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div\n                v-for=\"(item, index) in communityShowcase\"\n                :key=\"index\"\n                class=\"relative group overflow-hidden rounded-lg shadow-md cursor-pointer\"\n              >\n                <img\n                  :src=\"item.image\"\n                  :alt=\"item.title\"\n                  class=\"w-full h-64 object-cover object-top transition transform group-hover:scale-105\"\n                />\n                <div\n                  class=\"absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-100 transition flex flex-col justify-end p-4\"\n                >\n                  <h4 class=\"text-white font-bold\">{{ item.title }}</h4>\n                  <p class=\"text-white text-sm\">分享者: {{ item.user }}</p>\n                </div>\n              </div>\n            </div>\n            <div class=\"text-center mt-8\">\n              <button\n                class=\"bg-pink-500 text-white px-6 py-3 !rounded-button cursor-pointer whitespace-nowrap hover:bg-pink-600 transition\"\n              >\n                分享我的收藏\n              </button>\n            </div>\n          </div>\n\n          <!-- 热门讨论 -->\n          <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div class=\"col-span-2\">\n              <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">热门讨论</h3>\n              <div class=\"space-y-6\">\n                <div\n                  v-for=\"(discussion, index) in communityDiscussions\"\n                  :key=\"index\"\n                  class=\"bg-gray-50 rounded-xl p-6 shadow-md hover:shadow-lg transition cursor-pointer\"\n                >\n                  <div class=\"flex items-start\">\n                    <img\n                      :src=\"discussion.userAvatar\"\n                      alt=\"用户头像\"\n                      class=\"w-12 h-12 rounded-full mr-4\"\n                    />\n                    <div class=\"flex-1\">\n                      <h4 class=\"font-bold text-gray-800\">\n                        {{ discussion.title }}\n                      </h4>\n                      <p class=\"text-gray-600 mt-2\">{{ discussion.preview }}</p>\n                      <div class=\"flex items-center mt-4 text-sm text-gray-500\">\n                        <span>{{ discussion.user }}</span>\n                        <span class=\"mx-2\">•</span>\n                        <span>{{ discussion.date }}</span>\n                        <span class=\"mx-2\">•</span>\n                        <span\n                          ><i class=\"far fa-comment mr-1\"></i>{{\n                          discussion.comments }}</span\n                        >\n                        <span class=\"mx-2\">•</span>\n                        <span\n                          ><i class=\"far fa-heart mr-1\"></i>{{ discussion.likes\n                          }}</span\n                        >\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 专家问答 -->\n            <div>\n              <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">专家问答</h3>\n              <div class=\"bg-gray-50 rounded-xl p-6 shadow-md\">\n                <div class=\"space-y-6\">\n                  <div\n                    v-for=\"(qa, index) in expertQA\"\n                    :key=\"index\"\n                    class=\"border-b border-gray-200 pb-4 last:border-0\"\n                  >\n                    <div class=\"flex items-start mb-3\">\n                      <div\n                        class=\"bg-pink-100 text-pink-500 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n                      >\n                        Q\n                      </div>\n                      <p class=\"font-medium text-gray-800\">{{ qa.question }}</p>\n                    </div>\n                    <div class=\"flex items-start ml-9\">\n                      <div\n                        class=\"bg-blue-100 text-blue-500 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n                      >\n                        A\n                      </div>\n                      <p class=\"text-gray-600\">{{ qa.answer }}</p>\n                    </div>\n                  </div>\n                </div>\n                <button\n                  class=\"mt-4 w-full bg-gray-200 text-gray-700 px-4 py-2 !rounded-button cursor-pointer whitespace-nowrap hover:bg-gray-300 transition\"\n                >\n                  提问专家\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- 购买指南区 -->\n      <section v-if=\"activeNav === 'purchase'\" class=\"py-16 bg-gray-50\">\n        <div class=\"container mx-auto px-4\">\n          <h2 class=\"text-3xl font-bold text-center mb-12 text-gray-800\">\n            <i class=\"fas fa-shopping-bag text-pink-500 mr-2\"></i>购买指南\n          </h2>\n\n          <!-- 官方渠道 -->\n          <div class=\"bg-white rounded-xl shadow-lg p-8 mb-12\">\n            <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">官方渠道推荐</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div\n                v-for=\"(channel, index) in officialChannels\"\n                :key=\"index\"\n                class=\"border border-gray-200 rounded-lg p-6 hover:border-pink-300 transition cursor-pointer\"\n              >\n                <div class=\"flex items-center mb-4\">\n                  <i :class=\"['text-2xl mr-3 text-pink-500', channel.icon]\"></i>\n                  <h4 class=\"text-xl font-bold text-gray-800\">\n                    {{ channel.name }}\n                  </h4>\n                </div>\n                <p class=\"text-gray-600 mb-4\">{{ channel.description }}</p>\n                <div class=\"flex items-center text-sm text-gray-500\">\n                  <i class=\"fas fa-check-circle text-green-500 mr-2\"></i>\n                  <span>{{ channel.verification }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 价格趋势 -->\n          <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-12\">\n            <div class=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 class=\"text-xl font-bold mb-6 text-gray-800\">价格趋势分析</h3>\n              <div ref=\"priceChart\" class=\"h-80\"></div>\n            </div>\n\n            <div class=\"bg-white rounded-xl shadow-lg p-6\">\n              <h3 class=\"text-xl font-bold mb-6 text-gray-800\">购买注意事项</h3>\n              <ul class=\"space-y-4\">\n                <li\n                  v-for=\"(tip, index) in purchaseTips\"\n                  :key=\"index\"\n                  class=\"flex items-start\"\n                >\n                  <div\n                    class=\"bg-pink-100 rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-1\"\n                  >\n                    <span class=\"text-pink-500 font-bold\">{{ index + 1 }}</span>\n                  </div>\n                  <div>\n                    <h4 class=\"font-bold text-gray-800\">{{ tip.title }}</h4>\n                    <p class=\"text-gray-600\">{{ tip.content }}</p>\n                  </div>\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          <!-- 防骗指南 -->\n          <div class=\"bg-white rounded-xl shadow-lg p-8\">\n            <h3 class=\"text-2xl font-bold mb-6 text-gray-800\">防骗指南</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n              <div>\n                <p class=\"text-gray-600 mb-6\">\n                  在购买 Labubu 收藏品时，请警惕以下常见骗局，保护您的权益。\n                </p>\n                <div class=\"space-y-4\">\n                  <div\n                    v-for=\"(scam, index) in antiScamGuide\"\n                    :key=\"index\"\n                    class=\"bg-red-50 border-l-4 border-red-500 p-4\"\n                  >\n                    <h4 class=\"font-bold text-red-600 mb-1\">\n                      {{ scam.title }}\n                    </h4>\n                    <p class=\"text-gray-700\">{{ scam.description }}</p>\n                  </div>\n                </div>\n              </div>\n              <div class=\"relative h-80\">\n                <img\n                  :src=\"antiScamImage\"\n                  alt=\"防骗指南\"\n                  class=\"w-full h-full object-cover object-top rounded-lg\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </main>\n\n    <!-- 页脚 -->\n    <footer class=\"bg-gray-800 text-white py-12\">\n      <div class=\"container mx-auto px-4\">\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">关于 Labubu</h3>\n            <p class=\"text-gray-400\">\n              Labubu\n              是一款备受喜爱的可爱卡通收藏品，以其独特的设计和高品质而闻名。\n            </p>\n          </div>\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">快速链接</h3>\n            <ul class=\"space-y-2\">\n              <li v-for=\"(item, index) in navItems\" :key=\"index\">\n                <a\n                  href=\"#\"\n                  class=\"text-gray-400 hover:text-pink-300 cursor-pointer\"\n                  >{{ item.name }}</a\n                >\n              </li>\n              <li>\n                <a\n                  href=\"#\"\n                  class=\"text-gray-400 hover:text-pink-300 cursor-pointer\"\n                  >关于我们</a\n                >\n              </li>\n              <li>\n                <a\n                  href=\"#\"\n                  class=\"text-gray-400 hover:text-pink-300 cursor-pointer\"\n                  >联系我们</a\n                >\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">联系方式</h3>\n            <ul class=\"space-y-2 text-gray-400\">\n              <li class=\"flex items-center\">\n                <i class=\"fas fa-envelope mr-2 text-pink-400\"></i>\n                <EMAIL>\n              </li>\n              <li class=\"flex items-center\">\n                <i class=\"fas fa-phone mr-2 text-pink-400\"></i> +86 123 4567\n                8910\n              </li>\n              <li class=\"flex items-center\">\n                <i class=\"fas fa-map-marker-alt mr-2 text-pink-400\"></i>\n                上海市浦东新区\n              </li>\n            </ul>\n          </div>\n          <div>\n            <h3 class=\"text-xl font-bold mb-4\">关注我们</h3>\n            <div class=\"flex space-x-4\">\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-weibo\"></i>\n              </a>\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-weixin\"></i>\n              </a>\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-instagram\"></i>\n              </a>\n              <a\n                href=\"#\"\n                class=\"w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center hover:bg-pink-500 transition cursor-pointer\"\n              >\n                <i class=\"fab fa-bilibili\"></i>\n              </a>\n            </div>\n            <div class=\"mt-4\">\n              <p class=\"text-gray-400\">支付方式</p>\n              <div class=\"flex space-x-3 mt-2\">\n                <i class=\"fab fa-alipay text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-weixin text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-cc-visa text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-cc-mastercard text-2xl text-gray-400\"></i>\n                <i class=\"fab fa-paypal text-2xl text-gray-400\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div\n          class=\"border-t border-gray-700 mt-8 pt-8 text-center text-gray-500\"\n        >\n          <p>&copy; 2025 Labubu Portal. 保留所有权利。</p>\n        </div>\n      </div>\n    </footer>\n  </div>\n</template>\n\n<script>\nimport { Swiper, SwiperSlide, directive } from \"vue-awesome-swiper\";\nimport * as echarts from \"echarts\";\n\nexport default {\n  components: {\n    Swiper,\n    SwiperSlide,\n  },\n  directives: {\n    swiper: directive,\n  },\n  data() {\n    return {\n      activeNav: \"news\",\n      logoUrl:\n        \"https://readdy.ai/api/search-image?query=cute%20cartoon%20logo%20for%20Labubu%2C%20minimalist%2C%20pastel%20colors%2C%20simple%20design%2C%20kawaii%20style%2C%20rounded%20edges%2C%20pink%20and%20mint%20color%20palette%2C%20white%20background%2C%20high%20quality&width=200&height=200&seq=1&orientation=squarish\",\n      heroBackgroundUrl:\n        \"https://readdy.ai/api/search-image?query=cute%20cartoon%20characters%20collection%2C%20pastel%20colors%2C%20kawaii%20style%2C%20soft%20gradient%20background%2C%20pink%20and%20mint%20color%20palette%2C%20adorable%20fantasy%20creatures%2C%20high%20quality%20illustration%2C%20simple%20and%20clean%20design&width=1440&height=500&seq=2&orientation=landscape\",\n      navItems: [\n        { id: \"news\", name: \"最新资讯\", icon: \"fas fa-newspaper\" },\n        { id: \"guide\", name: \"收藏指南\", icon: \"fas fa-book\" },\n        { id: \"community\", name: \"社区讨论\", icon: \"fas fa-users\" },\n        { id: \"purchase\", name: \"购买指南\", icon: \"fas fa-shopping-bag\" },\n      ],\n      swiperOptions: {\n        slidesPerView: 1,\n        spaceBetween: 0,\n        loop: true,\n        autoplay: {\n          delay: 5000,\n          disableOnInteraction: false,\n        },\n        pagination: {\n          el: \".swiper-pagination\",\n          clickable: true,\n        },\n        navigation: {\n          nextEl: \".swiper-button-next\",\n          prevEl: \".swiper-button-prev\",\n        },\n      },\n      newsSlides: [\n        {\n          title: \"Labubu 2025 限量版系列即将发布\",\n          description:\n            \"全新限量版 Labubu 系列将于下月发布，包含 5 款全新角色设计\",\n          image:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20figurine%20collection%20on%20display%2C%20pastel%20colors%2C%20kawaii%20style%2C%20soft%20lighting%2C%20professional%20product%20photography%2C%20clean%20background%2C%20high%20quality%2C%20detailed%20figurines&width=1200&height=600&seq=3&orientation=landscape\",\n        },\n        {\n          title: \"第三届 Labubu 收藏家大会圆满结束\",\n          description: \"来自全球的 Labubu 爱好者齐聚上海，分享收藏心得与经验\",\n          image:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20convention%20with%20people%20displaying%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20exhibition%20hall%2C%20collectors%20meeting%2C%20bright%20and%20colorful%20atmosphere%2C%20high%20quality&width=1200&height=600&seq=4&orientation=landscape\",\n        },\n        {\n          title: \"Labubu 艺术家签售会即将举行\",\n          description: \"原创设计师将在北京、上海、广州三地举办签售会\",\n          image:\n            \"https://readdy.ai/api/search-image?query=artist%20signing%20cute%20cartoon%20figurines%2C%20fans%20waiting%20in%20line%2C%20pastel%20colors%2C%20kawaii%20style%2C%20bright%20indoor%20setting%2C%20art%20gallery%20atmosphere%2C%20high%20quality&width=1200&height=600&seq=5&orientation=landscape\",\n        },\n      ],\n      newsCategories: [\n        {\n          title: \"官方资讯\",\n          icon: \"fas fa-bullhorn\",\n          items: [\n            \"Labubu 2025 夏季限定款预售开启\",\n            \"设计师访谈：Labubu 灵感来源揭秘\",\n            \"官方商城六一儿童节特别活动\",\n            \"新品发布：Labubu 海洋系列\",\n          ],\n        },\n        {\n          title: \"行业动态\",\n          icon: \"fas fa-chart-line\",\n          items: [\n            \"2025 年潮玩市场发展趋势分析\",\n            \"Labubu 在国际收藏市场的地位\",\n            \"潮玩收藏品投资价值研究报告\",\n            \"全球限量版收藏品拍卖记录\",\n          ],\n        },\n        {\n          title: \"社区热点\",\n          icon: \"fas fa-fire\",\n          items: [\n            \"收藏家故事：我与 Labubu 的十年之约\",\n            \"社区投票：最受欢迎 Labubu 系列\",\n            \"粉丝创作：Labubu 同人作品展示\",\n            \"线下活动：各地 Labubu 粉丝聚会\",\n          ],\n        },\n      ],\n      collectionGuides: [\n        {\n          title: \"新手入门指南\",\n          description: \"从零开始了解 Labubu 收藏，掌握基础知识和收藏技巧\",\n          image:\n            \"https://readdy.ai/api/search-image?query=beginner%20guide%20to%20collecting%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20educational%20illustration%2C%20simple%20steps%20shown%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=6&orientation=landscape\",\n          icon: \"fas fa-star\",\n        },\n        {\n          title: \"真伪鉴别技巧\",\n          description: \"学习如何辨别 Labubu 正品与仿制品，保护您的收藏投资\",\n          image:\n            \"https://readdy.ai/api/search-image?query=comparing%20authentic%20vs%20fake%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20side%20by%20side%20comparison%2C%20magnifying%20glass%2C%20detailed%20differences%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=7&orientation=landscape\",\n          icon: \"fas fa-check-circle\",\n        },\n        {\n          title: \"收藏保养方法\",\n          description: \"专业的 Labubu 收藏品保养技巧，让您的收藏保持最佳状态\",\n          image:\n            \"https://readdy.ai/api/search-image?query=caring%20for%20cute%20cartoon%20figurines%2C%20cleaning%20and%20maintenance%2C%20pastel%20colors%2C%20kawaii%20style%2C%20gentle%20handling%2C%20display%20case%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=8&orientation=landscape\",\n          icon: \"fas fa-heart\",\n        },\n        {\n          title: \"投资价值分析\",\n          description: \"深入分析 Labubu 收藏品的市场价值和投资潜力\",\n          image:\n            \"https://readdy.ai/api/search-image?query=investment%20value%20chart%20with%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20financial%20graphs%2C%20upward%20trends%2C%20clean%20background%2C%20high%20quality&width=400&height=300&seq=9&orientation=landscape\",\n          icon: \"fas fa-chart-line\",\n        },\n      ],\n      authenticationTips: [\n        \"检查包装盒上的官方防伪标识和序列号\",\n        \"注意 Labubu 的材质质感和细节处理\",\n        \"辨别正品特有的颜色饱和度和色彩过渡\",\n        \"了解不同系列的限量编号规则\",\n        \"通过官方渠道验证产品真伪\",\n      ],\n      authenticationImage:\n        \"https://readdy.ai/api/search-image?query=authentication%20guide%20for%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20showing%20details%20for%20verification%2C%20magnifying%20glass%20examining%20details%2C%20clean%20background%2C%20high%20quality&width=600&height=600&seq=10&orientation=squarish\",\n      communityShowcase: [\n        {\n          title: \"梦幻系列全收藏\",\n          user: \"星辰收藏家\",\n          image:\n            \"https://readdy.ai/api/search-image?query=complete%20collection%20of%20cute%20cartoon%20figurines%20on%20display%20shelf%2C%20pastel%20colors%2C%20kawaii%20style%2C%20dream%20series%2C%20organized%20display%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=11&orientation=squarish\",\n        },\n        {\n          title: \"限量版珍藏\",\n          user: \"稀有收集者\",\n          image:\n            \"https://readdy.ai/api/search-image?query=limited%20edition%20cute%20cartoon%20figurines%20in%20display%20case%2C%20pastel%20colors%2C%20kawaii%20style%2C%20rare%20collectibles%2C%20special%20lighting%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=12&orientation=squarish\",\n        },\n        {\n          title: \"海洋主题系列\",\n          user: \"蓝色梦想\",\n          image:\n            \"https://readdy.ai/api/search-image?query=ocean%20themed%20cute%20cartoon%20figurines%20collection%2C%20pastel%20colors%2C%20kawaii%20style%2C%20blue%20color%20palette%2C%20sea%20creatures%20design%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=13&orientation=squarish\",\n        },\n        {\n          title: \"节日特别版\",\n          user: \"四季收藏家\",\n          image:\n            \"https://readdy.ai/api/search-image?query=holiday%20special%20edition%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20festive%20themed%2C%20christmas%20and%20halloween%20designs%2C%20clean%20background%2C%20high%20quality&width=400&height=400&seq=14&orientation=squarish\",\n        },\n      ],\n      communityDiscussions: [\n        {\n          title: \"分享我的 Labubu 收藏之旅：从入门到痴迷\",\n          preview:\n            \"作为一名收藏 Labubu 已有五年的老粉，想和大家分享我的收藏经验和心得...\",\n          user: \"粉红收藏家\",\n          userAvatar:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20female%20character%2C%20pastel%20pink%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=15&orientation=squarish\",\n          date: \"2025-06-15\",\n          comments: 42,\n          likes: 156,\n        },\n        {\n          title: \"求助：如何为 Labubu 收藏品打造完美展示空间？\",\n          preview:\n            \"最近我的收藏越来越多，想请教各位大神如何设计一个既美观又实用的展示柜...\",\n          user: \"新手收藏者\",\n          userAvatar:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20male%20character%2C%20pastel%20blue%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=16&orientation=squarish\",\n          date: \"2025-06-14\",\n          comments: 28,\n          likes: 73,\n        },\n        {\n          title: \"讨论：2025年最值得投资的 Labubu 系列是哪个？\",\n          preview:\n            \"根据市场趋势和历年数据分析，我认为今年最具投资潜力的系列是...\",\n          user: \"投资分析师\",\n          userAvatar:\n            \"https://readdy.ai/api/search-image?query=cute%20cartoon%20avatar%2C%20professional%20character%20with%20glasses%2C%20pastel%20green%2C%20kawaii%20style%2C%20simple%20design%2C%20clean%20background%2C%20high%20quality&width=100&height=100&seq=17&orientation=squarish\",\n          date: \"2025-06-12\",\n          comments: 56,\n          likes: 210,\n        },\n      ],\n      expertQA: [\n        {\n          question: \"如何判断早期 Labubu 系列的收藏价值？\",\n          answer:\n            \"早期系列的价值主要看限量程度、保存状态和市场需求。建议关注官方发行量数据，以及历年拍卖成交价格走势。\",\n        },\n        {\n          question: \"Labubu 收藏品应该拆盒展示还是保持原包装？\",\n          answer:\n            \"从保值角度看，保持原包装完好更有利于维持价值。但个人收藏可根据自己喜好，拆盒展示能更好欣赏细节。\",\n        },\n        {\n          question: \"如何防止 Labubu 收藏品褪色或变形？\",\n          answer:\n            \"避免阳光直射、高温和潮湿环境，定期用软毛刷轻轻除尘，不要用湿布擦拭，展示柜最好选择防紫外线玻璃。\",\n        },\n      ],\n      officialChannels: [\n        {\n          name: \"官方网店\",\n          icon: \"fas fa-store\",\n          description: \"Labubu 官方授权网店，提供最新发布和限量版收藏品\",\n          verification: \"官方认证销售渠道\",\n        },\n        {\n          name: \"授权实体店\",\n          icon: \"fas fa-map-marker-alt\",\n          description: \"遍布全国的 Labubu 授权实体店，可实地挑选和购买\",\n          verification: \"线下官方授权店铺\",\n        },\n        {\n          name: \"特许经销商\",\n          icon: \"fas fa-certificate\",\n          description: \"经过严格筛选的特许经销商，确保产品正品保障\",\n          verification: \"官方认证合作伙伴\",\n        },\n      ],\n      purchaseTips: [\n        {\n          title: \"关注发售时间\",\n          content:\n            \"限量版 Labubu 通常会在特定时间发售，提前关注官方公告，设置提醒避免错过。\",\n        },\n        {\n          title: \"验证销售渠道\",\n          content:\n            \"只从官方认证的渠道购买，避免购买到假冒产品，保障收藏品的真实性和价值。\",\n        },\n        {\n          title: \"检查包装完整性\",\n          content:\n            \"收到商品后，仔细检查包装是否完好，防伪标识是否清晰，附件是否齐全。\",\n        },\n        {\n          title: \"保留购买凭证\",\n          content:\n            \"妥善保管购买凭证和收据，这对于后续鉴定真伪和转售都非常重要。\",\n        },\n      ],\n      antiScamGuide: [\n        {\n          title: \"虚假预售骗局\",\n          description:\n            \"谨防声称可预订未发布系列的卖家，官方预售只通过官方渠道进行。\",\n        },\n        {\n          title: \"以次充好\",\n          description:\n            \"警惕价格明显低于市场的商品，可能是仿制品或有质量问题的次品。\",\n        },\n        {\n          title: \"虚假限量编号\",\n          description: \"核实限量版编号的真实性，某些骗子会伪造限量编号标签。\",\n        },\n      ],\n      antiScamImage:\n        \"https://readdy.ai/api/search-image?query=anti-scam%20guide%20for%20buying%20cute%20cartoon%20figurines%2C%20pastel%20colors%2C%20kawaii%20style%2C%20warning%20signs%2C%20security%20verification%2C%20safe%20shopping%20tips%2C%20clean%20background%2C%20high%20quality&width=600&height=500&seq=18&orientation=landscape\",\n    };\n  },\n  mounted() {\n    this.initPriceChart();\n  },\n  methods: {\n    initPriceChart() {\n      const chartDom = this.$refs.priceChart;\n      const myChart = echarts.init(chartDom);\n\n      const option = {\n        animation: false,\n        title: {\n          text: \"Labubu 系列价格趋势 (2023-2025)\",\n          left: \"center\",\n          textStyle: {\n            color: \"#333\",\n          },\n        },\n        tooltip: {\n          trigger: \"axis\",\n        },\n        legend: {\n          data: [\"限量版系列\", \"常规系列\", \"特别合作款\"],\n          bottom: 0,\n        },\n        grid: {\n          left: \"3%\",\n          right: \"4%\",\n          bottom: \"15%\",\n          top: \"15%\",\n          containLabel: true,\n        },\n        xAxis: {\n          type: \"category\",\n          boundaryGap: false,\n          data: [\n            \"2023 Q1\",\n            \"2023 Q2\",\n            \"2023 Q3\",\n            \"2023 Q4\",\n            \"2024 Q1\",\n            \"2024 Q2\",\n            \"2024 Q3\",\n            \"2024 Q4\",\n            \"2025 Q1\",\n            \"2025 Q2\",\n          ],\n        },\n        yAxis: {\n          type: \"value\",\n          name: \"价格 (¥)\",\n          nameLocation: \"end\",\n        },\n        series: [\n          {\n            name: \"限量版系列\",\n            type: \"line\",\n            data: [1200, 1350, 1500, 1800, 2100, 2400, 2700, 3100, 3500, 3800],\n            smooth: true,\n            lineStyle: {\n              color: \"#ff6b81\",\n            },\n            itemStyle: {\n              color: \"#ff6b81\",\n            },\n          },\n          {\n            name: \"常规系列\",\n            type: \"line\",\n            data: [300, 320, 350, 370, 390, 410, 430, 450, 480, 500],\n            smooth: true,\n            lineStyle: {\n              color: \"#70a1ff\",\n            },\n            itemStyle: {\n              color: \"#70a1ff\",\n            },\n          },\n          {\n            name: \"特别合作款\",\n            type: \"line\",\n            data: [800, 950, 1100, 1300, 1500, 1650, 1800, 2000, 2300, 2600],\n            smooth: true,\n            lineStyle: {\n              color: \"#7bed9f\",\n            },\n            itemStyle: {\n              color: \"#7bed9f\",\n            },\n          },\n        ],\n      };\n\n      myChart.setOption(option);\n\n      window.addEventListener(\"resize\", () => {\n        myChart.resize();\n      });\n    },\n  },\n};\n</script>\n\n<style scoped>\n.nav-link {\n  position: relative;\n  padding-bottom: 4px;\n}\n\n.nav-link::after {\n  content: \"\";\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background-color: #ec4899;\n  transition: width 0.3s ease;\n}\n\n.nav-link:hover::after,\n.nav-link.active::after {\n  width: 100%;\n}\n\ninput[type=\"number\"]::-webkit-inner-spin-button,\ninput[type=\"number\"]::-webkit-outer-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\n.labubu-card {\n  transition:\n    transform 0.3s ease,\n    box-shadow 0.3s ease;\n}\n\n.labubu-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n}\n</style>\n"], "mappings": "AA2iBA,SAASA,MAAM,EAAEC,WAAW,EAAEC,SAAQ,QAAS,oBAAoB;AACnE,OAAO,KAAKC,OAAM,MAAO,SAAS;AAElC,eAAe;EACbC,UAAU,EAAE;IACVJ,MAAM;IACNC;EACF,CAAC;EACDI,UAAU,EAAE;IACVC,MAAM,EAAEJ;EACV,CAAC;EACDK,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,MAAM;MACjBC,OAAO,EACL,uTAAuT;MACzTC,iBAAiB,EACf,mWAAmW;MACrWC,QAAQ,EAAE,CACR;QAAEC,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAmB,CAAC,EACtD;QAAEF,EAAE,EAAE,OAAO;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAc,CAAC,EAClD;QAAEF,EAAE,EAAE,WAAW;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAe,CAAC,EACvD;QAAEF,EAAE,EAAE,UAAU;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAsB,CAAC,CAC9D;MACDC,aAAa,EAAE;QACbC,aAAa,EAAE,CAAC;QAChBC,YAAY,EAAE,CAAC;QACfC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE;UACRC,KAAK,EAAE,IAAI;UACXC,oBAAoB,EAAE;QACxB,CAAC;QACDC,UAAU,EAAE;UACVC,EAAE,EAAE,oBAAoB;UACxBC,SAAS,EAAE;QACb,CAAC;QACDC,UAAU,EAAE;UACVC,MAAM,EAAE,qBAAqB;UAC7BC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,UAAU,EAAE,CACV;QACEC,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EACT,oCAAoC;QACtCC,KAAK,EACH;MACJ,CAAC,EACD;QACEF,KAAK,EAAE,sBAAsB;QAC7BC,WAAW,EAAE,gCAAgC;QAC7CC,KAAK,EACH;MACJ,CAAC,EACD;QACEF,KAAK,EAAE,mBAAmB;QAC1BC,WAAW,EAAE,wBAAwB;QACrCC,KAAK,EACH;MACJ,CAAC,CACF;MACDC,cAAc,EAAE,CACd;QACEH,KAAK,EAAE,MAAM;QACbf,IAAI,EAAE,iBAAiB;QACvBmB,KAAK,EAAE,CACL,uBAAuB,EACvB,qBAAqB,EACrB,eAAe,EACf,kBAAkB;MAEtB,CAAC,EACD;QACEJ,KAAK,EAAE,MAAM;QACbf,IAAI,EAAE,mBAAmB;QACzBmB,KAAK,EAAE,CACL,kBAAkB,EAClB,mBAAmB,EACnB,eAAe,EACf,cAAc;MAElB,CAAC,EACD;QACEJ,KAAK,EAAE,MAAM;QACbf,IAAI,EAAE,aAAa;QACnBmB,KAAK,EAAE,CACL,uBAAuB,EACvB,qBAAqB,EACrB,oBAAoB,EACpB,qBAAqB;MAEzB,CAAC,CACF;MACDC,gBAAgB,EAAE,CAChB;QACEL,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,8BAA8B;QAC3CC,KAAK,EACH,4SAA4S;QAC9SjB,IAAI,EAAE;MACR,CAAC,EACD;QACEe,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,+BAA+B;QAC5CC,KAAK,EACH,sUAAsU;QACxUjB,IAAI,EAAE;MACR,CAAC,EACD;QACEe,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,gCAAgC;QAC7CC,KAAK,EACH,uSAAuS;QACzSjB,IAAI,EAAE;MACR,CAAC,EACD;QACEe,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,2BAA2B;QACxCC,KAAK,EACH,4RAA4R;QAC9RjB,IAAI,EAAE;MACR,CAAC,CACF;MACDqB,kBAAkB,EAAE,CAClB,mBAAmB,EACnB,sBAAsB,EACtB,mBAAmB,EACnB,eAAe,EACf,cAAc,CACf;MACDC,mBAAmB,EACjB,oUAAoU;MACtUC,iBAAiB,EAAE,CACjB;QACER,KAAK,EAAE,SAAS;QAChBS,IAAI,EAAE,OAAO;QACbP,KAAK,EACH;MACJ,CAAC,EACD;QACEF,KAAK,EAAE,OAAO;QACdS,IAAI,EAAE,OAAO;QACbP,KAAK,EACH;MACJ,CAAC,EACD;QACEF,KAAK,EAAE,QAAQ;QACfS,IAAI,EAAE,MAAM;QACZP,KAAK,EACH;MACJ,CAAC,EACD;QACEF,KAAK,EAAE,OAAO;QACdS,IAAI,EAAE,OAAO;QACbP,KAAK,EACH;MACJ,CAAC,CACF;MACDQ,oBAAoB,EAAE,CACpB;QACEV,KAAK,EAAE,yBAAyB;QAChCW,OAAO,EACL,0CAA0C;QAC5CF,IAAI,EAAE,OAAO;QACbG,UAAU,EACR,mPAAmP;QACrPC,IAAI,EAAE,YAAY;QAClBC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACEf,KAAK,EAAE,4BAA4B;QACnCW,OAAO,EACL,uCAAuC;QACzCF,IAAI,EAAE,OAAO;QACbG,UAAU,EACR,iPAAiP;QACnPC,IAAI,EAAE,YAAY;QAClBC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACEf,KAAK,EAAE,8BAA8B;QACrCW,OAAO,EACL,kCAAkC;QACpCF,IAAI,EAAE,OAAO;QACbG,UAAU,EACR,2QAA2Q;QAC7QC,IAAI,EAAE,YAAY;QAClBC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MACT,CAAC,CACF;MACDC,QAAQ,EAAE,CACR;QACEC,QAAQ,EAAE,wBAAwB;QAClCC,MAAM,EACJ;MACJ,CAAC,EACD;QACED,QAAQ,EAAE,0BAA0B;QACpCC,MAAM,EACJ;MACJ,CAAC,EACD;QACED,QAAQ,EAAE,uBAAuB;QACjCC,MAAM,EACJ;MACJ,CAAC,CACF;MACDC,gBAAgB,EAAE,CAChB;QACEnC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,cAAc;QACpBgB,WAAW,EAAE,6BAA6B;QAC1CmB,YAAY,EAAE;MAChB,CAAC,EACD;QACEpC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,uBAAuB;QAC7BgB,WAAW,EAAE,6BAA6B;QAC1CmB,YAAY,EAAE;MAChB,CAAC,EACD;QACEpC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,oBAAoB;QAC1BgB,WAAW,EAAE,uBAAuB;QACpCmB,YAAY,EAAE;MAChB,CAAC,CACF;MACDC,YAAY,EAAE,CACZ;QACErB,KAAK,EAAE,QAAQ;QACfsB,OAAO,EACL;MACJ,CAAC,EACD;QACEtB,KAAK,EAAE,QAAQ;QACfsB,OAAO,EACL;MACJ,CAAC,EACD;QACEtB,KAAK,EAAE,SAAS;QAChBsB,OAAO,EACL;MACJ,CAAC,EACD;QACEtB,KAAK,EAAE,QAAQ;QACfsB,OAAO,EACL;MACJ,CAAC,CACF;MACDC,aAAa,EAAE,CACb;QACEvB,KAAK,EAAE,QAAQ;QACfC,WAAW,EACT;MACJ,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,WAAW,EACT;MACJ,CAAC,EACD;QACED,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE;MACf,CAAC,CACF;MACDuB,aAAa,EACX;IACJ,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB,CAAC;EACDC,OAAO,EAAE;IACPD,cAAcA,CAAA,EAAG;MACf,MAAME,QAAO,GAAI,IAAI,CAACC,KAAK,CAACC,UAAU;MACtC,MAAMC,OAAM,GAAIzD,OAAO,CAAC0D,IAAI,CAACJ,QAAQ,CAAC;MAEtC,MAAMK,MAAK,GAAI;QACbC,SAAS,EAAE,KAAK;QAChBlC,KAAK,EAAE;UACLmC,IAAI,EAAE,2BAA2B;UACjCC,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE;YACTC,KAAK,EAAE;UACT;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,OAAO,EAAE;QACX,CAAC;QACDC,MAAM,EAAE;UACN/D,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;UAChCgE,MAAM,EAAE;QACV,CAAC;QACDC,IAAI,EAAE;UACJP,IAAI,EAAE,IAAI;UACVQ,KAAK,EAAE,IAAI;UACXF,MAAM,EAAE,KAAK;UACbG,GAAG,EAAE,KAAK;UACVC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLC,IAAI,EAAE,UAAU;UAChBC,WAAW,EAAE,KAAK;UAClBvE,IAAI,EAAE,CACJ,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS;QAEb,CAAC;QACDwE,KAAK,EAAE;UACLF,IAAI,EAAE,OAAO;UACbhE,IAAI,EAAE,QAAQ;UACdmE,YAAY,EAAE;QAChB,CAAC;QACDC,MAAM,EAAE,CACN;UACEpE,IAAI,EAAE,OAAO;UACbgE,IAAI,EAAE,MAAM;UACZtE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UAClE2E,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;YACThB,KAAK,EAAE;UACT,CAAC;UACDiB,SAAS,EAAE;YACTjB,KAAK,EAAE;UACT;QACF,CAAC,EACD;UACEtD,IAAI,EAAE,MAAM;UACZgE,IAAI,EAAE,MAAM;UACZtE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UACxD2E,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;YACThB,KAAK,EAAE;UACT,CAAC;UACDiB,SAAS,EAAE;YACTjB,KAAK,EAAE;UACT;QACF,CAAC,EACD;UACEtD,IAAI,EAAE,OAAO;UACbgE,IAAI,EAAE,MAAM;UACZtE,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UAChE2E,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;YACThB,KAAK,EAAE;UACT,CAAC;UACDiB,SAAS,EAAE;YACTjB,KAAK,EAAE;UACT;QACF,CAAC;MAEL,CAAC;MAEDP,OAAO,CAACyB,SAAS,CAACvB,MAAM,CAAC;MAEzBwB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC3B,OAAO,CAAC4B,MAAM,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}