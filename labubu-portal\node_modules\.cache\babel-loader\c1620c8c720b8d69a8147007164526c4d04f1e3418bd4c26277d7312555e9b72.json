{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as axisPointerModelHelper from '../axisPointer/modelHelper.js';\nimport ComponentView from '../../view/Component.js';\nvar axisPointerClazz = {};\n/**\r\n * Base class of AxisView.\r\n */\nvar AxisView = /** @class */function (_super) {\n  __extends(AxisView, _super);\n  function AxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisView.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    // FIXME\n    // This process should proformed after coordinate systems updated\n    // (axis scale updated), and should be performed each time update.\n    // So put it here temporarily, although it is not appropriate to\n    // put a model-writing procedure in `view`.\n    this.axisPointerClass && axisPointerModelHelper.fixValue(axisModel);\n    _super.prototype.render.apply(this, arguments);\n    this._doUpdateAxisPointerClass(axisModel, api, true);\n  };\n  /**\r\n   * Action handler.\r\n   */\n  AxisView.prototype.updateAxisPointer = function (axisModel, ecModel, api, payload) {\n    this._doUpdateAxisPointerClass(axisModel, api, false);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.remove = function (ecModel, api) {\n    var axisPointer = this._axisPointer;\n    axisPointer && axisPointer.remove(api);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.dispose = function (ecModel, api) {\n    this._disposeAxisPointer(api);\n    _super.prototype.dispose.apply(this, arguments);\n  };\n  AxisView.prototype._doUpdateAxisPointerClass = function (axisModel, api, forceRender) {\n    var Clazz = AxisView.getAxisPointerClass(this.axisPointerClass);\n    if (!Clazz) {\n      return;\n    }\n    var axisPointerModel = axisPointerModelHelper.getAxisPointerModel(axisModel);\n    axisPointerModel ? (this._axisPointer || (this._axisPointer = new Clazz())).render(axisModel, axisPointerModel, api, forceRender) : this._disposeAxisPointer(api);\n  };\n  AxisView.prototype._disposeAxisPointer = function (api) {\n    this._axisPointer && this._axisPointer.dispose(api);\n    this._axisPointer = null;\n  };\n  AxisView.registerAxisPointerClass = function (type, clazz) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (axisPointerClazz[type]) {\n        throw new Error('axisPointer ' + type + ' exists');\n      }\n    }\n    axisPointerClazz[type] = clazz;\n  };\n  ;\n  AxisView.getAxisPointerClass = function (type) {\n    return type && axisPointerClazz[type];\n  };\n  ;\n  AxisView.type = 'axis';\n  return AxisView;\n}(ComponentView);\nexport default AxisView;", "map": {"version": 3, "names": ["__extends", "axisPointerModelHelper", "ComponentView", "axisPointerClazz", "AxisView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "axisModel", "ecModel", "api", "payload", "axisPointerClass", "fixValue", "_doUpdateAxisPointerClass", "updateAxisPointer", "remove", "axisPointer", "_axisPointer", "dispose", "_disposeAxisPointer", "forceRender", "Clazz", "getAxisPointerClass", "axisPointerModel", "getAxisPointerModel", "registerAxisPointerClass", "clazz", "process", "env", "NODE_ENV", "Error"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/axis/AxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as axisPointerModelHelper from '../axisPointer/modelHelper.js';\nimport ComponentView from '../../view/Component.js';\nvar axisPointerClazz = {};\n/**\r\n * Base class of AxisView.\r\n */\nvar AxisView = /** @class */function (_super) {\n  __extends(AxisView, _super);\n  function AxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisView.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    // FIXME\n    // This process should proformed after coordinate systems updated\n    // (axis scale updated), and should be performed each time update.\n    // So put it here temporarily, although it is not appropriate to\n    // put a model-writing procedure in `view`.\n    this.axisPointerClass && axisPointerModelHelper.fixValue(axisModel);\n    _super.prototype.render.apply(this, arguments);\n    this._doUpdateAxisPointerClass(axisModel, api, true);\n  };\n  /**\r\n   * Action handler.\r\n   */\n  AxisView.prototype.updateAxisPointer = function (axisModel, ecModel, api, payload) {\n    this._doUpdateAxisPointerClass(axisModel, api, false);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.remove = function (ecModel, api) {\n    var axisPointer = this._axisPointer;\n    axisPointer && axisPointer.remove(api);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.dispose = function (ecModel, api) {\n    this._disposeAxisPointer(api);\n    _super.prototype.dispose.apply(this, arguments);\n  };\n  AxisView.prototype._doUpdateAxisPointerClass = function (axisModel, api, forceRender) {\n    var Clazz = AxisView.getAxisPointerClass(this.axisPointerClass);\n    if (!Clazz) {\n      return;\n    }\n    var axisPointerModel = axisPointerModelHelper.getAxisPointerModel(axisModel);\n    axisPointerModel ? (this._axisPointer || (this._axisPointer = new Clazz())).render(axisModel, axisPointerModel, api, forceRender) : this._disposeAxisPointer(api);\n  };\n  AxisView.prototype._disposeAxisPointer = function (api) {\n    this._axisPointer && this._axisPointer.dispose(api);\n    this._axisPointer = null;\n  };\n  AxisView.registerAxisPointerClass = function (type, clazz) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (axisPointerClazz[type]) {\n        throw new Error('axisPointer ' + type + ' exists');\n      }\n    }\n    axisPointerClazz[type] = clazz;\n  };\n  ;\n  AxisView.getAxisPointerClass = function (type) {\n    return type && axisPointerClazz[type];\n  };\n  ;\n  AxisView.type = 'axis';\n  return AxisView;\n}(ComponentView);\nexport default AxisView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,sBAAsB,MAAM,+BAA+B;AACvE,OAAOC,aAAa,MAAM,yBAAyB;AACnD,IAAIC,gBAAgB,GAAG,CAAC,CAAC;AACzB;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC5CL,SAAS,CAACI,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,QAAQ,CAACK,IAAI;IAC1B,OAAOH,KAAK;EACd;EACA;AACF;AACA;EACEF,QAAQ,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACtE;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,gBAAgB,IAAIf,sBAAsB,CAACgB,QAAQ,CAACL,SAAS,CAAC;IACnEP,MAAM,CAACK,SAAS,CAACC,MAAM,CAACJ,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC9C,IAAI,CAACU,yBAAyB,CAACN,SAAS,EAAEE,GAAG,EAAE,IAAI,CAAC;EACtD,CAAC;EACD;AACF;AACA;EACEV,QAAQ,CAACM,SAAS,CAACS,iBAAiB,GAAG,UAAUP,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACjF,IAAI,CAACG,yBAAyB,CAACN,SAAS,EAAEE,GAAG,EAAE,KAAK,CAAC;EACvD,CAAC;EACD;AACF;AACA;EACEV,QAAQ,CAACM,SAAS,CAACU,MAAM,GAAG,UAAUP,OAAO,EAAEC,GAAG,EAAE;IAClD,IAAIO,WAAW,GAAG,IAAI,CAACC,YAAY;IACnCD,WAAW,IAAIA,WAAW,CAACD,MAAM,CAACN,GAAG,CAAC;EACxC,CAAC;EACD;AACF;AACA;EACEV,QAAQ,CAACM,SAAS,CAACa,OAAO,GAAG,UAAUV,OAAO,EAAEC,GAAG,EAAE;IACnD,IAAI,CAACU,mBAAmB,CAACV,GAAG,CAAC;IAC7BT,MAAM,CAACK,SAAS,CAACa,OAAO,CAAChB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACjD,CAAC;EACDJ,QAAQ,CAACM,SAAS,CAACQ,yBAAyB,GAAG,UAAUN,SAAS,EAAEE,GAAG,EAAEW,WAAW,EAAE;IACpF,IAAIC,KAAK,GAAGtB,QAAQ,CAACuB,mBAAmB,CAAC,IAAI,CAACX,gBAAgB,CAAC;IAC/D,IAAI,CAACU,KAAK,EAAE;MACV;IACF;IACA,IAAIE,gBAAgB,GAAG3B,sBAAsB,CAAC4B,mBAAmB,CAACjB,SAAS,CAAC;IAC5EgB,gBAAgB,GAAG,CAAC,IAAI,CAACN,YAAY,KAAK,IAAI,CAACA,YAAY,GAAG,IAAII,KAAK,CAAC,CAAC,CAAC,EAAEf,MAAM,CAACC,SAAS,EAAEgB,gBAAgB,EAAEd,GAAG,EAAEW,WAAW,CAAC,GAAG,IAAI,CAACD,mBAAmB,CAACV,GAAG,CAAC;EACnK,CAAC;EACDV,QAAQ,CAACM,SAAS,CAACc,mBAAmB,GAAG,UAAUV,GAAG,EAAE;IACtD,IAAI,CAACQ,YAAY,IAAI,IAAI,CAACA,YAAY,CAACC,OAAO,CAACT,GAAG,CAAC;IACnD,IAAI,CAACQ,YAAY,GAAG,IAAI;EAC1B,CAAC;EACDlB,QAAQ,CAAC0B,wBAAwB,GAAG,UAAUrB,IAAI,EAAEsB,KAAK,EAAE;IACzD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI/B,gBAAgB,CAACM,IAAI,CAAC,EAAE;QAC1B,MAAM,IAAI0B,KAAK,CAAC,cAAc,GAAG1B,IAAI,GAAG,SAAS,CAAC;MACpD;IACF;IACAN,gBAAgB,CAACM,IAAI,CAAC,GAAGsB,KAAK;EAChC,CAAC;EACD;EACA3B,QAAQ,CAACuB,mBAAmB,GAAG,UAAUlB,IAAI,EAAE;IAC7C,OAAOA,IAAI,IAAIN,gBAAgB,CAACM,IAAI,CAAC;EACvC,CAAC;EACD;EACAL,QAAQ,CAACK,IAAI,GAAG,MAAM;EACtB,OAAOL,QAAQ;AACjB,CAAC,CAACF,aAAa,CAAC;AAChB,eAAeE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}