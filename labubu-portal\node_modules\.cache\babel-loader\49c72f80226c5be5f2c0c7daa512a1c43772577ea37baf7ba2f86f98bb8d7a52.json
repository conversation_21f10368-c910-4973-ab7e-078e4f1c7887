{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport './assets/tailwind.css';\ncreateApp(App).mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "mount"], "sources": ["C:/suibianwanwan/labubu-portal/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\nimport './assets/tailwind.css'\n\ncreateApp(App).mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAO,uBAAuB;AAE9BD,SAAS,CAACC,GAAG,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}