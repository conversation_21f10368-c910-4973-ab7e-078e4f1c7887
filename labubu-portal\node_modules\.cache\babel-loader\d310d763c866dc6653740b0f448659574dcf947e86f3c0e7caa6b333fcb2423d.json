{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BrushController from '../helper/BrushController.js';\nimport { layoutCovers } from './visualEncoding.js';\nimport ComponentView from '../../view/Component.js';\nvar BrushView = /** @class */function (_super) {\n  __extends(BrushView, _super);\n  function BrushView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BrushView.type;\n    return _this;\n  }\n  BrushView.prototype.init = function (ecModel, api) {\n    this.ecModel = ecModel;\n    this.api = api;\n    this.model;\n    (this._brushController = new BrushController(api.getZr())).on('brush', zrUtil.bind(this._onBrush, this)).mount();\n  };\n  BrushView.prototype.render = function (brushModel, ecModel, api, payload) {\n    this.model = brushModel;\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateTransform = function (brushModel, ecModel, api, payload) {\n    // PENDING: `updateTransform` is a little tricky, whose layout need\n    // to be calculate mandatorily and other stages will not be performed.\n    // Take care the correctness of the logic. See #11754 .\n    layoutCovers(ecModel);\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateVisual = function (brushModel, ecModel, api, payload) {\n    this.updateTransform(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateView = function (brushModel, ecModel, api, payload) {\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype._updateController = function (brushModel, ecModel, api, payload) {\n    // Do not update controller when drawing.\n    (!payload || payload.$from !== brushModel.id) && this._brushController.setPanels(brushModel.brushTargetManager.makePanelOpts(api)).enableBrush(brushModel.brushOption).updateCovers(brushModel.areas.slice());\n  };\n  // updateLayout: updateController,\n  // updateVisual: updateController,\n  BrushView.prototype.dispose = function () {\n    this._brushController.dispose();\n  };\n  BrushView.prototype._onBrush = function (eventParam) {\n    var modelId = this.model.id;\n    var areas = this.model.brushTargetManager.setOutputRanges(eventParam.areas, this.ecModel);\n    // Action is not dispatched on drag end, because the drag end\n    // emits the same params with the last drag move event, and\n    // may have some delay when using touch pad, which makes\n    // animation not smooth (when using debounce).\n    (!eventParam.isEnd || eventParam.removeOnClick) && this.api.dispatchAction({\n      type: 'brush',\n      brushId: modelId,\n      areas: zrUtil.clone(areas),\n      $from: modelId\n    });\n    eventParam.isEnd && this.api.dispatchAction({\n      type: 'brushEnd',\n      brushId: modelId,\n      areas: zrUtil.clone(areas),\n      $from: modelId\n    });\n  };\n  BrushView.type = 'brush';\n  return BrushView;\n}(ComponentView);\nexport default BrushView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "BrushController", "layoutCovers", "ComponentView", "BrushView", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "ecModel", "api", "model", "_brushController", "getZr", "on", "bind", "_onBrush", "mount", "render", "brushModel", "payload", "_updateController", "updateTransform", "updateVisual", "updateView", "$from", "id", "set<PERSON><PERSON><PERSON>", "brushTargetManager", "makePanelOpts", "enableBrush", "brushOption", "updateCovers", "areas", "slice", "dispose", "eventParam", "modelId", "setOutputRanges", "isEnd", "removeOnClick", "dispatchAction", "brushId", "clone"], "sources": ["C:/suibianwanwan/labubu-portal/node_modules/echarts/lib/component/brush/BrushView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BrushController from '../helper/BrushController.js';\nimport { layoutCovers } from './visualEncoding.js';\nimport ComponentView from '../../view/Component.js';\nvar BrushView = /** @class */function (_super) {\n  __extends(BrushView, _super);\n  function BrushView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BrushView.type;\n    return _this;\n  }\n  BrushView.prototype.init = function (ecModel, api) {\n    this.ecModel = ecModel;\n    this.api = api;\n    this.model;\n    (this._brushController = new BrushController(api.getZr())).on('brush', zrUtil.bind(this._onBrush, this)).mount();\n  };\n  BrushView.prototype.render = function (brushModel, ecModel, api, payload) {\n    this.model = brushModel;\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateTransform = function (brushModel, ecModel, api, payload) {\n    // PENDING: `updateTransform` is a little tricky, whose layout need\n    // to be calculate mandatorily and other stages will not be performed.\n    // Take care the correctness of the logic. See #11754 .\n    layoutCovers(ecModel);\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateVisual = function (brushModel, ecModel, api, payload) {\n    this.updateTransform(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype.updateView = function (brushModel, ecModel, api, payload) {\n    this._updateController(brushModel, ecModel, api, payload);\n  };\n  BrushView.prototype._updateController = function (brushModel, ecModel, api, payload) {\n    // Do not update controller when drawing.\n    (!payload || payload.$from !== brushModel.id) && this._brushController.setPanels(brushModel.brushTargetManager.makePanelOpts(api)).enableBrush(brushModel.brushOption).updateCovers(brushModel.areas.slice());\n  };\n  // updateLayout: updateController,\n  // updateVisual: updateController,\n  BrushView.prototype.dispose = function () {\n    this._brushController.dispose();\n  };\n  BrushView.prototype._onBrush = function (eventParam) {\n    var modelId = this.model.id;\n    var areas = this.model.brushTargetManager.setOutputRanges(eventParam.areas, this.ecModel);\n    // Action is not dispatched on drag end, because the drag end\n    // emits the same params with the last drag move event, and\n    // may have some delay when using touch pad, which makes\n    // animation not smooth (when using debounce).\n    (!eventParam.isEnd || eventParam.removeOnClick) && this.api.dispatchAction({\n      type: 'brush',\n      brushId: modelId,\n      areas: zrUtil.clone(areas),\n      $from: modelId\n    });\n    eventParam.isEnd && this.api.dispatchAction({\n      type: 'brushEnd',\n      brushId: modelId,\n      areas: zrUtil.clone(areas),\n      $from: modelId\n    });\n  };\n  BrushView.type = 'brush';\n  return BrushView;\n}(ComponentView);\nexport default BrushView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,IAAIC,SAAS,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC7CN,SAAS,CAACK,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAAA,EAAG;IACnB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,SAAS,CAACK,IAAI;IAC3B,OAAOH,KAAK;EACd;EACAF,SAAS,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACjD,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,KAAK;IACV,CAAC,IAAI,CAACC,gBAAgB,GAAG,IAAId,eAAe,CAACY,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,OAAO,EAAEjB,MAAM,CAACkB,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAClH,CAAC;EACDhB,SAAS,CAACM,SAAS,CAACW,MAAM,GAAG,UAAUC,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,EAAE;IACxE,IAAI,CAACT,KAAK,GAAGQ,UAAU;IACvB,IAAI,CAACE,iBAAiB,CAACF,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,CAAC;EAC3D,CAAC;EACDnB,SAAS,CAACM,SAAS,CAACe,eAAe,GAAG,UAAUH,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,EAAE;IACjF;IACA;IACA;IACArB,YAAY,CAACU,OAAO,CAAC;IACrB,IAAI,CAACY,iBAAiB,CAACF,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,CAAC;EAC3D,CAAC;EACDnB,SAAS,CAACM,SAAS,CAACgB,YAAY,GAAG,UAAUJ,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,EAAE;IAC9E,IAAI,CAACE,eAAe,CAACH,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,CAAC;EACzD,CAAC;EACDnB,SAAS,CAACM,SAAS,CAACiB,UAAU,GAAG,UAAUL,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,EAAE;IAC5E,IAAI,CAACC,iBAAiB,CAACF,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,CAAC;EAC3D,CAAC;EACDnB,SAAS,CAACM,SAAS,CAACc,iBAAiB,GAAG,UAAUF,UAAU,EAAEV,OAAO,EAAEC,GAAG,EAAEU,OAAO,EAAE;IACnF;IACA,CAAC,CAACA,OAAO,IAAIA,OAAO,CAACK,KAAK,KAAKN,UAAU,CAACO,EAAE,KAAK,IAAI,CAACd,gBAAgB,CAACe,SAAS,CAACR,UAAU,CAACS,kBAAkB,CAACC,aAAa,CAACnB,GAAG,CAAC,CAAC,CAACoB,WAAW,CAACX,UAAU,CAACY,WAAW,CAAC,CAACC,YAAY,CAACb,UAAU,CAACc,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;EAC/M,CAAC;EACD;EACA;EACAjC,SAAS,CAACM,SAAS,CAAC4B,OAAO,GAAG,YAAY;IACxC,IAAI,CAACvB,gBAAgB,CAACuB,OAAO,CAAC,CAAC;EACjC,CAAC;EACDlC,SAAS,CAACM,SAAS,CAACS,QAAQ,GAAG,UAAUoB,UAAU,EAAE;IACnD,IAAIC,OAAO,GAAG,IAAI,CAAC1B,KAAK,CAACe,EAAE;IAC3B,IAAIO,KAAK,GAAG,IAAI,CAACtB,KAAK,CAACiB,kBAAkB,CAACU,eAAe,CAACF,UAAU,CAACH,KAAK,EAAE,IAAI,CAACxB,OAAO,CAAC;IACzF;IACA;IACA;IACA;IACA,CAAC,CAAC2B,UAAU,CAACG,KAAK,IAAIH,UAAU,CAACI,aAAa,KAAK,IAAI,CAAC9B,GAAG,CAAC+B,cAAc,CAAC;MACzEnC,IAAI,EAAE,OAAO;MACboC,OAAO,EAAEL,OAAO;MAChBJ,KAAK,EAAEpC,MAAM,CAAC8C,KAAK,CAACV,KAAK,CAAC;MAC1BR,KAAK,EAAEY;IACT,CAAC,CAAC;IACFD,UAAU,CAACG,KAAK,IAAI,IAAI,CAAC7B,GAAG,CAAC+B,cAAc,CAAC;MAC1CnC,IAAI,EAAE,UAAU;MAChBoC,OAAO,EAAEL,OAAO;MAChBJ,KAAK,EAAEpC,MAAM,CAAC8C,KAAK,CAACV,KAAK,CAAC;MAC1BR,KAAK,EAAEY;IACT,CAAC,CAAC;EACJ,CAAC;EACDpC,SAAS,CAACK,IAAI,GAAG,OAAO;EACxB,OAAOL,SAAS;AAClB,CAAC,CAACD,aAAa,CAAC;AAChB,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}